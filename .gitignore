
# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli
expo-env.d.ts
# @end expo-cli

# OSX
.DS_Store

# Xcode
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
build/
.idea
.gradle
local.properties
android/sentry.properties # Ignore Sentry properties file (contains auth token)
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
!android/app/keystore/.gitkeep
android/app/keystore/release.keystore

# node.js
node_modules/
npm-debug.log
yarn-error.log

# fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Expo
.expo/
web-build/
dist/

# Local Netlify folder
.netlify

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# TypeScript
*.tsbuildinfo

# Testing
coverage/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# SQLite database files (except seed data)
*.db
*.sqlite
*.sqlite3
!docs/odia_calendar_data.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Cache
.eslintcache
.stylelintcache

# macOS
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.lnk

.env.local

# Supabase secrets file
src/config/secrets.ts

# Backup files and releases
backup/
