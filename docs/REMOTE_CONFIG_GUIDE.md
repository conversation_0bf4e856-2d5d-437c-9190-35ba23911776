# Remote Configuration Guide

This document explains how to set up and manage the remote configuration for the Odia Calendar Lite app.

## Overview

The Odia Calendar Lite app uses a remote configuration system to manage certain app settings, including the Supabase connection details. This allows you to change these settings without requiring an app update.

## How It Works

1. The app has default configuration values bundled with it
2. On startup, the app attempts to fetch the latest configuration from a remote endpoint
3. If successful, the app uses the remote configuration
4. If unsuccessful, the app falls back to the bundled configuration
5. The configuration is cached locally with a time-to-live (TTL)

## Setting Up the Remote Configuration

### 1. Host the Configuration File

The configuration file is a simple JSON file that needs to be hosted at a stable URL. You can host it on:

- GitHub Pages
- AWS S3
- Your own web server
- Any static file hosting service

### 2. Configuration File Format

The configuration file should follow this format:

```json
{
  "version": 1,
  "supabaseUrl": "https://your-project-id.supabase.co",
  "supabaseAnonKey": "your-anon-key",
  "lastUpdated": "2024-07-01T00:00:00Z"
}
```

### 3. Update the App Configuration

In the app code, update the `REMOTE_CONFIG_URL` constant in `src/services/RemoteConfigService.ts` to point to your hosted configuration file:

```typescript
const REMOTE_CONFIG_URL = 'https://your-domain.com/odia-calendar-config.json';
```

## Migrating from Supabase Cloud to Self-Hosted

When migrating from Supabase Cloud to a self-hosted instance, follow these steps:

1. Set up your self-hosted Supabase instance
2. Update the remote configuration file with the new Supabase URL and anon key
3. Host the updated configuration file at the same URL
4. The app will automatically pick up the new configuration on the next refresh

## Testing the Remote Configuration

To test that the remote configuration is working correctly:

1. Make a small change to the remote configuration file
2. Force a refresh in the app by calling `remoteConfig.forceRefresh()`
3. Verify that the app is using the updated configuration

## Troubleshooting

If the app is not picking up the remote configuration:

1. Check that the `REMOTE_CONFIG_URL` is correct
2. Verify that the configuration file is accessible from the device
3. Check the app logs for any errors related to fetching the configuration
4. Try forcing a refresh with `remoteConfig.forceRefresh()`

## Security Considerations

- The remote configuration file should be hosted on a secure (HTTPS) endpoint
- Sensitive information should not be included in the configuration file
- Consider implementing authentication for the configuration endpoint in production

## Fallback Mechanism

If the remote configuration cannot be fetched, the app will use the bundled configuration. This ensures that the app will continue to function even if the remote configuration is unavailable.

The bundled configuration is defined in the `DEFAULT_CONFIG` constant in `src/services/RemoteConfigService.ts`.
