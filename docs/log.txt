Android Bundled 1806ms src/index.js (3729 modules)
 (NOBRIDGE) LOG  Bridgeless mode is enabled
 INFO  
 💡 JavaScript logs will be removed from Metro in React Native 0.77! Please use React Native DevTools as your default tool. Tip: Type j in the terminal to open (requires Google Chrome or Microsoft Edge).
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Accessing client before initialization. Initializing now... {"app": "OdiaCalendarLite", "platform": "android", "property": "$$typeof", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Supabase: Starting initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Fast initializing with cache or defaults {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Fast initializing with cache or defaults... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] RemoteConfig: Attempting to load from cache {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] RemoteConfig: No cached config found {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: No cache found, using defaults {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Fast initialization complete {"app": "OdiaCalendarLite", "platform": "android", "source": "cache", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Got configuration from cache/defaults {"app": "OdiaCalendarLite", "hasAnonKey": true, "platform": "android", "urlPrefix": "https://kmiyieb...", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Creating client with configuration {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Supabase: Client initialized successfully with configuration from cache {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Triggering background refresh of configuration {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Starting background refresh... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Background refresh not needed (recently checked) {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Remote config checked, no updates needed {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Logging system initialized {"app": "OdiaCalendarLite", "environment": "development", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] App started {"app": "OdiaCalendarLite", "colorScheme": "light", "component": "RootLayout", "environment": "development", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: Initialize: 3485.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Color scheme changed {"app": "OdiaCalendarLite", "colorScheme": "light", "component": "RootLayout", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: Store Hydrate: 19.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: First Render: 485.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Initializing data coordinator... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Starting database service initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Starting database initialization process {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Database directory path {"app": "OdiaCalendarLite", "path": "file:///data/user/0/com.kalingatech.odia.simplecalendar/files/SQLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Database file path {"app": "OdiaCalendarLite", "path": "file:///data/user/0/com.kalingatech.odia.simplecalendar/files/SQLite/odia_calendar_data.db", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Waiting for database initialization to complete {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Checking status for year 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Checking year display info for 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Starting database initialization {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Calling dataCoordinator.initialize() {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Fonts loaded, hiding splash screen {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: Fonts Load: 832.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Attempting to hide splash screen... {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Using SplashScreen.hideAsync method {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Checking status for year 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Checking year display info for 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] SplashScreen.hideAsync completed successfully {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Splash screen hiding process completed (success or failure) {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: Assets Load: 589.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: First Interaction: 1054.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Device is online with internet access {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Device is online with internet access {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Device is online with internet access {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Device is online with internet access {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Database file already exists {"app": "OdiaCalendarLite", "path": "file:///data/user/0/com.kalingatech.odia.simplecalendar/files/SQLite/odia_calendar_data.db", "platform": "android", "size": 225280, "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Opening database... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Trying to open database with openDatabaseAsync... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Database opened successfully with openDatabaseAsync {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] WAL mode enabled {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Ensured calendar_year_versions table exists {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Migration] Checking database schema version... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Migration] Current DB version: 1 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Migration] Database schema is up-to-date. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Verifying database tables using getAllAsync... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Raw result from sqlite_master query (getAllAsync) {"app": "OdiaCalendarLite", "platform": "android", "tables": "[
  {
    \"name\": \"calendar_year_versions\"
  },
  {
    \"name\": \"app_metadata\"
  },
  {
    \"name\": \"panchang_data\"
  },
  {
    \"name\": \"user_reminders\"
  },
  {
    \"name\": \"sqlite_sequence\"
  },
  {
    \"name\": \"scheduled_notifications\"
  }
]", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Database tables {"app": "OdiaCalendarLite", "platform": "android", "tables": [{"name": "calendar_year_versions"}, {"name": "app_metadata"}, {"name": "panchang_data"}, {"name": "user_reminders"}, {"name": "sqlite_sequence"}, {"name": "scheduled_notifications"}], "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Test query result {"app": "OdiaCalendarLite", "platform": "android", "result": [{"count": 365}], "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Database initialized successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Database initialization completed successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Database service initialized successfully. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Starting sync service initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Initializing sync service and local version tracking... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Database already initialized, skipping initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Starting robust year version initialization... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Found 365 records in panchang_data. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Distinct years in panchang_data: 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Years already in versions table: 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Service] All years from panchang_data are already present in versions table. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Year version initialization check complete. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sync service initialization complete. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Sync service initialized successfully. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Initialization complete. Data coordinator is ready. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Scheduling background sync for 5 seconds from now {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Database initialization completed successfully {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Initializing reminder service {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Initializing reminder service... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Initializing notification service... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching data for 2025-5 from database {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Year 2025 found locally with version 2 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Status for year 2025: AVAILABLE_LOCALLY {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Year 2025 found locally with version 2 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Status for year 2025: AVAILABLE_LOCALLY {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-4 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-04-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-04-31'
        ", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-6 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-06-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-06-31'
        ", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [], "month": 4, "platform": "android", "scheduledCount": 0, "version": "1.0.0", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Prefetched data for 2025-4 {"app": "OdiaCalendarLite", "calendarDays": 30, "platform": "android", "reminderDays": 0, "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [23], "month": 6, "platform": "android", "scheduledCount": 1, "version": "1.0.0", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Prefetched data for 2025-6 {"app": "OdiaCalendarLite", "calendarDays": 30, "platform": "android", "reminderDays": 1, "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] AdMob SDK initialized successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Retrieved stored notification permission status {"app": "OdiaCalendarLite", "platform": "android", "status": {"granted": true, "status": {"android": [Object], "canAskAgain": true, "expires": "never", "granted": true, "status": "granted"}}, "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Connected notification service to reminder service for next occurrence scheduling {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Starting smart notification scheduling check {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Starting notification sync detection {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] [Review Service] Skipping check - checked recently {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [Review Service] Auto-review check: {"app": "OdiaCalendarLite", "currentVersion": "1.0.0", "lastRequestTime": "2025-05-25T10:02:41.505Z", "lastRequestVersion": "1.0.0", "opens": 22, "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [Review Service] Auto-review conditions not met {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Ad frequency service initialized {"adsShownCount": 1, "app": "OdiaCalendarLite", "lastAdShown": "2025-05-25T17:55:17.742Z", "placementCounters": {"appSession": 0, "dateDetails": 0, "reminderCreation": 0, "yearChange": 0}, "platform": "android", "sessionStart": "2025-05-25T17:52:18.710Z", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Retrieved OS scheduled notifications {"app": "OdiaCalendarLite", "count": 4, "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] OS notifications found {"app": "OdiaCalendarLite", "count": 4, "ids": ["9f9bee80-5ce9-43da-b367-1acb9cbb3a48", "ce6ea65a-f44f-4a4e-bccd-22787588e342", "7c910d40-6502-471d-af40-4e3b330c8a1b", "3e124b4c-704d-4824-bdab-5cd1ba97b123"], "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT notification_id as notificationId, scheduled_date as scheduledDate
        FROM scheduled_notifications
        WHERE status = 'scheduled'
        AND scheduled_date > '2025-05-25T18:14:19.861Z'
      ", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Database notifications found {"app": "OdiaCalendarLite", "count": 4, "ids": ["9f9bee80-5ce9-43da-b367-1acb9cbb3a48", "ce6ea65a-f44f-4a4e-bccd-22787588e342", "7c910d40-6502-471d-af40-4e3b330c8a1b", "3e124b4c-704d-4824-bdab-5cd1ba97b123"], "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Notification sync analysis {"app": "OdiaCalendarLite", "dbCount": 4, "missingInOs": 0, "needsReschedule": false, "orphanedInOs": 0, "osCount": 4, "platform": "android", "totalMismatches": 0, "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] All notifications are properly scheduled - skipping reschedule {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Reminder service initialized successfully {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Checking for pending notification navigation {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Pending navigation check completed {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Database initialization process finished (success or failure) {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-5 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-05-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-05-31'
        ", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight reminder request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Triggering background sync... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Running background sync (forceCheck: false)... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for yearChange {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for appSession {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for dateDetails {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for reminderCreation {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [], "month": 5, "platform": "android", "scheduledCount": 0, "version": "1.0.0", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Acquired update lock {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Skipping sync check - checked recently. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Released update lock {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Background sync completed successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled,
          created_at as createdAt, updated_at as updatedAt
        FROM user_reminders
        ORDER BY created_at DESC
      ", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT
            sn.id,
            sn.reminder_id AS reminderId,
            sn.notification_id AS notificationId,
            sn.scheduled_date AS scheduledDate,
            sn.status,
            pd.odia_date AS odiaDate,
            pd.odia_month AS odiaMonth,
            pd.paksha,
            pd.tithi_name AS tithi
          FROM scheduled_notifications sn
          JOIN panchang_data pd ON DATE(sn.scheduled_date) = pd.eng_date
          WHERE sn.reminder_id = 3
          AND sn.status = 'scheduled'
          AND DATE(sn.scheduled_date) >= '2025-05-25'
          ORDER BY sn.scheduled_date ASC
          LIMIT 1
        ", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Found next scheduled notification {"app": "OdiaCalendarLite", "notificationId": "9f9bee80-5ce9-43da-b367-1acb9cbb3a48", "platform": "android", "reminderId": 3, "scheduledDate": "2025-06-22T18:30:00.000Z", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for settingsScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for settingsScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for settingsScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Showing interstitial ad for appSession {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) DEBUG  [DEBUG] Recorded ad shown {"adsShownInSession": 2, "app": "OdiaCalendarLite", "lastShownAt": "2025-05-25T18:17:20.112Z", "placement": "appSession", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for settingsScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.0"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for settingsScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.0"}