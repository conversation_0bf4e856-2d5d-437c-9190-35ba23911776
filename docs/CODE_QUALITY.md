# Code Quality Guidelines

This document outlines the code quality standards and type safety improvements implemented in the Odia Calendar Lite application.

## Table of Contents

1. [TypeScript Type System](#typescript-type-system)
2. [Naming Conventions](#naming-conventions)
3. [Documentation Standards](#documentation-standards)
4. [Code Organization](#code-organization)
5. [Logging and Error Handling](#logging-and-error-handling)
6. [Future Considerations](#future-considerations)

## TypeScript Type System

### Type Definitions

We've implemented a comprehensive type system to ensure type safety throughout the application. All types are centralized in the `/src/types` directory and organized by domain:

- `calendar.ts`: Types related to calendar data and operations
- `settings.ts`: Types related to application settings
- `store.ts`: Types related to state management
- `index.ts`: Re-exports all types and provides utility type helpers

### Benefits of Strong Typing

- **Error Prevention**: Catches type-related errors at compile time
- **Better IDE Support**: Provides autocomplete and inline documentation
- **Self-Documenting Code**: Types serve as documentation for data structures
- **Refactoring Confidence**: Makes large-scale refactoring safer

### Type Safety Examples

```typescript
// Before
const handleDatePress = (day: number | null) => {
  // Implementation
};

// After
const handleDatePress = useCallback((day: number | null): void => {
  // Implementation with proper return type
}, [dependencies]);
```

## Performance Optimizations

We've implemented several performance optimizations to ensure the app runs smoothly:

### Component Memoization

- Used `React.memo()` for pure components to prevent unnecessary re-renders
- Memoized list items in FlatList components
- Implemented proper dependency arrays for all memoized components

### Function Memoization

- Used `useCallback()` for event handlers to maintain referential equality
- Ensured all callback dependencies are correctly specified
- Memoized expensive calculations with `useMemo()`

### List Rendering Optimization

- Optimized FlatList components with proper performance props:
  - `initialNumToRender`: Limited initial render batch size
  - `maxToRenderPerBatch`: Controlled batch rendering size
  - `windowSize`: Optimized render window
  - `removeClippedSubviews`: Improved memory usage for off-screen items

### Render Cycle Optimization

- Memoized date check functions in calendar components
- Optimized conditional rendering with proper dependency tracking
- Prevented unnecessary re-renders in modal components

## Naming Conventions

We follow consistent naming conventions throughout the codebase:

### Files and Directories

- **Directories**: Use kebab-case for directories (e.g., `date-utils`)
- **Component Files**: Use PascalCase for React components (e.g., `CalendarGrid.tsx`)
- **Utility Files**: Use kebab-case for utility files (e.g., `date-utils.ts`)
- **Type Files**: Use kebab-case for type files (e.g., `calendar.ts`)

### Variables and Functions

- **Variables**: Use camelCase for variables (e.g., `selectedDate`)
- **Functions**: Use camelCase for functions (e.g., `formatDateString`)
- **React Components**: Use PascalCase for React components (e.g., `CalendarGrid`)
- **React Hooks**: Use camelCase with 'use' prefix (e.g., `useCalendarStore`)
- **Constants**: Use UPPER_SNAKE_CASE for constants (e.g., `DEFAULT_SETTINGS`)

### Types and Interfaces

- **Interfaces**: Use PascalCase with descriptive names (e.g., `CalendarState`)
- **Types**: Use PascalCase for type aliases (e.g., `CalendarGrid`)
- **Enums**: Use PascalCase for enum names (e.g., `ThemeMode`)

## Documentation Standards

We use JSDoc comments to document our code:

### Function Documentation

```typescript
/**
 * Format date as YYYY-MM-DD
 * @param date The date to format
 * @returns The formatted date string
 */
export const formatDateString = (date: Date): string => {
  // Implementation
};
```

### Interface Documentation

```typescript
/**
 * Represents a festival in the Odia calendar
 */
export interface FestivalInfo {
  /** The date of the festival in YYYY-MM-DD format */
  date: string;

  /** The name of the festival */
  name: string;

  /** Description of the festival */
  description: string;
}
```

### File Documentation

```typescript
/**
 * Utility functions for date operations
 */
```

## Code Organization

### Project Structure

```
/src                 # Source code directory
  /components        # UI components
    /calendar        # Calendar-related components
    /common          # Reusable UI components
  /store             # State management with Zustand
  /types             # TypeScript type definitions
  /utils             # Utility functions
  /constants         # App constants and translations
  /mocks             # Mock data (to be replaced with database)
  /index.js          # MAIN ENTRY POINT - specified in package.json
/app                 # Expo Router screens and navigation (file-based routing)
  /_layout.tsx       # ROOT LAYOUT - wraps the entire application
  /error-boundary.tsx # Global error boundary component
  /index.tsx         # Root redirect to tabs
  /+not-found.tsx    # 404 page (special naming convention for Expo Router)
  /modal.tsx         # Modal screen
  /(tabs)            # Tab navigation group (parentheses define a group in Expo Router)
    /_layout.tsx     # TABS LAYOUT - defines tab navigation structure
    /index.tsx       # Calendar screen (main calendar view)
    /settings.tsx    # Settings screen
/assets              # Static assets
  /images            # Image assets
```

### Component Structure

Components follow a consistent structure:

1. Imports
2. Type definitions (props interface)
3. Component implementation
4. Styles
5. Export

Example:
```typescript
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface ComponentProps {
  // Props definition
}

const Component: React.FC<ComponentProps> = ({ /* props */ }) => {
  // Implementation
  return (
    // JSX
  );
};

const styles = StyleSheet.create({
  // Styles
});

export default Component;
```

## Logging and Error Handling

We've implemented a robust logging and error handling system to ensure the app is stable and provides useful debugging information.

### Logging System

Our logging system is designed to be efficient and have minimal impact on app performance, especially in production builds.

#### Key Features

- **Environment-Aware Behavior**: Different logging behaviors based on environment
  - In development: Logs to console, stores in memory for viewing
  - In production: Only warnings and errors are processed, sent to Sentry
- **Component-Specific Logging**: Logs include component context for better debugging
- **Log Levels**: Different log levels (DEBUG, INFO, WARN, ERROR) for better filtering
- **Sentry Integration**: Remote error tracking in production
- **Development Tools**: In-app log viewer for development

#### Implementation

```typescript
// Component-specific logging
const logger = useComponentLogger('ComponentName');
logger.info('Component initialized');
logger.error('Error occurred', { error });

// Global logging
import { logger } from '@/utils/logging-sentry';
logger.warn('Warning message', { context });
```

### Error Handling

Our error handling system is designed to catch and properly handle errors without crashing the app, while providing useful information for debugging.

#### Key Features

- **Error Boundaries**: React error boundaries to catch and handle component errors
- **Platform-Specific Handling**: Different error handling for web and native environments
- **Graceful Degradation**: App continues to function even when parts fail
- **User-Friendly Messages**: Clear error messages for users
- **Remote Monitoring**: Integration with Sentry for error tracking

#### Implementation

```typescript
// Error boundary usage
<ErrorBoundary>
  <ComponentThatMightError />
</ErrorBoundary>

// Try-catch pattern
try {
  // Code that might throw
} catch (error) {
  logger.error('Operation failed', { error });
  // Graceful fallback
}
```

### Best Practices

1. **Never use console.log directly**: Always use the logger
2. **Always handle promises**: Use try/catch with async/await or .catch() with promises
3. **Use error boundaries**: Wrap components that might throw errors
4. **Provide context**: Include relevant context with error logs
5. **Graceful degradation**: Always provide fallbacks for error states

## Future Considerations

As we continue to develop the application, we'll maintain and extend these code quality standards:

### Database Integration

When implementing the database layer:

- Create comprehensive types for database models
- Implement proper error handling with typed errors
- Use repository pattern for data access

### API Integration

When implementing API integration:

- Define request and response types
- Implement proper error handling
- Use typed fetch wrappers

### Testing

When implementing tests:

- Use TypeScript for test files
- Define proper test utility types
- Ensure type coverage in tests
- Add tests for error handling scenarios
- Mock logging system in tests

### Logging and Error Handling

As the app evolves:

- Add performance metrics logging
- Implement crash analytics
- Add user feedback mechanisms for errors
- Consider implementing log rotation for persistent logs
- Add more detailed error reporting for critical features
