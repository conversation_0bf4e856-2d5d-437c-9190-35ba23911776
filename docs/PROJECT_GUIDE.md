# Odia Calendar Lite - Project Guide

This document serves as a comprehensive guide to the Odia Calendar Lite project, providing a single source of truth for development. It outlines the current state of the project, implemented features, pending tasks, and architectural decisions.

## Project Overview

Odia Calendar Lite is a mobile application that provides Odia calendar information, including:
- Calendar view with Odia dates
- Festival information
- Marriage dates
- Marriage/Brata Ghara dates
- Moon phases (Purnima/Amavasya)
- Detailed date information with Na<PERSON><PERSON><PERSON>, Yoga, Karana, etc.
- Year availability checking and downloading
- Tithi-based reminders (monthly and yearly)

The app is built using:
- React Native with Expo
- TypeScript
- Zustand for state management
- SQLite for local data storage
- Supabase for remote data synchronization
- Sentry for error tracking and logging
- AdMob for monetization

## Current Implementation Status

### Completed Features

1. **UI Components**:
   - ✅ Calendar grid with date cells
   - ✅ Month/year navigation
   - ✅ Date details modal
   - ✅ Festival list
   - ✅ Marriage dates list
   - ✅ Theme switching (light/dark)
   - ✅ Language switching (English/Odia)
   - ✅ Moon phase indicators
   - ✅ In-App Review Prompt (manual trigger via Settings, using `expo-store-review`)
   - ✅ Automatic In-App Review Prompt (triggered by usage, using `expo-store-review`)
   - ✅ Reminder management (list, add/edit, details screens)
   - ✅ Tithi picker for selecting Odia months, pakshas, and tithis

2. **State Management**:
   - ✅ Settings store (theme, language)
   - ✅ Calendar store (selected date, month, year)
   - ✅ Reminder store (reminders, notifications)
   - ✅ Persistence using AsyncStorage
   - ✅ Comprehensive TypeScript interfaces

3. **Performance Optimizations**:
   - ✅ Component memoization with React.memo()
   - ✅ Event handler optimization with useCallback()
   - ✅ Expensive calculations optimization with useMemo()
   - ✅ FlatList rendering optimization
   - ✅ Proper dependency specification in hooks

4. **Code Quality**:
   - ✅ Comprehensive TypeScript interfaces
   - ✅ Consistent naming conventions
   - ✅ JSDoc documentation
   - ✅ Centralized types directory

5. **Logging and Error Handling**:
   - ✅ Streamlined logging system with Sentry integration
   - ✅ Environment-aware logging (development vs. production)
   - ✅ Component-specific logging with context
   - ✅ Comprehensive error boundaries
   - ✅ Platform-specific error handling
   - ✅ Development-only log viewer
   - ✅ User-controlled error reporting toggle

6. **Performance Measurement**:
   - ✅ Lightweight performance metrics store
   - ✅ App startup time measurement
   - ✅ Component render time measurement
   - ✅ Screen render time measurement
   - ✅ Data fetch time measurement
   - ✅ Interaction time measurement
   - ✅ Development-only performance dashboard

7. **Data Synchronization & Settings**:
   - ✅ User settings for enabling/disabling automatic background updates (`autoUpdateEnabled`).
   - ✅ User setting to restrict automatic updates to Wi-Fi only (`autoUpdateWifiOnly`).
   - ✅ Manual "Check for Updates" button in Settings screen with loading state and status feedback.
   - ✅ Display of "Last checked" timestamp in Settings screen.
   - ✅ Background sync logic respects user settings (`sync-service.ts`).
   - ✅ Update prompt modal (`UpdatePromptModal.tsx`) shown when updates are available but auto-update is disabled.
   - ✅ Manual update trigger function (`syncService.triggerManualUpdate`) callable from UI.
   - ✅ "Delete Year Data" feature for managing storage space with protection for current year data.

### Pending Features

1. **Database Integration**: ✅ COMPLETED
   - ✅ SQLite database initialization
   - ✅ Database provider
   - ✅ Calendar data service
   - ✅ Calendar update service (via `sync-service.ts`)
   - ✅ Schema migration framework using `PRAGMA user_version`
   - ✅ Year version tracking and management

2. **Data Synchronization**: ✅ COMPLETED
   - ✅ Background update functionality (via `sync-service.ts`)
   - ✅ Year data download mechanism (via `sync-service.ts`)
   - ✅ Version tracking (local via `database-service.ts`, sync via `sync-service.ts`)
   - ✅ User consent settings integrated into sync logic
   - ✅ Wi-Fi only update option
   - ✅ Manual update trigger with status feedback
   - ✅ Update prompt modal for pending updates

3. **UI Enhancements**: ⚠️ PARTIALLY COMPLETED
   - ✅ Brata Ghara dates indicator added to Date Details Modal
   - ❌ Brata Ghara dates indicator on main Calendar Grid
   - ✅ Date Details Modal UI significantly enhanced with more data and better structure
   - ✅ Combined Odia date and Panchang sections into one logical block
   - ✅ Reorganized content in a more logical sequence
   - ✅ Moved the banner ad below the combined section
   - ✅ Added marriage/brataghara eligibility information to the main section
   - ✅ Ensured proper display of all text in Odia when Odia language is selected
   - ✅ Added borders to festival, marriage, and brataghara cards in dark mode for better visibility
   - ✅ Enhanced text visibility with better font weight and text shadows in dark mode
   - ✅ Added Odia month display beside the red dot in the header
   - ✅ Created utility function to map English months to corresponding Odia months
   - ✅ Fixed the Odia name for May month (updated from "ମେ" to "ମଇ")
   - ✅ Optimized calendar grid for readability
   - ❌ Animations for month transitions
   - ❌ Share functionality for date details

4. **Additional Performance Optimizations**: ⚠️ PARTIALLY COMPLETED
   - ✅ Performance measurement system
   - ✅ Startup time measurement and optimization
   - ✅ In-memory caching with TTL
   - ✅ Adjacent month prefetching
   - ❌ Memory usage optimization
   - ❌ Battery usage optimization
   - ❌ Database query optimization with indexes

5. **Remote Configuration**: ✅ COMPLETED
   - ✅ Fast initialization with cached/default config
   - ✅ Background refresh for updates
   - ✅ Support for migrating from Supabase Cloud to self-hosted
   - ✅ Fallback mechanism for offline operation

## Project Structure

### Expo Router File Naming Conventions

This project uses Expo Router for navigation, which follows a file-based routing system similar to Next.js. Understanding the special file naming conventions is important:

- `src/index.js`: The main entry point of the application (specified in package.json)
- `app/_layout.tsx`: The root layout that wraps the entire application
- `app/(tabs)/_layout.tsx`: The layout specifically for the tabs navigation
- `app/+not-found.tsx`: Special file for 404 page (note the `+` prefix)
- `app/index.tsx`: Redirects to the main tab
- `app/(tabs)/index.tsx`: The main calendar screen (index route within tabs)

Files with special prefixes:
- Files starting with `_` (like `_layout.tsx`) define layouts
- Files starting with `+` (like `+not-found.tsx`) are for error handling
- Directories in parentheses like `(tabs)` define route groups

These naming conventions are required by Expo Router and cannot be changed without breaking the routing system.

### Directory Structure

```
/src                 # Source code directory
  /components        # UI components
    /calendar        # Calendar-related components
    /common          # Reusable UI components
      /SentryLogViewer.tsx  # Development-only log viewer
      /PerformanceDashboard.tsx  # Development-only performance dashboard
      /UpdatePromptModal.tsx # Modal to prompt user for manual update
    /reminders       # Reminder-related components
      /RemindersList.tsx  # List of user reminders
      /ReminderItem.tsx   # Individual reminder component
      /TithiPicker.tsx    # Custom picker for Odia months, pakshas, and tithis
    /providers       # Provider components
      /CalendarDataProvider.tsx  # Database provider component
  /hooks             # Custom React hooks
    /useCalendarData.ts  # Hooks for accessing calendar data
  /services          # Service layer
    /database-service.ts  # SQLite database service
    /cache-service.ts  # In-memory cache service
    /data-coordinator.ts  # Coordinates database and cache access
    /supabase-client.ts # Supabase client initialization
    /sync-service.ts    # Supabase data synchronization logic
    /reminder-service.ts # Manages reminder operations
    /notification-service.ts # Handles notification scheduling
  /store             # State management with Zustand
    /settings-store.ts  # Settings state management
    /calendar-store.ts  # Calendar state management
    /performance-store.ts  # Performance metrics state management
    /reminder-store.ts  # Reminder state management
  /types             # TypeScript type definitions
    /calendar.ts     # Calendar-related types
    /settings.ts     # Settings-related types
    /store.ts        # Store-related types
    /performance.ts  # Performance-related types
    /database.ts     # Database-related types
    /reminders.ts    # Reminder-related types
  /utils             # Utility functions
    /logging-sentry.ts  # Centralized logging system with Sentry integration
    /tithi-calculator.ts # Calculates tithi occurrences
    /date-formatter.ts  # Formats dates for display
  /constants         # App constants (like translations)
  /config            # Centralized application configuration
    /appConfig.ts    # Main configuration file
  /mocks             # Mock data (used as fallback)
  /index.js          # MAIN ENTRY POINT - specified in package.json
/app                 # Expo Router screens and navigation
  /_layout.tsx       # ROOT LAYOUT - wraps the entire application
  /error-boundary.tsx # Global error boundary component
  /index.tsx         # Root redirect to tabs
  /+not-found.tsx    # 404 page
  /modal.tsx         # Modal screen
  /add-reminder.tsx  # Add/edit reminder screen
  /reminder-details.tsx # Reminder details screen
  /(tabs)            # Tab navigation group
    /_layout.tsx     # TABS LAYOUT - defines tab navigation structure & contains automatic review prompt logic (uses appConfig)
    /index.tsx       # Calendar screen (main calendar view)
    /settings.tsx    # Settings screen (theme, language, sync, error reporting, manual review prompt, etc. - uses appConfig)
    /reminders.tsx   # Reminders screen (list of user reminders)
/assets              # Static assets
  /images            # Image assets
  /db                # Database files
    /odia_calendar_data.db  # Pre-bundled SQLite database
/docs                # Documentation files
  /DATABASE_ISSUES.md  # Database-related issues and solutions
```

## Key Components

### Calendar Components

1. **CalendarGrid.tsx**:
   - Displays the monthly calendar grid
   - Uses memoized DayCell component
   - Implements date check functions with useCallback
   - Handles date selection
   - Now uses database data via useCalendarMonth hook

### Reminder Components

1. **RemindersList.tsx**:
   - Displays a list of user reminders
   - Uses optimized FlatList with memoized items
   - Implements empty state and loading indicators
   - Provides options to add, edit, and delete reminders

2. **ReminderItem.tsx**:
   - Displays an individual reminder with title, type, and next occurrence
   - Shows toggle switch for enabling/disabling reminders
   - Provides options to edit and delete
   - Uses proper formatting for dates and times

3. **TithiPicker.tsx**:
   - Custom picker component for selecting Odia months, pakshas, and tithis
   - Displays options in a modal with search functionality
   - Supports both English and Odia languages
   - Provides clear visual feedback for selected items

2. **DateDetailsModal.tsx**:
   - Shows detailed information for a selected date.
   - Displays Gregorian Date, Odia Date/Month/Year, Day Name, Paksha, Tithi (with end time), Nakshatra (with end time), Yoga, Karana, Chandra Rasi, Sunrise/Sunset times.
   - Conditionally displays Festivals and indicators for suitable Marriage/Brata Ghara dates.
   - Uses database data via `useDateDetails` hook.
   - Implements loading and error states.
   - Uses a `DetailRow` sub-component for consistent layout.
   - Layout structured into logical sections with Odia date and Panchang combined into one block.
   - Marriage and Brata Ghara eligibility information integrated into the main section.
   - Banner ad positioned below the combined Odia date and Panchang section.
   - Ensures proper display of all text in Odia when Odia language is selected.

3. **FestivalsList.tsx**:
   - Displays festivals for the selected month
   - Uses optimized FlatList with memoized items
   - Implements empty state
   - Now uses database data via useFestivals hook
   - Implements loading and error states

4. **MarriageDatesList.tsx**:
   - Displays marriage dates for the selected month
   - Uses optimized FlatList with memoized items
   - Implements empty state
   - Now uses database data via useMarriageDates hook
   - Implements loading and error states

5. **BrataGharaDatesList.tsx**:
   - Displays brata ghara dates for the selected month
   - Uses optimized FlatList with memoized items
   - Implements empty state
   - Uses database data via useBrataGharaDates hook
   - Implements loading and error states
   - Uses a purple color scheme to differentiate from marriage dates

6. **MonthYearPicker.tsx**:
   - Modal for selecting month and year
   - Uses optimized FlatLists for month and year selection
   - Implements memoized render functions

### Database Components

1. **CalendarDataProvider.tsx**:
   - Provides database context to the application
   - Initializes the database on app startup
   - Handles database initialization errors
   - Provides retry functionality for failed initialization
   - Includes development-only debug panel with:
     - Database initialization status
     - Database file information
     - Cache clearing functionality
     - SQL Query Executor for direct database queries (development builds only)

2. **database-service.ts**:
   - Handles SQLite database initialization
   - Copies pre-bundled database from assets
   - Provides methods for querying calendar data
   - Implements proper error handling
   - Includes schema migration runner (`runMigrations`) using `PRAGMA user_version`.
   - Provides helper methods for reminder-related database operations

### Reminder Services

1. **reminder-service.ts**:
   - Manages CRUD operations for reminders
   - Handles scheduling of notifications for reminders
   - Calculates next occurrences of tithis for scheduling
   - Manages recurring reminders with proper rescheduling
   - Provides methods for retrieving and filtering reminders

2. **notification-service.ts**:
   - Handles notification permissions and initialization
   - Schedules and cancels notifications
   - Manages notification delivery and user interactions
   - Tracks notification status in the database with proper column aliases
   - Implements structured logging for the notification lifecycle
   - Handles background notification scheduling
   - Properly excludes delivered notifications when finding the next occurrence

3. **tithi-calculator.ts**:
   - Calculates the next occurrence of a specific tithi
   - Finds all occurrences of a tithi within a date range
   - Handles edge cases like tithis spanning multiple days
   - Provides utilities for working with Odia calendar elements

3. **cache-service.ts**:
   - Provides in-memory caching for database queries
   - Implements TTL (Time To Live) for cache entries
   - Optimizes performance by reducing database access

4. **data-coordinator.ts**:
   - Coordinates between database and cache
   - Implements prefetching for adjacent months
   - Provides a unified interface for data access

5. **sync-service.ts**:
   - Handles background data synchronization with Supabase backend.
   - Checks for existing year updates and new year data based on versions.
   - Respects user settings for auto-update and Wi-Fi only downloads.
   - Sets a flag (`UPDATE_PENDING`) if updates are found but auto-update is off.
   - Provides `triggerManualUpdate` function to bypass settings for user-initiated updates.
   - Provides `isUpdatePending` function to check the flag.

6. **useCalendarData.ts**:
   - Provides React hooks (`useCalendarMonth`, `useDateDetails`, `useFestivals`, `useMarriageDates`, `useBrataGharaDates`) for accessing calendar data via `dataCoordinator`.
   - Implements loading and error states.
   - Handles data fetching and caching implicitly through `dataCoordinator`.
   - Hooks re-fetch data when `lastDataUpdateTime` in `calendar-store` changes.

### Logging and Error Handling Components

1. **logging-sentry.ts**:
   - Centralized logging system with Sentry integration
   - Environment-aware logging (development vs. production)
   - Component-specific logging with context
   - Log levels (DEBUG, INFO, WARN, ERROR)
   - Integration with Sentry for remote error tracking

2. **SentryLogViewer.tsx**:
   - Development-only log viewer component
   - Displays logs with filtering by level
   - Provides test functionality for Sentry integration
   - Only visible in development builds

3. **error-boundary.tsx**:
   - Global error boundary component
   - Platform-specific error handling
   - User-friendly error messages
   - Integration with logging system

### State Management

1. **settings-store.ts**:
   - Manages theme mode (light/dark/system).
   - Manages language (English/Odia).
   - Manages error reporting preference (`errorReportingEnabled`).
   - Manages data sync preferences (`autoUpdateEnabled`, `autoUpdateWifiOnly`).
   - Provides actions to toggle settings.
   - Persists settings to AsyncStorage.

2. **calendar-store.ts**:
   - Manages selected date, month, and year.
   - Provides navigation functions (previous/next month, go to today).
   - Manages date details modal visibility.
   - Tracks `lastDataUpdateTime` to trigger UI refreshes after sync/cache invalidation.
   - Manages state for the update prompt modal (`isUpdatePending`, `showUpdatePrompt`).

## Type System

The project uses a comprehensive type system defined in `/src/types`:

1. **calendar.ts**:
   - OdiaDate: Represents a day in the Odia calendar
   - FestivalInfo: Represents a festival
   - MarriageDate: Represents a marriage date
   - CalendarDay: Represents a calendar grid cell
   - CalendarGrid: Represents the entire calendar grid

2. **settings.ts**:
   - AppSettings: Application settings interface
   - ThemeMode: Theme mode options (light/dark/system)
   - SupportedLanguage: Supported languages (en/or)

3. **store.ts**:
   - SettingsState: Settings store state interface
   - CalendarState: Calendar store state interface

## Implementation Notes

### Database Integration

The app now uses a SQLite database for all calendar data:
- Pre-bundled database file in `assets/db/odia_calendar_data.db`
- Database is copied to the app's document directory on first launch
- All calendar data is queried from the database
- In-memory caching layer for performance optimization
- Prefetching of adjacent months for smoother navigation
- Background synchronization with Supabase for updates

The mock data from `/src/mocks/calendar-data.ts` has been completely replaced with the SQLite implementation.

### Performance Optimizations

1. **Component Memoization**:
   - Used React.memo() for pure components
   - Implemented proper dependency arrays

2. **Function Memoization**:
   - Used useCallback() for event handlers
   - Used useMemo() for expensive calculations

3. **List Rendering**:
   - Optimized FlatList components with:
     - initialNumToRender
     - maxToRenderPerBatch
     - windowSize
     - removeClippedSubviews

### Language and Localization

1. **Odia Digit Conversion**:
   - Implemented centralized utility in `/src/utils/language-utils.ts`
   - Created mapping of English digits to Odia digits
   - Added conversion functions for numbers and dates
   - Applied to calendar grid, year display, and date formatting

2. **Optimization Strategy**:
   - Conversion applied only to dynamically generated numbers
   - Database will store pre-formatted text in both languages
   - Avoids unnecessary runtime conversions for better performance

### Logging System

1. **Centralized Logging Utility**:
   - Implemented in `/src/utils/logger.ts`
   - Multiple log levels (DEBUG, INFO, WARN, ERROR)
   - Environment-aware behavior (development vs. production)
   - Structured logging with timestamps and context
   - Support for remote logging in production

2. **Component Integration**:
   - LogProvider component for app-wide logging configuration
   - Component-specific logging with context
   - Global error handling and logging
   - Development-only LogViewer for in-app log inspection

3. **Usage Guidelines**:
   - Use `logger.debug()` for detailed development information (not shown in production)
   - Use `logger.info()` for general information (not shown in production by default)
   - Use `logger.warn()` for warning conditions (shown in production)
   - Use `logger.error()` for error conditions (shown in production and potentially sent to remote)
   - Include relevant context with all log messages
   - Use component-specific loggers with `useComponentLogger()` hook

### Performance Measurement System (Simplified)

1.  **Simple Utility**:
    *   Implemented in `/src/utils/performanceMonitor.ts`.
    *   Uses simple `mark(name)` and `measure(name, startMark, endMark?)` functions.
    *   No complex state management (Zustand store removed).
    *   Only active in development mode (`__DEV__`).
2.  **Direct Logging**:
    *   Measurements are logged directly to the console using the existing logger (`logger.debug`) immediately upon completion (e.g., `[PERF] Measurement Name: 123.4ms`).
    *   Provides clear, chronological performance feedback during development.
    *   Removed the visual Performance Dashboard component.
3.  **Usage Guidelines**:
    *   Wrap code blocks with `mark()` before and `measure()` after.
    *   Use `measureAsync()` or `measureSync()` helper functions to wrap entire function calls.
    *   Check console debug logs for performance timings during development.

### Remote Configuration System

1. **Overview**:
   * Implemented in `/src/services/RemoteConfigService.ts`.
   * Provides a way to update Supabase connection details without requiring an app update.
   * Uses a fast initialization approach with cached/default config first, then background refresh.
   * Supports migration from Supabase Cloud to self-hosted instances.

2. **Key Features**:
   * Default configuration bundled with the app.
   * Remote configuration fetched from a stable URL (GitHub Pages).
   * Configuration cached locally with TTL (Time To Live).
   * Background refresh that doesn't block app startup.
   * Version tracking to ensure only newer configurations are applied.
   * Fallback mechanism for offline operation.

3. **Integration with Supabase**:
   * Supabase client is initialized with cached/default config first.
   * Background refresh triggered after initialization.
   * New configuration applied on next app launch.
   * Proxy pattern used to ensure Supabase client is always initialized before use.

### Next Implementation Steps

1. **Database Integration**: ✅ COMPLETED
   - ✅ Created a database service for initializing SQLite
   - ✅ Implemented pre-bundled database loading
   - ✅ Created database provider component
   - ✅ Implemented database context hooks

2. **Replace Mock Data**: ✅ COMPLETED
   - ✅ Updated components to use database instead of mock data
   - ✅ Implemented loading states and error handling

3. **Data Synchronization**: ✅ COMPLETED
   - ✅ Implemented version checking mechanism (`sync-service.ts`)
   - ✅ Created calendar update service (`sync-service.ts`)
   - ✅ Implemented background fetch functionality (`sync-service.ts`, triggered by `data-coordinator.ts`)
   - ✅ Fixed initialization race condition causing "Database not initialized" errors by adding loading state to UI (`app/(tabs)/index.tsx`, `useCalendarDataContext`).
   - ✅ Fixed repeated initialization logs by adding module-level guard in `data-coordinator.ts`.
   - ✅ Made sync service resilient to invalid `eng_date` in Supabase `calendar_updates` table (`sync-service.ts`).
   - ✅ Improved sync transaction handling to prevent errors and ensure consistency (`sync-service.ts`).
   - ✅ Implemented cache invalidation after successful sync (`sync-service.ts`, `data-coordinator.ts`, `cache-service.ts`).
   - ✅ Implemented instant UI refresh after cache invalidation using Zustand store (`calendar-store.ts`, `data-coordinator.ts`, `useCalendarData.ts`).
   - ✅ Configured calendar store persistence to prevent saving navigation state (`calendar-store.ts`), ensuring app always opens to current month.
   - ✅ Fixed TypeScript errors related to `expo-sqlite` `execAsync` usage (`database-service.ts`).

4. **User Consent for Updates**: ✅ COMPLETED
   - ✅ Added settings toggles for auto-update and Wi-Fi only.
   - ✅ Integrated settings into `sync-service.ts`.
   - ✅ Implemented manual update prompt modal (`UpdatePromptModal.tsx`).
   - ✅ Added manual update check button and last checked display to Settings screen.
   - ✅ Reorganized settings screen for better UX with most important items at the top:
     - Data & Sync section at the top (most important)
     - Data Management section right after (related to data)
     - Appearance section (theme and language) in the middle
     - Privacy section (error reporting) after appearance
     - App Actions section at the bottom (least frequently used)

5. **Date Details Modal Enhancement**: ✅ COMPLETED
   - ✅ Added display for Nakshatra, Yoga, Karana, Chandra Rasi, Tithi/Nakshatra end times, Sunrise/Sunset.
   - ✅ Added conditional indicators for Marriage/Brata Ghara dates.
   - ✅ Restructured layout with sections and `DetailRow` component.
   - ✅ Removed non-functional moon phase indicators.

6. **Configuration Management**: ✅ COMPLETED
   - ✅ Created a centralized configuration file (`src/config/appConfig.ts`).
   - ✅ Moved hardcoded values (review logic thresholds, store URLs, share URL, timeouts) to `appConfig.ts`.
   - ✅ Refactored relevant components (`app/(tabs)/_layout.tsx`, `app/(tabs)/settings.tsx`) to use the centralized configuration.

7. **Brata Ghara Dates Feature**: ✅ COMPLETED
   - ✅ Created BrataGharaDatesList component for displaying brata ghara dates.
   - ✅ Implemented useBrataGharaDates hook for fetching brata ghara dates.
   - ✅ Added methods to data coordinator and database service for brata ghara dates.
   - ✅ Added styling and translations for brata ghara dates.
   - ✅ Added the component to the home page.

8. **Data Synchronization Improvements**: ✅ COMPLETED
   - ✅ Fixed year data version synchronization to only update version when actual data exists.
   - ✅ Added verification step to ensure data exists in the panchang_data table before updating the version table.
   - ✅ Added method to remove version entries when no actual data exists.
   - ✅ Improved error handling and logging for data synchronization.

9. **Data Management Features**: ✅ COMPLETED
   - ✅ Implemented "Delete Year Data" feature in settings screen for managing storage space.
   - ✅ Added protection to prevent deletion of current year data to ensure app functionality.
   - ✅ Implemented specific error codes and user-friendly error messages for different deletion scenarios.
   - ✅ Automatically filters out current year from available years list in the UI.
   - ✅ Added confirmation dialog before deletion to prevent accidental data loss.
   - ✅ Implemented proper transaction handling for database operations to ensure atomicity.
   - ✅ Added status messages with success/error feedback after deletion operations.

10. **Reminder Feature**: ✅ COMPLETED
   - ✅ Implemented database tables for reminders and scheduled notifications
   - ✅ Created reminder service for CRUD operations on reminders
   - ✅ Implemented notification service for scheduling notifications
   - ✅ Developed tithi calculator for finding tithi occurrences
   - ✅ Created reminder management UI (list, add/edit, details screens)
   - ✅ Added custom TithiPicker component for selecting Odia calendar elements
   - ✅ Implemented reminder store for state management
   - ✅ Added reminder tab to bottom navigation
   - ✅ Implemented proper error handling and logging

## Development Guidelines

### Code Style

- Use TypeScript for all new files
- Add JSDoc comments for functions and interfaces
- Follow existing naming conventions
- Use React.memo() for pure components
- Use useCallback() and useMemo() with proper dependencies
- Use the logging system instead of direct console.log calls
- Wrap components with ErrorBoundary for proper error handling
- Use try/catch blocks for error-prone operations
- Use the component logger with proper context

### Development Tools

#### SQL Query Executor

The app includes a SQL Query Executor in the debug panel for development builds:

- Access it by tapping the "Show Debug" button in the bottom-right corner of the app
- Enter SQL queries in the text input field
- Tap "Execute Query" to run the query against the app's SQLite database
- Results are logged to the console, not displayed in the UI
- Includes proper error handling for invalid queries
- This feature is ONLY available in development builds and is completely removed from production builds

### Performance Considerations

- Minimize re-renders with proper memoization
- Optimize list rendering for large datasets
- Use proper dependency arrays in hooks
- Implement loading states for async operations

### Testing

- Manual testing is currently used
- Future: Implement unit tests for critical functions

## Important Notes for Future Development

### Database Implementation

The SQLite database has been implemented with the following considerations:
- ✅ The database file (`odia_calendar_data.db`) is maintained in git version control
- ✅ The database file is stored in the `assets/db` folder for bundling with the app
- ✅ A copy of the database file is also kept in the `docs` folder for reference
- ✅ Proper error handling is implemented for all database operations
- ✅ Pre-formatted Odia text is stored for all display fields to avoid runtime conversions
- ✅ Both English and Odia versions of text are included in the database schema
- ✅ For date-related fields, both the raw date and the formatted display strings are stored
- ✅ Reminder data is stored in dedicated tables with proper relationships
- ✅ Scheduled notifications are tracked with status information

Important lessons learned during implementation:
- When querying SQLite metadata tables (like `sqlite_master`), use `getAllAsync` instead of `execAsync` to avoid null reference errors
- Ensure the database file is properly copied from assets to the app's document directory
- Use the Asset module from expo-asset to properly load and download the database file
- Implement a robust caching mechanism to minimize database access
- See [DATABASE_ISSUES.md](./DATABASE_ISSUES.md) for detailed information on issues encountered and their solutions

### Performance Considerations

- Monitor memory usage, especially with large datasets
- Implement pagination for lists that might grow large
- Consider implementing virtualization for very large datasets
- Use the React DevTools Profiler to identify and fix performance bottlenecks
- Implement proper loading states and error boundaries

### User Experience

- Maintain consistent design language across all screens
- Ensure accessibility features are implemented
- Support both light and dark themes consistently
- Provide clear feedback for user actions
- Implement smooth transitions between screens and states

### Testing Strategy

- Implement unit tests for critical business logic
- Add integration tests for key user flows
- Test on various device sizes and orientations
- Test with different language settings
- Test offline functionality

### Project Documentation

Refer to these additional documentation files for specific aspects of the project:

1. **[IMPLEMENTATION_CHECKLIST.md](./IMPLEMENTATION_CHECKLIST.md)**
   - Detailed breakdown of implemented and pending features
   - Priority order for implementation
   - Technical considerations for each feature

2. **[PERFORMANCE_OPTIMIZATIONS.md](./PERFORMANCE_OPTIMIZATIONS.md)**
   - Current performance optimizations
   - Techniques used for optimizing rendering
   - Future optimization opportunities

3. **[CODE_QUALITY.md](./CODE_QUALITY.md)**
   - TypeScript type system details
   - Naming conventions
   - Documentation standards
   - Code organization guidelines

4. **[DATABASE_ISSUES.md](./DATABASE_ISSUES.md)**
   - Database-related issues encountered during development
   - Solutions and workarounds
   - Best practices for SQLite integration

5. **[BUILD_CONFIGURATION.md](./BUILD_CONFIGURATION.md)**
   - Critical version requirements (Kotlin, Gradle, AdMob)
   - Keystore configuration for development and production builds
   - AdMob configuration details
   - Build commands for development and production
   - Common build issues and solutions

## Manual Release Checklist

This section lists manual steps that MUST be performed before creating a new release build.

1.  **Update Bundled Database for Schema Migrations:**
    *   If the release includes database schema changes (i.e., `LATEST_APP_SCHEMA_VERSION` in `database-service.ts` was increased):
        1.  Manually apply **all** new migration scripts (from `src/migrations/` or embedded in code) sequentially to the database file located at `assets/db/odia_calendar_data.db` using an external SQLite tool.
        2.  Manually update the schema version within that same `assets/db/odia_calendar_data.db` file by executing `PRAGMA user_version = X;` (where X is the new `LATEST_APP_SCHEMA_VERSION`).
        3.  Commit the updated `assets/db/odia_calendar_data.db` file to version control.
    *   This ensures new installations start with the correct schema.

*(Add other manual checks here as needed, e.g., updating version numbers, checking asset integrity, etc.)*

## External References

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Expo Documentation](https://docs.expo.dev/)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [React Performance Optimization](https://reactjs.org/docs/optimizing-performance.html)
- [SQLite Documentation](https://www.sqlite.org/docs.html)
- [Expo Notifications Documentation](https://docs.expo.dev/versions/latest/sdk/notifications/)
- [Reminder Feature Documentation](./reminder-feature.md)
