# Odia Calendar Lite - App Initialization and Data Flow

This document provides a detailed explanation of the app's initialization process, data flow, and synchronization mechanisms. It serves as a reference for understanding the exact sequence of events from app installation to normal operation.

## Table of Contents

1. [First Installation and Startup](#first-installation-and-startup)
2. [Subsequent App Reopens](#subsequent-app-reopens)
3. [Remote Configuration Process](#remote-configuration-process)
4. [Supabase Initialization](#supabase-initialization)
5. [Background Sync Process](#background-sync-process)
6. [Manual Update Process](#manual-update-process)
7. [Data Flow Diagram](#data-flow-diagram)
8. [Potential Optimization Areas](#potential-optimization-areas)

## First Installation and Startup

### 1. App Installation
- The app is installed with pre-bundled database file (`odia_calendar_data.db`) in the assets folder
- Default configuration values are included in `appConfig.ts` and `DEFAULT_SETTINGS`

### 2. Initialization Phase
- Entry point: `src/index.js` loads Expo Router
- `app/_layout.tsx` is loaded first
- Splash screen is shown and prevented from auto-hiding
- Performance monitoring starts with `mark('AppInitialize_Start')`
- Supabase client initialization begins in background with `supabase` proxy object

### 3. Font Loading
- Required fonts are loaded using `useFonts` hook
- Performance is measured with `mark('FontsLoad_Start')`

### 4. Logging Initialization
- Logging system initialized with `initializeLogging()`
- Sentry configured for error reporting in production

### 5. Database Initialization
- `CalendarDataProvider` initializes the database
- `dataCoordinator.initialize()` is called, which:
  - Calls `databaseService.initialize()` to set up SQLite
  - Checks if database file exists in app's document directory
  - If not, copies pre-bundled database from assets
  - Opens database connection and verifies tables
  - Runs migrations based on schema version
  - Initializes sync service with `syncService.initializeSync()`
  - Sets up year version tracking

### 6. Remote Configuration
- `RemoteConfigService` initializes with cached/default configuration
- Tries to load from AsyncStorage cache first
- Falls back to default values if no cache exists
- Background refresh triggered to fetch latest configuration

### 7. State Initialization
- Zustand stores initialized:
  - `settings-store.ts` loads persisted settings or uses defaults
  - `calendar-store.ts` initializes with current date (doesn't persist navigation state)

### 8. Initial Data Loading
- After database initialization, app navigates to main calendar screen
- `CalendarGrid` loads data for current month using `useCalendarMonth` hook
- This triggers `dataCoordinator.getMonthData()` which:
  - Checks in-memory cache (empty on first launch)
  - Queries database for current month's data
  - Stores result in cache
  - Triggers prefetching of adjacent months

### 9. Background Sync
- After 5-second delay, `syncService.runBackgroundSync()` is triggered
- Checks network connectivity and user settings
- If conditions met, checks for updates from Supabase:
  - Checks for updates to existing years' data
  - Checks for availability of new years' data
  - If auto-update enabled, applies updates immediately
  - If auto-update disabled, sets flag for pending updates

### 10. Splash Screen Hiding
- Once fonts loaded and initialization complete, `SplashScreen.hideAsync()` is called
- App's UI becomes visible to user

## Subsequent App Reopens

### 1. App Relaunch
- App launched from device's home screen or app drawer
- Splash screen shown again

### 2. State Restoration
- Persisted settings loaded from AsyncStorage via Zustand
- Calendar navigation state NOT restored (by design) - app always opens to current month

### 3. Database Reinitialization
- Database service checks if database file exists (it should)
- Opens existing database connection
- Verifies database is accessible and runs any pending migrations

### 4. Cache Restoration
- In-memory cache starts empty (not persisted between app launches)
- When calendar screen loads, data is fetched from database and cached

### 5. Remote Config Check
- Cached remote configuration loaded from AsyncStorage
- Background refresh triggered to check for configuration updates

### 6. Background Sync
- After initialization, background sync triggered again
- Checks for updates based on last sync time
- May skip check if last check was recent (within 24 hours)

### 7. UI Rendering
- Calendar view rendered for current month
- If pending updates and auto-update disabled, update prompt may be shown

## Remote Configuration Process

### Fast Initialization (Before First Screen Appears)
- In `app/_layout.tsx`, `void supabase;` triggers Supabase proxy initialization
- This calls `initializeSupabase()` which immediately calls `remoteConfig.initializeWithCache()`
- Only loads cached configuration from AsyncStorage or falls back to default values
- Happens while splash screen visible, before any UI rendered

### Background Refresh (After First Screen Appears)
- After fast initialization, background refresh scheduled with 100ms delay
- Network request to fetch latest remote configuration happens after UI rendered
- If updates found, saved to cache but only applied on next app start

## Supabase Initialization

### Client Creation (Before First Screen Appears)
- In `app/_layout.tsx`, `void supabase;` triggers Supabase proxy initialization
- Happens outside React component lifecycle, at module level
- Proxy pattern creates placeholder that initializes actual client when first accessed
- When first accessed, logs a debug message (not a warning) to avoid cluttering Sentry logs
- Client creation happens in `initializeSupabase()`:
  - Gets configuration from cache or defaults
  - Creates Supabase client with `createClient()`

### Actual Connection (After First Screen Appears)
- First actual connection to Supabase happens when first query is made
- Occurs in background sync process after 5-second delay
- First Supabase query happens in `sync-service.ts` when checking for updates

## Background Sync Process

After the 5-second delay, the following process occurs:

### 1. Initial Checks
- **Lock Acquisition**: Prevents multiple syncs running simultaneously
- **Network Connectivity**: Verifies device is connected to internet
- **Last Check Time**: Skips if checked within last 24 hours (unless forced)

### 2. Data Checks and Downloads

#### A. Check for Updates to Existing Years
- Fetches all year versions from local database
- Queries Supabase `calendar_year_versions` table for server versions
- Compares local and server versions to identify years needing updates
- If updates found and auto-update enabled:
  - Queries `calendar_updates` table for specific updated records
  - Applies updates to local database
- If auto-update disabled:
  - Sets pending update flag

#### B. Check for New Year Data
- Determines next year that might be needed (current year + 1)
- Checks if year exists in local database
- If not, queries Supabase to check availability
- If available and auto-update enabled:
  - Downloads entire year's data from `panchang_data` table
  - Inserts into local database
  - Updates local version table
- If auto-update disabled:
  - Sets pending update flag

### 3. Final Steps
- Updates last check timestamp in AsyncStorage
- Invalidates cache for updated months if any
- Notifies UI to refresh via calendar store
- Releases sync lock

## Manual Update Process

When "Check for Updates" is clicked in settings:

### 1. UI Updates
- Shows loading indicator
- Clears previous status messages

### 2. Sync Service Triggered
- Calls `syncService.triggerManualUpdate()`
- Performs preliminary checks:
  - Database initialization
  - Sync lock acquisition
  - Network connectivity

### 3. Force Updates and Downloads
- Calls `forceApplyUpdates()` for existing year updates
- Calls `forceDownloadNewYear()` for new year data
- Both bypass auto-update setting checks

### 4. Database Updates
- Inserts any found updates or new data into SQLite database
- Updates version information
- Clears pending update flag

### 5. Cache Invalidation
- Invalidates cache for affected months
- Triggers UI refresh

### 6. UI Updates with Results
- Shows success or failure message
- Updates last checked timestamp
- Automatically clears status message after timeout

## Data Flow Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  UI Components  │◄────┤  Zustand Store  │◄────┤  Cache Service  │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         │                       │                       │
┌────────▼────────┐     ┌────────▼────────┐     ┌────────▼────────┐
│                 │     │                 │     │                 │
│ Data Coordinator│◄────┤ Database Service│◄────┤   Sync Service  │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         │                       │                       │
         └───────────────────────▼───────────────────────┘
                                 │
                        ┌────────▼────────┐
                        │                 │
                        │    Supabase     │
                        │                 │
                        └─────────────────┘
```

## Potential Optimization Areas

Based on code analysis, these areas could benefit from optimization:

1. **SQL Injection Risk**: Some queries use string interpolation instead of parameterized queries
2. **Missing Database Indexes**: Indexes needed on `panchang_data` table for frequently queried fields
3. **Error Handling Specificity**: More specific error messages for different failure scenarios
4. **Timeout Handling**: Better handling for stuck sync operations
5. **Memory Management**: Size limits for in-memory cache
6. **Battery Optimization**: Reduce background operations frequency
7. **Automated Testing**: Add tests for critical database operations
