# AdMob Integration Issues in Odia Calendar Lite

## Overview

This document outlines the compatibility issues encountered when attempting to integrate Google AdMob into the Odia Calendar Lite app. The primary challenge involves version mismatches between Ko<PERSON>in, Expo SDK, and the Google Mobile Ads SDK.

## AdMob Integration Options

### 1. expo-ads-admob (Not Available)

The `expo-ads-admob` package was the official Expo solution for AdMob integration in previous SDK versions. However:

- It has been removed from Expo SDK 52
- The last version (13.0.0) was published 3 years ago
- It's no longer maintained or recommended by Expo

### 2. react-native-google-mobile-ads (Current Approach)

The `react-native-google-mobile-ads` package is the recommended alternative for AdMob integration in Expo SDK 52. However, it comes with compatibility challenges:

## Core Issues

### 1. Kotlin Version Incompatibility

The application faces a three-way version conflict:

- **Expo SDK 52** uses Kotlin 1.9.0 (metadata version 1.9.0)
- **react-native-google-mobile-ads 15.1.0** uses Google Play Services Ads 24.2.0
- **Google Play Services Ads 24.2.0** requires Kotlin metadata version 2.1.0

This creates a fundamental incompatibility where the Google Mobile Ads SDK requires a newer Kotlin version than what Expo SDK supports.

### 2. Compose Compiler Compatibility

The Compose Compiler in expo-modules-core expects Kotlin version 1.7.20, but the project uses Kotlin 1.9.0, causing additional compatibility issues:

```
This version (1.3.2) of the Compose Compiler requires Kotlin version 1.7.20 but you appear to be using Kotlin version 1.9.0 which is not known to be compatible.
```

### 3. Build Errors

These incompatibilities result in specific build errors:

```
e.protobuf.kotlin_only_for_use_in_proto_generated_code_its_generator_and_tests.kotlin_moduleModule was compiled with an incompatible version of Kotlin. The binary version of its metadata is 2.1.0, expected version is 1.9.0.
```

## Attempted Solutions

Several approaches were attempted to resolve these issues:

1. **Downgrading Google Play Services Ads**: Attempting to use an older version (22.5.0) of Google Play Services Ads that might be compatible with Kotlin 1.9.0

2. **Modifying Kotlin Version**: Adjusting the project's Kotlin version to match requirements (tried 1.8.10, 1.8.22, etc.)

3. **Disabling Compose in expo-modules-core**: Attempting to work around the Compose Compiler issue by disabling Compose functionality

4. **Adding Resolution Strategy**: Using Gradle's resolution strategy to force consistent Kotlin library versions

5. **Suppressing Version Checks**: Adding flags to suppress Kotlin version compatibility checks

## Current Status

- The iOS version of the app works correctly with AdMob integration
- The Android version fails to build due to these Kotlin version incompatibilities
- We've reverted all attempted fixes to maintain a clean codebase

## Potential Solutions

1. **Wait for Official Updates**: The most reliable solution is to wait for official updates from either Expo or react-native-google-mobile-ads to resolve these compatibility issues

2. **Use an Older Version of react-native-google-mobile-ads**: Version 14.x might be more compatible with Expo SDK 52

3. **Custom AdMob Implementation**: Implement AdMob directly without using react-native-google-mobile-ads, though this requires significant native code

4. **Downgrade Expo**: Use an older version of Expo that's compatible with both AdMob and the required Kotlin version

## Recommendation

For projects requiring both Expo SDK 52 and AdMob integration on Android:

1. Start with iOS implementation which works without these issues
2. For Android, consider using an older version of react-native-google-mobile-ads (14.x)
3. Test thoroughly on both platforms before production deployment
4. Monitor for updates from Expo and react-native-google-mobile-ads that address these compatibility issues

## Technical Details for Reference

### Relevant Dependencies

- Expo SDK: 52
- React Native: 0.76.0
- react-native-google-mobile-ads: 15.1.0
- Google Play Services Ads: 24.2.0
- Kotlin version in project: 1.8.0

### Error Logs

```
e.protobuf.kotlin_only_for_use_in_proto_generated_code_its_generator_and_tests.kotlin_moduleModule was compiled with an incompatible version of Kotlin. The binary version of its metadata is 2.1.0, expected version is 1.9.0.
e: file:///Users/<USER>/.gradle/caches/8.10.2/transforms/66468b6b9c1148e43f95ddf2d1a343cb/transformed/play-services-ads-24.2.0-api.jar!/META-INF/third_party.kotlin.protobuf.src.commonMain.kotlin.com.google.protobuf.kotlin_shared_runtime.kotlin_moduleModule was compiled with an incompatible version of Kotlin. The binary version of its metadata is 2.1.0, expected version is 1.9.0.
```

```
e: This version (1.3.2) of the Compose Compiler requires Kotlin version 1.7.20 but you appear to be using Kotlin version 1.9.25 which is not known to be compatible.
```

---

## Solution by Cline (25/04/2025)

The Android build issue was resolved by addressing the Kotlin version conflicts. The successful fix involved two key changes:

1.  **Downgraded `react-native-google-mobile-ads`:**
    *   The version in `package.json` was changed from `^15.1.0` to `^14.1.0`.
    *   Reason: Version `15.1.0` depends on Google Play Services Ads `24.2.0`, which requires Kotlin `2.1.0`. Expo SDK 52 uses Kotlin `1.9.x`. Version `14.1.0` of the ads library is compatible with the older Kotlin version used by Expo SDK 52.

2.  **Aligned Project Kotlin Version:**
    *   In `android/build.gradle`, the `kotlinVersion` variable in the `buildscript.ext` block was explicitly set to `'1.9.25'`.
    *   The corresponding classpath dependency was updated to `classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")`.
    *   Reason: Build logs indicated that Expo SDK 52 dependencies were effectively using Kotlin `1.9.25`, despite the project potentially defaulting to `1.8.0`. Explicitly setting the version to `1.9.25` resolved the secondary conflict with the Compose Compiler in `expo-modules-core`, which expected Kotlin `1.7.20` but failed when encountering the mismatch with `1.9.25`. Aligning the project to `1.9.25` allowed the build to proceed successfully.

These two changes together ensured that `react-native-google-mobile-ads`, Expo SDK 52, and the Android project build configuration were all using compatible Kotlin versions, allowing the Android build to complete and AdMob ads to function.
