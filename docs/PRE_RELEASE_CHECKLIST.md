# Odia Calendar Lite - Android Pre-Release Checklist (Google Play Store)

This checklist outlines the necessary steps before submitting the Odia Calendar Lite app to the Google Play Store.

**Phase 1: Configuration & Metadata**

1.  **Versioning:**
    *   ✅ **`app.json`:** `expo.version` ("1.0.0") - Confirm this is the desired user-facing version.
    *   ✅ **`app.json`:** `versionCode` is `3` for the third release to Play Store.
    *   ✅ **`android/app/build.gradle`:** `versionName` ("1.0.0") matches `app.json`.
2.  **Application ID:**
    *   ✅ **`app.json`, `build.gradle`, `AndroidManifest.xml`, Java files:** Package ID set to `com.kalingatech.odia.simplecalendar`. Directory structure updated.
3.  **App Name:**
    *   ✅ **`app.json` & `strings.xml`:** Device app name set to "Odia Calendar".
4.  **Icons & Splash Screen:**
    *   ✅ **`app.json`:** Paths seem correct. **Action:** Manually verify asset quality and resolution.
5.  **Orientation:**
    *   ✅ **`app.json`:** Orientation is "portrait". Confirmed.

**Phase 2: Build & Signing**

6.  **Build Type:**
    *   ✅ **Action:** Ensure you generate a *release* build (`.aab` recommended for Play Store).
7.  **Signing Key:**
    *   ✅ **`android/app/build.gradle`:** Configured to load from `local.properties`. Keystore placed in `android/app/keystore/release.keystore`. Credentials added to `local.properties` (gitignored) with placeholders in `gradle.properties`.
8.  **Code Obfuscation (ProGuard/R8):**
    *   ✅ **`android/app/build.gradle`:** `minifyEnabled true` set for `release`.
    *   ✅ **`android/app/proguard-rules.pro`:** Comprehensive rules added for React Native, Expo, SQLite, AdMob, and Sentry.
    *   ✅ **`docs/PROGUARD_OPTIMIZATION.md`:** Documentation created for tracking size optimization and troubleshooting.
9.  **Resource Shrinking:**
    *   ✅ **`android/app/build.gradle`:** `shrinkResources true` set for `release`.

**Phase 3: Dependencies & Plugins**

10. **Update Dependencies:**
    *   ✅ **Action:** Check for and update outdated dependencies (especially Expo SDK). Test after updates.
11. **Plugin Configuration:**
    *   ✅ **`appConfig.ts` & `AndroidManifest.xml`:** Production AdMob IDs are set.
    *   ✅ **`app.json` & `sentry.properties`:** Sentry config seems present. Build uses auth token to fetch DSN for `kalinga-tech`/`react-native`. **Action:** User to confirm this org/project is correct for production.
    *   ✅ **`app.json`:** Expo Router config seems present.
12. **Remove Debug Code:**
    *   ✅ **Action:** Remove/disable `console.log`, `logger.debug/info`, ensure dev components (`SentryLogViewer`, etc.) are excluded via `__DEV__`.

**Phase 4: Testing**

13. **Functionality Testing:** ✅ Test all features & edge cases.
14. **UI/UX Testing:** ✅ Test on various devices/Android versions/screen sizes. Check light/dark modes and languages.
15. **Performance Testing:** ✅ Profile startup, scrolling, memory, battery.
16. **Offline Testing:** ✅ Test offline functionality and sync handling.
17. **Update Testing:** ✅ Test data sync and schema migration flow (if applicable).
18. **Regression Testing:** ✅ Re-test fixed bugs.

**Phase 5: Assets & Resources**

19. **Asset Optimization:** ✅ Optimize images. Verify bundled database (`assets/db/odia_calendar_data.db`) is correct, especially if schema changed.
20. **Localization:** ✅ Proofread all English/Odia strings.

**Phase 6: Play Store Listing**

21. **Visuals:** ✅ Prepare Screenshots, Video (optional), Feature Graphic.
22. **Text:** ✅ Write Short/Full Descriptions. **Note:** Desired Play Store name is "Odia Calendar - Simple & Fast (ଓଡ଼ିଆ କ୍ୟାଲେଣ୍ଡର)".
23. **Metadata:** ✅ Complete Content Rating, choose Category, provide Contact Info.
24. **Privacy Policy URL:** ✅ Provide a link to your policy.

**Phase 7: Legal & Compliance**

25. **Privacy Policy Content:** ✅ Ensure policy accurately reflects data usage (AdMob, Sentry, etc.).
26. **Data Safety Section:** ✅ Complete accurately in Play Console.
27. **Permissions:**
    *   ✅ **`AndroidManifest.xml`:** Unnecessary permissions removed. `INTERNET` and `VIBRATE` remain. `VIBRATE` confirmed as needed.

**Phase 8: Final Checks**

28. **Crash Monitoring:** ✅ Confirm Sentry reports release crashes.
29. **Analytics:** ✅ Confirm production configuration (if used).
30. **Expo Updates:**
    *   ✅ **`AndroidManifest.xml`:** OTA updates are disabled. Confirmed as intended for Play Store-only updates.
31. **Final Build Test:** ✅ Install the signed release `.aab` and perform a smoke test.

**Summary of Critical Actions:**

*   ✅ **Configure Release Signing Key** (`build.gradle`) - DONE
*   ✅ **Add Keystore Credentials** to `android/local.properties` - DONE
*   ✅ **Ensure `local.properties` is gitignored** - DONE (already in .gitignore)
*   ✅ **Place Keystore File** in `android/app/keystore/` - DONE
*   ✅ **Configure ProGuard Rules** (`proguard-rules.pro`) - DONE
*   ✅ **Add Database Indexes** for performance optimization - DONE
*   ✅ **Update AdMob IDs** for production - DONE
*   ✅ **Test Build with Optimizations** (Minify, Shrink Resources) - DONE
*   ✅ **Confirm `VIBRATE` Permission Need** - DONE (Needed)
*   ✅ **Confirm Expo OTA Update Setting** - DONE (Disabled Intentionally)
*   ✅ **Increment `versionCode`** (`app.json`) before final upload - DONE (Updated to `3` for third release)
*   🟡 **Verify Sentry Org/Project for Production** - PENDING (User confirmation needed)
*   ℹ️ **Investigate 2026 Data** - Deferred (Not required for this pre-release check)
