# Odia Calendar Lite - Release Tracking

This document tracks all releases of the Odia Calendar Lite app, including features, bug fixes, and other changes. It serves as a comprehensive record for development and release management.

## Version History

| Version | Version Code | Release Date | Play Store Status | Bundle Size | Key Changes |
|---------|--------------|--------------|-------------------|-------------|-------------|
| 1.0.0   | 6            | 2025-05-19   | Submitted         | 42MB        | Initial public release |

## Release Details

### v1.0.0 (Version Code 6) - Initial Release

**Release Date:** 2024-05-19  
**Bundle Size:** 42MB  
**Status:** Submitted to Play Store

#### Features:
- Complete Odia calendar with panchang information
- Festival and auspicious date tracking
- Marriage and Brata Ghara date information
- Personalized tithi reminders with notifications
- Bilingual support (Odia and English)
- Light and dark themes
- Offline functionality
- Swipe navigation between months
- Detailed date information modal

#### Technical Implementations:
- SQLite database with bundled panchang data
- React Native with Expo framework
- AdMob integration for monetization
- Sentry integration for error tracking
- Notification system with proper permission handling
- Session-based in-memory caching
- Performance optimizations (React.memo, useCallback, useMemo)

#### Known Issues:
- None reported yet

#### Build Information:
- React Native: 0.76.9
- Expo: 52.0.36
- Kotlin: 1.9.25
- Gradle: 8.10.2
- react-native-google-mobile-ads: 14.1.0
- ProGuard enabled with custom rules
- Architecture: armeabi-v7a, arm64-v8a

#### Important Files:
- AAB: `android/app/build/outputs/bundle/release/app-release.aab`
- Mapping: `android/app/build/outputs/mapping/release/mapping.txt`

## Bug Tracking

| Bug ID | Version Introduced | Version Fixed | Description | Status | Priority |
|--------|-------------------|---------------|-------------|--------|----------|
| -      | -                 | -             | No bugs reported yet | - | - |

## Feature Requests & Roadmap

| Feature ID | Description | Requested Date | Planned Version | Status |
|------------|-------------|----------------|-----------------|--------|
| F001 | Year data auto-update mechanism | 2024-05-19 | 1.1.0 | Planned |
| F002 | Cloud sync for reminders | 2024-05-19 | 2.0.0 | Under consideration |
| F003 | Widget for home screen | 2024-05-19 | 1.2.0 | Planned |

## Performance Metrics

| Version | App Size | Cold Start Time | Memory Usage | Battery Impact |
|---------|----------|-----------------|--------------|----------------|
| 1.0.0   | 42MB     | Not measured    | Not measured | Not measured   |

## Release Checklist

### Pre-Release
- [ ] All planned features implemented
- [ ] All critical bugs fixed
- [ ] UI/UX review completed
- [ ] Performance testing completed
- [ ] Localization verified
- [ ] AdMob integration verified
- [ ] Notification system tested
- [ ] Database integrity verified

### Release
- [ ] Version numbers updated (app.json, build.gradle)
- [ ] Release notes prepared
- [ ] AAB built and signed
- [ ] Mapping file saved for crash reporting
- [ ] Internal testing completed
- [ ] Submitted to Play Store

### Post-Release
- [ ] Monitor crash reports
- [ ] Monitor user feedback
- [ ] Plan next release based on feedback
- [ ] Update this document with actual release date

## Notes for Future Releases

### Version 1.1.0 (Planned)
- Automatic year data updates
- Performance improvements
- Additional festival information
- Bug fixes based on user feedback

### Version 1.2.0 (Planned)
- Home screen widget
- Enhanced reminder options
- Improved UI for date details
- Additional language options

### Version 2.0.0 (Long-term)
- Cloud sync for reminders
- User accounts
- Social sharing features
- Premium features

## Maintenance Notes

- Keep mapping files for each release for crash debugging
- Update database with new year data in Q4 each year
- Monitor AdMob performance and adjust placement if needed
- Regularly update dependencies while ensuring compatibility

---

*Last updated: 2024-05-19*
