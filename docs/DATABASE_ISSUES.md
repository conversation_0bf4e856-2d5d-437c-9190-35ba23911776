# Database Issues and Solutions

This document records database-related issues encountered during development and their solutions.

## SQLite Table Query Issue

### Issue Description
During the app's startup, when it tried to check if the necessary database tables (like the one holding calendar data) existed, the command used to ask the database for a list of its tables was returning an unexpected empty response (null) instead of the actual list. Because the app didn't get the list it expected, it couldn't confirm the tables were there, threw an error, and failed to load any calendar data.

The specific error was:
```
Error initializing database: [TypeError: Cannot convert null value to object]
```

This occurred when using the `execAsync` method to query the list of tables:
```typescript
const result = await this.db.execAsync(`SELECT name FROM sqlite_master WHERE type='table'`);
const tables = result[0].rows; // Error: Cannot convert null value to object
```

### Solution
The solution was to change the command used to query the database tables. Instead of using the general-purpose `execAsync` command, we switched to using `getAllAsync` which is specifically designed for retrieving lists of data from the database:

```typescript
const tables = await this.db.getAllAsync(`SELECT name FROM sqlite_master WHERE type='table'`);
```

This command successfully returned the list of tables, allowing the app to verify everything was set up correctly and proceed with loading the calendar data without errors.

### Root Cause Analysis
The `execAsync` method in the expo-sqlite library (version 15.1.4) appears to have issues with certain types of queries, particularly when querying metadata tables like `sqlite_master`. The `getAllAsync` method provides a more reliable way to retrieve this information.

### Prevention
When querying SQLite metadata tables or when retrieving lists of data, prefer using the `getAllAsync` method over the `execAsync` method. The `execAsync` method should primarily be used for executing SQL statements that don't return data or for batch operations.
