# Handling Recurring Reminders After Device Restart

## Overview

This document outlines a comprehensive solution for ensuring that recurring reminders in the Odia Calendar app persist and function correctly after device restarts. The current implementation has a limitation where notifications might not be rescheduled if the user doesn't open the app after a device restart.

## Current Implementation

The app currently uses the following approach for handling reminders:

1. **App Startup Initialization**:
   - When the app launches, `reminderService.initialize()` is called
   - This reschedules all active reminders by querying the database
   - The app uses `expo-task-manager` for task definition and execution

2. **Background Task Implementation**:
   - The app attempts to use `expo-background-task` module for periodic rescheduling
   - This task is registered with a minimum interval of 60 minutes
   - The task reschedules all active reminders when it runs

3. **Limitations**:
   - The background task only runs if the app has been launched at least once after device restart
   - There's no mechanism to automatically start the app after device restart
   - Users might miss notifications if they don't open the app after restarting their device

## Proposed Solution

To ensure reminders persist after device restarts, we recommend implementing a comprehensive solution that combines several approaches:

### 1. Implement a Boot Receiver (Android)

Add a native Android boot receiver that will trigger the app to reschedule notifications when the device restarts:

```kotlin
// Android implementation in a new file: android/app/src/main/java/com/kalingatech/odia/simplecalendar/BootReceiver.kt
package com.kalingatech.odia.simplecalendar

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

class BootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED || 
            intent.action == Intent.ACTION_MY_PACKAGE_REPLACED ||
            intent.action == "android.intent.action.QUICKBOOT_POWERON") {
            
            Log.d("OdiaCalendar", "Boot completed, starting reminder service")
            
            // Start the app's main activity with a special flag to reschedule reminders
            val launchIntent = Intent(context, MainActivity::class.java)
            launchIntent.putExtra("RESCHEDULE_REMINDERS", true)
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            
            // For Android 12+ we need to use a foreground service
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Start a foreground service instead
                val serviceIntent = Intent(context, ReminderRescheduleService::class.java)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(serviceIntent)
                } else {
                    context.startService(serviceIntent)
                }
            } else {
                // For older Android versions, we can directly start the activity
                context.startActivity(launchIntent)
            }
        }
    }
}
```

### 2. Create a Foreground Service for Android 12+ (Android)

For Android 12 and above, implement a foreground service to handle rescheduling:

```kotlin
// Android implementation in a new file: android/app/src/main/java/com/kalingatech/odia/simplecalendar/ReminderRescheduleService.kt
package com.kalingatech.odia.simplecalendar

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.facebook.react.ReactApplication
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactContext
import com.facebook.react.modules.core.DeviceEventManagerModule

class ReminderRescheduleService : Service() {
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onCreate() {
        super.onCreate()
        
        // Create notification channel for foreground service
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                "reminder_reschedule_channel",
                "Reminder Reschedule",
                NotificationManager.IMPORTANCE_LOW
            )
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
        
        // Create a notification for the foreground service
        val notification = NotificationCompat.Builder(this, "reminder_reschedule_channel")
            .setContentTitle("Odia Calendar")
            .setContentText("Rescheduling reminders...")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
            
        // Start as foreground service
        startForeground(1001, notification)
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // Emit an event to the JS layer to reschedule reminders
        emitRescheduleEvent()
        
        // Stop the service after a short delay
        stopSelf()
        
        return START_NOT_STICKY
    }
    
    private fun emitRescheduleEvent() {
        try {
            val reactApplication = application as ReactApplication
            val reactContext = reactApplication.reactNativeHost.reactInstanceManager.currentReactContext
            
            if (reactContext != null) {
                reactContext
                    .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                    .emit("rescheduleRemindersAfterBoot", Arguments.createMap())
            }
        } catch (e: Exception) {
            // If React context is not ready, we'll try again later
            // The app will reschedule reminders when it's launched normally
        }
    }
}
```

### 3. Update AndroidManifest.xml

Add the necessary permissions and register the boot receiver:

```xml
<!-- Add to AndroidManifest.xml -->
<manifest ...>
    <!-- Add these permissions -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    
    <application ...>
        <!-- Register the boot receiver -->
        <receiver
            android:name=".BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        
        <!-- Register the foreground service -->
        <service
            android:name=".ReminderRescheduleService"
            android:enabled="true"
            android:exported="false" />
    </application>
</manifest>
```

### 4. Implement Background App Refresh (iOS)

For iOS, implement background app refresh capabilities:

```objective-c
// Add to AppDelegate.mm
#import <BackgroundTasks/BackgroundTasks.h>

// Add this method to the AppDelegate implementation
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  // Existing code...
  
  // Register for background refresh
  if (@available(iOS 13.0, *)) {
    [[BGTaskScheduler sharedScheduler] registerForTaskWithIdentifier:@"com.kalingatech.odia.simplecalendar.reminderrefresh"
                                                          usingQueue:dispatch_get_main_queue()
                                                       launchHandler:^(BGTask * _Nonnull task) {
      // This will be handled by the JS layer
      [self handleAppRefreshTask:task];
    }];
  }
  
  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

// Add this method to handle background refresh
- (void)handleAppRefreshTask:(BGTask *)task API_AVAILABLE(ios(13.0)) {
  // Create a task assertion to prevent the app from being suspended
  UIBackgroundTaskIdentifier bgTask = [[UIApplication sharedApplication] beginBackgroundTaskWithName:@"ReminderRefresh" expirationHandler:^{
    [[UIApplication sharedApplication] endBackgroundTask:bgTask];
  }];
  
  // Schedule the next refresh
  [self scheduleAppRefresh];
  
  // Notify JS layer to reschedule reminders
  [[NSNotificationCenter defaultCenter] postNotificationName:@"RescheduleRemindersNotification" object:nil];
  
  // Set a timer to ensure we complete the task
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    [task setTaskCompletedWithSuccess:YES];
    [[UIApplication sharedApplication] endBackgroundTask:bgTask];
  });
}

// Add this method to schedule the next refresh
- (void)scheduleAppRefresh API_AVAILABLE(ios(13.0)) {
  BGAppRefreshTaskRequest *request = [[BGAppRefreshTaskRequest alloc] initWithIdentifier:@"com.kalingatech.odia.simplecalendar.reminderrefresh"];
  request.earliestBeginDate = [NSDate dateWithTimeIntervalSinceNow:3600]; // 1 hour
  
  NSError *error = nil;
  [[BGTaskScheduler sharedScheduler] submitTaskRequest:request error:&error];
  if (error) {
    NSLog(@"Could not schedule app refresh: %@", error);
  }
}
```

### 5. Create a Native Module Bridge

Create a native module to handle the communication between native code and React Native:

```typescript
// src/native/ReminderRescheduleModule.ts
import { NativeEventEmitter, NativeModules, Platform } from 'react-native';
import { reminderService } from '@/services/reminder-service';
import { logger } from '@/utils/logging-sentry';

// This will be populated by the native side
const ReminderRescheduleModule = NativeModules.ReminderRescheduleModule || {};

// Create an event emitter for Android events
const eventEmitter = new NativeEventEmitter(ReminderRescheduleModule);

// Listen for reschedule events from native code
if (Platform.OS === 'android') {
  eventEmitter.addListener('rescheduleRemindersAfterBoot', async () => {
    logger.info('Received reschedule event from native Android module');
    await handleRescheduleReminders();
  });
}

// For iOS, we'll use the notification center
if (Platform.OS === 'ios') {
  // This will be set up in the AppDelegate
  const { RNNotificationCenter } = NativeModules;
  if (RNNotificationCenter) {
    RNNotificationCenter.addListener('RescheduleRemindersNotification', async () => {
      logger.info('Received reschedule event from native iOS module');
      await handleRescheduleReminders();
    });
  }
}

// Common handler for both platforms
async function handleRescheduleReminders() {
  try {
    logger.info('Rescheduling reminders after device restart');
    await reminderService.initialize();
    logger.info('Successfully rescheduled reminders after device restart');
  } catch (error) {
    logger.error('Failed to reschedule reminders after device restart', { error });
  }
}

export default {
  // Expose any methods that might be needed
  manuallyRescheduleReminders: handleRescheduleReminders
};
```

## Implementation Considerations

1. **Permissions**: The solution requires additional permissions which should be properly explained to users.

2. **Battery Optimization**: On some Android devices, battery optimization might still prevent the boot receiver from working. Consider adding guidance for users to disable battery optimization for the app.

3. **Testing**: Thoroughly test on various device models and OS versions, as boot receiver behavior can vary.

4. **Gradual Rollout**: Consider implementing this as part of a phased rollout to monitor any potential issues.

5. **User Education**: Add a note in the app explaining that for reliable reminders, users should either open the app after restart or disable battery optimization.

## Benefits of This Solution

1. **Comprehensive Coverage**: Handles device restarts on both Android and iOS platforms.

2. **Multiple Fallback Mechanisms**: If one approach fails, others can still ensure reminders are rescheduled.

3. **Battery-Efficient**: Uses system-provided mechanisms rather than custom solutions that might drain battery.

4. **Modern Implementation**: Uses the latest APIs available on both platforms.

5. **Robust Error Handling**: Includes proper logging and error recovery at each step.

6. **Minimal User Impact**: Works silently in the background without requiring user intervention.

## Dependencies

To implement this solution, the following dependencies are required:

1. `expo-task-manager` - For task definition and execution
2. `expo-background-task` - For background task scheduling
3. Native Android and iOS code for boot receivers and background tasks

## Conclusion

This comprehensive approach ensures that reminders will persist and function correctly after device restarts, providing a reliable user experience. By implementing multiple mechanisms with proper fallbacks, the solution is robust against various platform-specific limitations and restrictions.
