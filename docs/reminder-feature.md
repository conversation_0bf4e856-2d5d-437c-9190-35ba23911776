# Odia Calendar Lite - Reminder Feature Documentation

## Overview

The reminder feature allows users to set notifications based on the Odia calendar system (tithi, paksha, month) rather than the Gregorian calendar. This culturally relevant feature enables users to receive notifications for important Odia dates and recurring events.

## Key Features

- **Tithi-Based Reminders**: Set reminders based on Odia tithis (lunar days)
- **Multiple Reminder Types**:
  - Monthly tithi reminders (e.g., every Krishna Ekadashi)
  - Yearly tithi reminders (e.g., Baishakha Shukla Purnima every year)
- **Customizable Notifications**: Set preferred notification time and sound
- **Recurring Reminders**: Automatically schedule future occurrences
- **Reminder Management**: View, edit, and delete reminders

## Technical Implementation

### Database Schema

The reminder feature uses two tables in the SQLite database:

#### 1. `user_reminders` Table

Stores the reminder patterns and user preferences:

```sql
CREATE TABLE IF NOT EXISTS user_reminders (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  description TEXT,
  reminder_type TEXT NOT NULL, -- 'monthly_tithi', 'yearly_tithi', 'specific_date'
  odia_month TEXT, -- For yearly reminders
  paksha TEXT, -- '<PERSON><PERSON>' or 'Krishna'
  tithi TEXT, -- The tithi name
  is_recurring BOOLEAN NOT NULL DEFAULT 0,
  recurrence_interval INTEGER, -- For recurring reminders (e.g., every month)
  notification_time TEXT NOT NULL DEFAULT '08:00', -- Time of day for the notification in 24h format
  sound_name TEXT DEFAULT 'default', -- Name of the notification sound
  is_enabled BOOLEAN NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. `scheduled_notifications` Table

Tracks scheduled notifications and their status:

```sql
CREATE TABLE IF NOT EXISTS scheduled_notifications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  reminder_id INTEGER NOT NULL,
  notification_id TEXT NOT NULL, -- ID returned by the notification system
  scheduled_date TEXT NOT NULL, -- ISO date string
  status TEXT NOT NULL DEFAULT 'scheduled', -- 'scheduled', 'delivered', 'cancelled'
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (reminder_id) REFERENCES user_reminders(id) ON DELETE CASCADE
);
```

### Core Services

#### 1. Reminder Service (`src/services/reminder-service.ts`)

Manages reminder operations:
- Creating, updating, and deleting reminders
- Scheduling notifications for reminders
- Retrieving reminders and their next occurrences

#### 2. Notification Service (`src/services/notification-service.ts`)

Handles notification operations:
- Requesting notification permissions
- Scheduling and canceling notifications
- Handling notification delivery and user interactions

#### 3. Tithi Calculator (`src/utils/tithi-calculator.ts`)

Calculates tithi occurrences:
- Finding the next occurrence of a specific tithi
- Finding all occurrences of a tithi within a date range
- Converting between Odia and Gregorian dates

### UI Components

#### 1. Reminders Tab (`app/(tabs)/reminders.tsx`)

The main screen for the reminders feature, accessible from the bottom tab navigation.

#### 2. Reminders List (`src/components/reminders/RemindersList.tsx`)

Displays all user reminders with options to add, edit, and delete.

#### 3. Reminder Item (`src/components/reminders/ReminderItem.tsx`)

Individual reminder component showing title, details, and next occurrence.

#### 4. Add/Edit Reminder Screen (`app/add-reminder.tsx`)

Form for creating and editing reminders with fields for:
- Title and description
- Reminder type (monthly or yearly)
- Odia month, paksha, and tithi
- Notification time and sound
- Recurring options

#### 5. Reminder Details Screen (`app/reminder-details.tsx`)

Detailed view of a reminder showing:
- Reminder information
- Next occurrence date
- Options to edit or delete

#### 6. Tithi Picker (`src/components/reminders/TithiPicker.tsx`)

Custom picker component for selecting Odia months, pakshas, and tithis.

## Notification Mechanism

### Scheduling Process

1. When a reminder is created or updated:
   - The app checks for existing scheduled notifications to prevent duplicates
   - It calculates only the needed number of occurrences (up to MAX_SCHEDULED_OCCURRENCES, typically 4)
   - For each occurrence, it validates there's no existing notification for that date
   - It schedules notifications with the device's notification system using proper validation
   - It stores the notification IDs in the `scheduled_notifications` table with status 'scheduled'
   - Structured logging tracks the entire scheduling process with consistent event naming
   - The initial scheduling includes today's date using `>=` comparison to find occurrences

2. When a notification is delivered:
   - The app marks it as 'delivered' in the database using proper column aliases
   - The UI updates to show the next upcoming occurrence (excluding delivered notifications)
   - For recurring reminders, it calculates and schedules the next occurrence
   - It uses strict `>` comparison to find occurrences after the last scheduled date
   - Before scheduling, it checks if a notification already exists for that date to prevent duplicates
   - The notification lifecycle is tracked through structured logging events

3. During app startup and background tasks:
   - The app reschedules all active reminders
   - It maintains the rolling window of up to 4 future notifications
   - It uses the same validation and duplicate prevention mechanisms
   - Background tasks run every 6 hours to ensure notifications stay up-to-date

### Notification Lifecycle Events

The system tracks the following notification lifecycle events:
- `scheduled`: When a notification is initially scheduled
- `received`: When a notification is received by the device
- `delivered`: When a notification is marked as delivered in the database
- `user_response`: When a user responds to a notification
- `occurrences_found`: When occurrences are found for a reminder
- `next_occurrence_scheduled`: When a new occurrence is scheduled after delivery

### Handling Device Restarts

When the app starts:
- It initializes the reminder service
- It checks for active reminders and reschedules their notifications
- This ensures reminders work even after device restarts

## Edge Cases and Error Handling

### Missing Panchang Data

If panchang data for future dates is not available:
- The app schedules reminders for known occurrences
- When new data becomes available, it schedules additional occurrences
- The app provides clear feedback to users about data availability:
  - Explains that future data is typically released in Q4 of each year
  - Shows a message when no occurrences are found due to missing data
  - Offers a download button when next year's data is available but not yet downloaded
- The system checks if the lack of occurrences is due to missing data rather than an invalid combination

### Tithi Spanning Multiple Days

When a tithi spans midnight:
- The app uses the primary day of the tithi (when it's in effect for the majority of the day)
- This ensures consistent notification delivery

### Combined Tithis

When a day has multiple tithis (e.g., "ସପ୍ତମୀ ଅଷ୍ଟମୀକ୍ଷ" - Saptami Ashtamiksha):
- The app uses SQL LIKE queries with wildcards (`%tithi%`) instead of exact matching
- This ensures reminders trigger even when the tithi is part of a combined tithi name
- All tithi-related queries in `tithi-calculator.ts` use this approach for consistent behavior
- This implementation handles cases where a user selects "ସପ୍ତମୀ" but the day has "ସପ୍ତମୀ ଅଷ୍ଟମୀକ୍ଷ"

### Permission Handling

If notification permissions are denied:
- The app stores reminders but informs the user that notifications won't be delivered
- It provides guidance on how to enable permissions in device settings

### Null or Invalid Notification IDs

When the notification system returns null or invalid IDs:
- The app validates notification IDs before storing them in the database
- It logs detailed information about invalid IDs for debugging
- It skips database insertion for invalid IDs to maintain data integrity
- The transaction system ensures database consistency even if some notifications fail

### Validation of Reminder Inputs

To prevent users from creating reminders with invalid combinations:
- The app validates that required fields are filled (title, paksha, tithi, etc.)
- Before saving a reminder, it checks if the combination of month, paksha, and tithi exists in the panchang data
- If the combination doesn't exist, it shows a clear error message explaining the issue
- This prevents users from creating reminders that would never trigger
- The validation is performed using a database query that checks for the existence of the combination
- The validation uses LIKE queries for tithi_name to handle combined tithis (e.g., "ସପ୍ତମୀ ଅଷ୍ଟମୀକ୍ଷ")

### Duplicate Notifications Prevention

To prevent duplicate notifications:
- The app checks for existing scheduled notifications before scheduling new ones
- It only schedules the number of notifications needed to reach the maximum (typically 4)
- For each occurrence date, it verifies no notification already exists for that date
- When scheduling the next occurrence after delivery, it checks for existing notifications
- Detailed logging tracks the number of existing and newly scheduled notifications

## Performance Considerations

### Database Optimization

- Indexes on frequently queried columns
- Efficient query patterns for tithi lookups
- Minimal data storage (patterns rather than individual occurrences)
- Optimized next occurrence query that joins with panchang_data and excludes delivered notifications:

```sql
SELECT
  sn.id,
  sn.reminder_id AS reminderId,
  sn.notification_id AS notificationId,
  sn.scheduled_date AS scheduledDate,
  sn.status,
  pd.odia_date AS odiaDate,
  pd.odia_month AS odiaMonth,
  pd.paksha,
  pd.tithi_name AS tithi
FROM scheduled_notifications sn
JOIN panchang_data pd ON DATE(sn.scheduled_date) = pd.eng_date
WHERE sn.reminder_id = ?
AND sn.status = 'scheduled'
AND sn.scheduled_date >= ?
ORDER BY sn.scheduled_date ASC
LIMIT 1
```

### Battery Usage

- Limited lookahead for scheduling (typically 4 occurrences)
- Batch processing of notifications
- No continuous background processing
- Efficient notification lifecycle management with proper cleanup

## Debugging and Monitoring

### Structured Logging

The reminder feature implements a comprehensive structured logging system to track the notification lifecycle:

- **Consistent Event Naming**: All notification-related logs use the `Notification lifecycle` pattern with an `event` field
- **Contextual Information**: Logs include reminder ID, title, notification ID, and scheduled date
- **Error Handling**: Robust error logging with proper context for debugging
- **Log Levels**: Appropriate log levels (info for lifecycle events, debug for details)
- **Duplicate Prevention**: Logs track existing notifications and skipped duplicates
- **Transaction Status**: Logs include transaction start, commit, and rollback events
- **Validation Results**: Logs detail notification ID validation results

Example log entries:
```
[INFO] Notification lifecycle {"event":"scheduled","reminderId":3,"notificationId":"14999f67-12d0-4b1b-90a3-ba8e21ecf8e6","date":"2025-05-15T19:43:00.000Z","title":"Monthly Krishna Ekadashi","reminderType":"monthly_tithi"}

[DEBUG] Checking existing scheduled notifications {"reminderId":1,"existingCount":2}

[INFO] Notification scheduling summary {"reminderId":1,"existingCount":2,"newTotal":4,"newSuccess":2,"newFailures":0}

[DEBUG] Transaction committed {"app":"OdiaCalendarLite"}
```

### SQL Query Executor (Development Only)

A SQL query execution feature is available in the debug panel during development:

- Allows developers to execute SQL queries against the app's SQLite database
- Results are logged to the console for analysis
- Includes proper error handling for invalid queries
- Only available in development builds, never in production

## Future Enhancements

1. **Sync Across Devices**: Add cloud synchronization for reminders
2. **Additional Reminder Types**: Support for nakshatra, yoga, and other Odia calendar elements
3. **Custom Sound Library**: Add more culturally relevant notification sounds
4. **Smart Suggestions**: Suggest common reminders based on cultural significance
5. **Calendar Integration**: Show reminder indicators on the main calendar view
6. **Enhanced Notification Interactions**: Implement actions directly from notifications
7. **Improved Error Recovery**: Add more robust recovery mechanisms for notification delivery failures
