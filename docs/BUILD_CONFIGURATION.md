# Build Configuration Guide for Odia Calendar Lite

This document outlines the specific build configuration requirements for the Odia Calendar Lite app. It serves as a reference for developers to ensure consistent and successful builds across development and production environments.

## Critical Version Requirements

The following version requirements must be strictly followed to ensure successful builds:

### Kotlin Version

- **Required Version**: 1.9.25
- **Location**: Defined in `android/build.gradle`
- **Warning**: Do not downgrade this version as it's required for compatibility with Google Mobile Ads SDK

```gradle
kotlinVersion = findProperty('android.kotlinVersion') ?: '1.9.25'
```

### React Native Google Mobile Ads

- **Required Version**: 14.1.0 (or compatible)
- **Warning**: Do not upgrade or downgrade this package without thorough testing
- **Reason**: Specific version compatibility with Expo SDK 52 and Kotlin 1.9.25

### Gradle Version

- **Required Version**: 8.10.2
- **Location**: Defined in `android/gradle/wrapper/gradle-wrapper.properties`
- **Warning**: Using incorrect Gradle versions can cause build failures

## Keystore Configuration

### Development Builds

Development builds use the default debug keystore:

```gradle
debug {
    storeFile file('debug.keystore')
    storePassword 'android'
    keyAlias 'androiddebugkey'
    keyPassword 'android'
}
```

### Production Builds

Production builds require the release keystore:

1. **Keystore Location**: `android/keystore/release.keystore`
2. **Configuration**: Defined in `android/app/build.gradle`

```gradle
release {
    storeFile file("../keystore/release.keystore")
    storePassword MYAPP_RELEASE_STORE_PASSWORD
    keyAlias MYAPP_RELEASE_KEY_ALIAS
    keyPassword MYAPP_RELEASE_KEY_PASSWORD
}
```

3. **Credentials**: Stored in `android/local.properties` (not in version control)

```properties
MYAPP_RELEASE_STORE_FILE=keystore/release.keystore
MYAPP_RELEASE_KEY_ALIAS=964ca1772ff3b625e83a59b6e3d2c5c9
MYAPP_RELEASE_STORE_PASSWORD=ee222ca012690b4d6924197a3ebb4b9c
MYAPP_RELEASE_KEY_PASSWORD=4eb2c0fed8b2e1fa987b7bac2a8e28c9
```

## AdMob Configuration

### App ID

- **Android**: `ca-app-pub-7418777810665263~**********`
- **iOS**: `ca-app-pub-7418777810665263~**********`

### Banner Ad Units

- **Sticky Ad**: `ca-app-pub-7418777810665263/3515971501`
- **Calendar Screen**: `ca-app-pub-7418777810665263/9367914045`
- **Settings Screen**: `ca-app-pub-7418777810665263/1972849181`
- **Date Details Modal**: `ca-app-pub-7418777810665263/3285930857`

### Implementation Details

1. **AndroidManifest.xml**:
   ```xml
   <meta-data android:name="com.google.android.gms.ads.APPLICATION_ID" android:value="ca-app-pub-7418777810665263~**********" tools:replace="android:value"/>
   <meta-data android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT" android:value="true" tools:replace="android:value"/>
   <meta-data android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING" android:value="true" tools:replace="android:value"/>
   <meta-data android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION" android:value="true" tools:replace="android:value"/>
   ```

2. **app.json**:
   ```json
   "plugins": [
     [
       "react-native-google-mobile-ads",
       {
         "androidAppId": "ca-app-pub-7418777810665263~**********",
         "iosAppId": "ca-app-pub-7418777810665263~**********",
         "userTrackingPermission": "This identifier will be used to deliver personalized ads to you."
       }
     ]
   ]
   ```

3. **AdMobProvider.tsx**:
   - Test ad units are only used in development mode or Expo Go
   - Production builds always use real ad units from appConfig

## Build Commands

### Development Build

```bash
# Clean the project
cd android && ./gradlew clean

# Build debug APK
./gradlew assembleDebug

# Install on connected device/emulator
adb install -r ./app/build/outputs/apk/debug/app-debug.apk
```

### Production Build

#### APK (for testing)

```bash
# Clean the project
cd android && ./gradlew clean

# Build release APK
./gradlew assembleRelease

# Install on connected device/emulator
adb install -r ./app/build/outputs/apk/release/app-release.apk
```

#### AAB (for Play Store)

```bash
# Clean the project
cd android && ./gradlew clean

# Build release bundle
./gradlew bundleRelease
```

## Common Build Issues and Solutions

### 1. Manifest Merger Conflicts

**Issue**: Duplicate entries in AndroidManifest.xml, particularly with AdMob configuration.

**Solution**: Add `tools:replace="android:value"` to the conflicting meta-data entries:

```xml
<meta-data android:name="com.google.android.gms.ads.APPLICATION_ID" android:value="ca-app-pub-7418777810665263~**********" tools:replace="android:value"/>
```

### 2. Kotlin Version Incompatibility

**Issue**: Incompatibility between Kotlin version and Google Play Services Ads.

**Solution**: Maintain Kotlin version 1.9.25 as specified in this document.

### 3. Gradle Distribution Not Found

**Issue**: Unable to download Gradle distribution.

**Solution**: Ensure the Gradle wrapper properties file has a valid distribution URL:

```properties
distributionUrl=https\://services.gradle.org/distributions/gradle-8.10.2-all.zip
```

### 4. Keystore Access Issues

**Issue**: Unable to access keystore or incorrect keystore credentials.

**Solution**: 
1. Ensure the keystore file exists at the specified location
2. Verify the credentials in local.properties match the keystore
3. Use absolute paths if relative paths don't work

## Important Notes

1. **Never modify AdMob-related package versions** without thorough testing
2. **Always maintain the Kotlin version at 1.9.25** for compatibility
3. **Keep the release keystore secure** and ensure credentials are available for builds
4. **Test production builds on real devices** before submission to the Play Store
5. **Verify ad units are working correctly** in production builds
