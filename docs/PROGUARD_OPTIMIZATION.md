# ProGuard Optimization Results

This document tracks the size optimization achieved through ProGuard/R8 in our release builds.

## Configuration

ProGuard is enabled in our release builds with the following settings in `android/app/build.gradle`:

```gradle
release {
    signingConfig signingConfigs.release
    shrinkResources true  // Enable resource shrinking
    minifyEnabled true    // Enable code shrinking/obfuscation
    proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
}
```

Our custom ProGuard rules are defined in `android/app/proguard-rules.pro`.

## Size Comparison

| Version | Without ProGuard | With ProGuard | Reduction | Reduction % |
|---------|-----------------|--------------|-----------|-------------|
| 1.0.0   | TBD             | TBD          | TBD       | TBD         |

## Notes

- ProGuard not only reduces APK size but also makes reverse engineering more difficult.
- The size reduction varies depending on the libraries used and the complexity of the app.
- Some size reduction comes from removing unused code (dead code elimination).
- Additional reduction comes from optimizing the remaining code.
- Resource shrinking removes unused resources.

## How to Measure

To measure the APK size with and without ProGuard:

1. **Without ProGuard**:
   ```
   cd android
   ./gradlew assembleRelease -PminifyEnabled=false -PshrinkResources=false
   ```

2. **With ProGuard**:
   ```
   cd android
   ./gradlew assembleRelease
   ```

3. Check the size of the APK files in:
   ```
   android/app/build/outputs/apk/release/
   ```

## Troubleshooting

If you encounter crashes in the release build but not in debug, it's likely due to ProGuard rules. Common issues:

1. **Native methods being stripped**: Add `-keepclasseswithmembernames class * { native <methods>; }`
2. **Reflection issues**: Add `-keep class com.yourpackage.YourClass { *; }`
3. **Third-party library issues**: Check the library's documentation for ProGuard rules

Add specific rules to `proguard-rules.pro` to resolve these issues.
