# Notification Permission Handling in Odia Calendar Lite

## Overview

This document outlines the implementation of notification permission handling in the Odia Calendar Lite app. The solution provides a user-friendly way to request and manage notification permissions, ensuring users understand why permissions are needed and how to enable them if they've been denied.

## Implementation Details

### 1. Notification Service Methods

The notification service (`src/services/notification-service.ts`) has been enhanced with the following methods:

- `getPermissionStatus()`: Checks the current notification permission status
- `requestPermissions()`: Requests notification permissions from the user
- `openNotificationSettings()`: Opens the device settings to allow users to enable notifications

These methods provide a consistent API for handling permissions across the app.

### 2. NotificationPermissionAlert Component

A new component (`src/components/reminders/NotificationPermissionAlert.tsx`) has been created to handle notification permission alerts. This component:

- Shows a modal when notification permissions are denied
- Provides options to request permissions or open settings
- Can be configured to show only when permissions are explicitly denied (not just undetermined)
- Supports both English and Odia languages

### 3. Integration Points

The permission handling has been integrated at key points in the app:

#### Reminders Screen

The main reminders screen (`app/(tabs)/reminders.tsx`) checks for notification permissions when it loads and shows the permission alert if permissions are denied.

```typescript
// Check notification permissions when the screen loads
useEffect(() => {
  const checkPermissions = async () => {
    const status = await notificationService.getPermissionStatus();
    setPermissionGranted(status?.granted || false);
  };

  checkPermissions();
}, []);

// Show permission alert if permissions are not granted
{permissionGranted === false && (
  <NotificationPermissionAlert
    onPermissionChange={handlePermissionChange}
    showOnlyIfDenied={false}
  />
)}
```

#### Add/Edit Reminder Screen

The add/edit reminder screen (`app/add-reminder.tsx`) checks for notification permissions when saving a reminder and shows a warning if permissions are denied.

```typescript
// Check notification permissions
const permissionStatus = await notificationService.getPermissionStatus();
if (!permissionStatus?.granted) {
  // Show a warning but allow the user to continue
  Alert.alert(
    t.notificationPermissionRequired,
    t.notificationPermissionMessage,
    [
      {
        text: t.openSettings,
        onPress: async () => {
          await notificationService.openNotificationSettings();
        },
      },
      {
        text: t.later,
        style: 'cancel',
      },
      {
        text: t.save,
        onPress: () => saveReminder(),
      },
    ]
  );
  return;
}
```

### 4. User Experience

The implementation provides a smooth user experience:

1. **First Launch**: Users are prompted to allow notifications when they first create a reminder
2. **Denied Permissions**: If permissions are denied, users see a clear message explaining why notifications are needed
3. **Settings Access**: Users can easily access device settings to enable notifications if they've been denied
4. **Continued Use**: Users can still create reminders even if they deny notifications, but they're warned that they won't receive alerts

### 5. Translations

Translations for notification permission messages have been added to both English and Odia languages:

```typescript
// English
notificationPermissionRequired: 'Notification permission required for reminders',
enableNotifications: 'Enable Notifications',
notificationPermissionMessage: 'Reminders require notification permissions to work properly. Please enable notifications to receive reminder alerts.',
allowNotifications: 'Allow Notifications',
openSettings: 'Open Settings',
later: 'Later',
notificationPermissionDenied: 'Notification permission denied. You will not receive reminder alerts. You can enable notifications in your device settings.',

// Odia
notificationPermissionRequired: 'ରିମାଇଣ୍ଡର ପାଇଁ ନୋଟିଫିକେସନ୍ ଅନୁମତି ଆବଶ୍ୟକ',
enableNotifications: 'ନୋଟିଫିକେସନ୍ ସକ୍ଷମ କରନ୍ତୁ',
notificationPermissionMessage: 'ରିମାଇଣ୍ଡର ଠିକ୍ ଭାବରେ କାମ କରିବା ପାଇଁ ନୋଟିଫିକେସନ୍ ଅନୁମତି ଆବଶ୍ୟକ। ରିମାଇଣ୍ଡର ସୂଚନା ପାଇବା ପାଇଁ ଦୟାକରି ନୋଟିଫିକେସନ୍ ସକ୍ଷମ କରନ୍ତୁ।',
allowNotifications: 'ନୋଟିଫିକେସନ୍ ଅନୁମତି ଦିଅନ୍ତୁ',
openSettings: 'ସେଟିଂସ୍ ଖୋଲନ୍ତୁ',
later: 'ପରେ',
notificationPermissionDenied: 'ନୋଟିଫିକେସନ୍ ଅନୁମତି ଅସ୍ୱୀକୃତ। ଆପଣ ରିମାଇଣ୍ଡର ସୂଚନା ପାଇବେ ନାହିଁ। ଆପଣ ଆପଣଙ୍କ ଡିଭାଇସ୍ ସେଟିଂସରେ ନୋଟିଫିକେସନ୍ ସକ୍ଷମ କରିପାରିବେ।',
```

## Technical Implementation

### AsyncStorage for Permission Status

The app stores the notification permission status in AsyncStorage to avoid repeatedly requesting permissions:

```typescript
// Storage keys
const NOTIFICATION_PERMISSION_KEY = 'odia_calendar_notification_permission';

// Store the permission status
await AsyncStorage.setItem(NOTIFICATION_PERMISSION_KEY, JSON.stringify(this.permissionStatus));
```

### Platform-Specific Settings Access

The implementation handles platform differences when opening settings:

```typescript
async openNotificationSettings(): Promise<void> {
  try {
    if (Platform.OS === 'ios') {
      await Linking.openURL('app-settings:');
    } else {
      await Linking.openSettings();
    }
  } catch (error) {
    logger.error('Error opening notification settings', { error });
  }
}
```

## Benefits

This implementation provides several benefits:

1. **Improved User Experience**: Clear guidance on why permissions are needed
2. **Reduced Confusion**: Users understand why they might not receive notifications
3. **Increased Permission Grants**: The clear explanation increases the likelihood of users granting permissions
4. **Consistent Behavior**: The same permission handling logic is used throughout the app

## Future Enhancements

Potential future enhancements to the notification permission handling:

1. **Permission Re-prompting**: Periodically re-prompt users who have denied permissions
2. **Usage Statistics**: Track permission grant rates to optimize the permission request flow
3. **Enhanced Guidance**: Add visual guides showing how to enable permissions in different device settings
4. **Notification Testing**: Add a "Test Notification" feature to verify permissions are working correctly
5. **Settings Return Detection**: Implement AppState listeners to detect when users return from device settings after enabling notifications
