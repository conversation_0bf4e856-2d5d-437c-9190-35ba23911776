# Odia Calendar Lite - Implementation Checklist

This document outlines the actionable items needed to complete the Odia Calendar Lite app based on the existing codebase and requirements.

## Current State Analysis

### What's Already Implemented

1. **UI Components**:
   - ✅ Basic calendar grid with date cells
   - ✅ Month navigation
   - ✅ Date details modal
   - ✅ Festival list component
   - ✅ Marriage dates list component
   - ✅ Theme switching (light/dark)
   - ✅ Language switching (English/Odia)
   - ✅ Moon phase indicators (Purnima/Amavasya)
   - ✅ Month/Year picker
   - ✅ Odia digit conversion for numerical values
   - ✅ Reminder management UI (list, add/edit, details)

2. **State Management**:
   - ✅ Settings store (theme, language)
   - ✅ Calendar store (selected date, month, year)
   - ✅ Reminder store (reminders, notifications)
   - ✅ Persistence using AsyncStorage

3. **Navigation**:
   - ✅ Basic tab navigation
   - ✅ Modal navigation for date details
   - ✅ Reminder screens navigation

4. **Database Integration**:
   - ✅ SQLite database with pre-bundled data
   - ✅ Odia date information from database
   - ✅ Festival data from database
   - ✅ Marriage dates from database
   - ✅ Brata Ghara dates from database
   - ✅ Reminder data storage and retrieval

### What's Missing

1. **UI Enhancements**:
   - ✅ Brata Ghara dates indicator (in Date Details Modal)
   - ❌ Brata Ghara dates indicator (in Calendar Grid)
   - ✅ Date Details Modal UI enhanced with more data & structure
   - ❌ Optimized calendar grid for readability
   - ✅ Animations for month transitions (swipe navigation)
   - ❌ Share functionality for date details

2. **Performance Optimization**:
   - ✅ Component memoization with React.memo()
   - ✅ Event handler optimization with useCallback()
   - ✅ Expensive calculations optimization with useMemo()
   - ✅ FlatList rendering optimization
   - ✅ Proper dependency specification in hooks
   - ✅ Performance measurement system
   - ✅ Startup time measurement and optimization
   - ✅ In-memory caching with TTL
   - ✅ Adjacent month prefetching
   - ❌ Additional memory usage optimization
   - ❌ Battery usage optimization
   - ❌ Database query optimization with indexes

3. **Testing & Finalization**:
   - ❌ Comprehensive testing on various devices
   - ❌ Testing offline functionality
   - ❌ Testing edge cases (data corruption, etc.)
   - ❌ Final code review and cleanup

## Implementation Tasks

### Phase 1: Database Setup

1. **SQLite Integration**:
   - [x] Create a database service for initializing SQLite
   - [x] Implement pre-bundled database loading
   - [x] Create database provider component
   - [x] Implement database context hooks

2. **Database Schema**: ✅ COMPLETED
   - [x] Implement panchang_data table structure
   - [x] Implement calendar_year_versions table (creation handled in `database-service.ts`)
   - [x] Implement app_metadata table (implemented in `database-service.ts`)
   - [x] Implement user_reminders table for storing reminder patterns
   - [x] Implement scheduled_notifications table for tracking notification status
   - [ ] Create necessary indexes for performance (pending optimization)
   - [x] Store pre-formatted Odia and English text for all display fields
   - [x] Implement schema versioning using `PRAGMA user_version`
   - [x] Implement basic migration runner logic in `database-service.ts`

3. **Data Access Layer**:
   - [x] Create calendar data service
   - [x] Implement functions to query panchang data
   - [x] Implement functions to query festivals
   - [x] Implement functions to query marriage dates
   - [x] Implement functions to query Brata Ghara dates (implicitly handled by `getDateData` returning full object)
   - [x] Implement reminder service for CRUD operations on reminders
   - [x] Implement notification service for scheduling notifications
   - [x] Implement tithi calculator for finding tithi occurrences

### Phase 2: Replace Mock Data with Real Database

1. **Calendar Components**:
   - [x] Update CalendarGrid to use database instead of mock data
   - [x] Update DateDetailsModal to use database instead of mock data (use pre-formatted Odia text directly)
   - [x] Update FestivalsList to use database instead of mock data (use pre-formatted Odia text directly)
   - [x] Update MarriageDatesList to use database instead of mock data (use pre-formatted Odia text directly)
   - [x] Ensure digit conversion is only applied to dynamically generated numbers, not database content

2. **Data Loading**:
   - [x] Implement efficient data loading strategies
   - [x] Add loading indicators where appropriate
   - [x] Implement error handling for database operations

### Phase 3: Data Synchronization

1. **Supabase Integration**:
   - [x] Set up Supabase client (`supabase-client.ts`)
   - [x] Implement API functions to fetch calendar data (within `sync-service.ts`)
   - [x] Implement version checking mechanism (`sync-service.ts`)
   - [x] Integrate user settings (auto-update, Wi-Fi only) into sync logic

2. **Background Updates**:
   - [x] Create calendar update service (`sync-service.ts`)
   - [x] Implement background fetch functionality (`sync-service.ts`)
   - [x] Implement data synchronization logic (`sync-service.ts`)
   - [x] Implement manual update trigger (`sync-service.ts`)
   - [x] Implement update prompt modal (`UpdatePromptModal.tsx`) and related state management (`calendar-store.ts`)

3. **Year Data Management**:
   - [x] Implement year availability checking (`sync-service.ts`)
   - [x] Create year data download mechanism (`sync-service.ts`)
   - [x] Implement version tracking for updates (`database-service.ts` & `sync-service.ts`)
   - [x] Integrate user settings (auto-update, Wi-Fi only) into sync logic
   - [x] Implement manual update trigger (`sync-service.ts`)
   - [x] Implement update prompt modal (`UpdatePromptModal.tsx`) and related state management (`calendar-store.ts`)

### Phase 4: UI Refinements

1. **Calendar Grid Enhancements**:
   - [ ] Add Brata Ghara date indicators (in Calendar Grid)
   - [x] Optimize calendar grid for readability
   - [x] Implement animations for month transitions (swipe navigation)

2. **Day Detail Screen Improvements**:
   - [x] Add more panchang details (Nakshatra, Yoga, Karana, Timings, etc.) to Date Details Modal
   - [x] Enhance typography and layout (Sections, DetailRow) in Date Details Modal
   - [x] Add Brata Ghara date indicators (in Date Details Modal)
   - [x] Combine Odia date and Panchang sections into one logical block
   - [x] Reorganize content in a more logical sequence
   - [x] Move the banner ad below the combined section
   - [x] Add marriage/brataghara eligibility information to the main section
   - [x] Ensure proper display of all text in Odia when Odia language is selected
   - [x] Add borders to festival, marriage, and brataghara cards in dark mode for better visibility
   - [x] Enhance text visibility with better font weight and text shadows in dark mode
   - [x] Add Odia month display beside the red dot in the header
   - [x] Create utility function to map English months to corresponding Odia months
   - [x] Fix the Odia name for May month (updated from "ମେ" to "ମଇ")
   - [ ] Add share functionality for date details

3. **Settings Screen**:
   - [x] Implement data management options (auto-update, Wi-Fi only)
   - [x] Add manual update trigger button with status feedback & last checked time
   - [x] Add manual in-app review prompt trigger (using `expo-store-review`)
   - [x] Add automatic in-app review prompt logic (based on usage, using `expo-store-review`)
   - [x] Add "Delete Year Data" feature for managing storage
   - [x] Reorganize settings screen for better UX (group related settings, prioritize important ones)
   - [ ] Add about section (if not already covered by existing simple display)

### Phase 5: Configuration Management

1.  **Centralized Configuration**:
    - [x] Create central config file (`src/config/appConfig.ts`)
    - [x] Move review prompt thresholds to config
    - [x] Move store URLs/IDs to config
    - [x] Move share URL to config
    - [x] Move settings screen timeouts to config
    - [x] Refactor code (`_layout.tsx`, `settings.tsx`) to use central config

2.  **Remote Configuration System**:
    - [x] Implement RemoteConfigService for Supabase connection details
    - [x] Create default configuration bundled with the app
    - [x] Implement configuration caching with TTL
    - [x] Add background refresh mechanism
    - [x] Implement version tracking for configuration updates
    - [x] Add support for migrating from Supabase Cloud to self-hosted
    - [x] Create fallback mechanism for offline operation
    - [x] Integrate with Supabase client initialization

### Phase 6: Performance Optimization

1. **Performance Measurement (Simplified)**:
   - [x] Remove complex performance store (`performance-store.ts`)
   - [x] Remove performance dashboard component (`PerformanceDashboard.tsx`)
   - [x] Remove complex performance types (`performance.ts`)
   - [x] Create simple performance utility (`src/utils/performanceMonitor.ts`) with `mark` and `measure`
   - [x] Integrate `measure` with logger for direct debug output (`[PERF] name: duration`)
   - [x] Refactor app startup measurements (`app/_layout.tsx`) to use `mark`/`measure`
   - [ ] (Verify) Refactor any other component/screen/data fetch measurements (if they existed) to use `mark`/`measure` or `measureAsync`/`measureSync`

2. **Memory Optimization**:
   - [ ] Implement proper cleanup in useEffect hooks
   - [x] Use memoization for expensive calculations
   - [x] Implement efficient list rendering

3. **Battery Usage**:
   - [ ] Optimize background update frequency
   - [ ] Implement efficient data fetching strategies
   - [ ] Minimize network requests

4. **Startup Time**:
   - [x] Measure and optimize app initialization
   - [x] Measure and optimize assets loading
   - [x] Measure and optimize fonts loading
   - [x] Optimize database initialization
   - [ ] Implement lazy loading for non-critical components
   - [ ] Minimize bundle size

### Phase 7: Testing & Finalization

1. **Testing**:
   - [ ] Test on various devices
   - [ ] Test offline functionality
   - [ ] Test edge cases (data corruption, etc.)

2. **Finalization**:
   - [ ] Ensure all requirements are met
   - [ ] Perform final code review
   - [ ] Prepare for release

## Priority Order

1. **Completed High Priority Tasks**:
   - ✅ Database integration
   - ✅ Replace mock data with real database
   - ✅ Basic data synchronization
   - ✅ Background updates
   - ✅ Year data management
   - ✅ Remote configuration system

2. **Current Medium Priority Tasks**:
   - UI refinements (Brata Ghara indicators, calendar grid optimization)
   - Database query optimization with indexes
   - Share functionality for date details
   - Memory and battery usage optimization

3. **Low Priority Tasks**:
   - ✅ Animations for month transitions (implemented with swipe navigation)
   - Additional features beyond core requirements
   - Advanced performance optimizations

## Technical Considerations

1. **Database Efficiency**:
   - Use prepared statements for repeated queries
   - Implement proper transaction handling
   - Create indexes for frequently queried fields

2. **UI Performance**:
   - ✅ Use React.memo for pure components
   - ✅ Use useCallback for event handlers
   - ✅ Use useMemo for expensive calculations
   - Implement virtualized lists for large datasets when needed

3. **Data Management**: ✅ MOSTLY COMPLETED
   - ✅ Implemented three-tier caching (memory → SQLite → API)
   - ✅ Used proper error handling and retry logic
   - ✅ Implemented offline-first approach
   - ✅ Stored pre-formatted text in both languages to avoid runtime conversions
   - ✅ Applied digit conversion only to dynamically generated numbers, not database content
   - ✅ Implemented in-memory caching with TTL
   - ✅ Added adjacent month prefetching for smoother navigation
   - ❌ Database query optimization with indexes (pending)

4. **Logging and Error Handling**:
   - ✅ Use the centralized logging system instead of direct console.log calls
   - ✅ Implement environment-aware logging (development vs. production)
   - ✅ Use component-specific logging with proper context
   - ✅ Wrap components with ErrorBoundary for proper error handling
   - ✅ Use try/catch blocks for error-prone operations
   - ✅ Integrate with Sentry for remote error tracking in production
   - ✅ Add user-controlled toggle for error reporting

## Implementation Progress

### Completed Tasks

1. **Project Structure Refinement**:
   - ✅ Organized components into logical subfolders (calendar, common)
   - ✅ Implemented Expo Router structure with app directory
   - ✅ Cleaned up duplicate files and legacy structure
   - ✅ Improved overall project organization with clear separation of concerns

2. **Performance Optimization**:
   - ✅ Implemented React.memo() for calendar day cells
   - ✅ Implemented React.memo() for list items
   - ✅ Implemented useCallback() for event handlers
   - ✅ Implemented useMemo() for expensive calculations
   - ✅ Optimized FlatList components with performance props
   - ✅ Ensured proper dependency arrays for all hooks

3. **Code Quality Improvements**:
   - ✅ Created comprehensive TypeScript interfaces
   - ✅ Implemented consistent naming conventions
   - ✅ Added proper JSDoc documentation
   - ✅ Created central types directory

4. **Localization Improvements**:
   - ✅ Implemented Odia digit conversion for calendar grid numbers
   - ✅ Added Odia digit support for year display in calendar header
   - ✅ Added Odia digit support in month/year picker
   - ✅ Implemented Odia digit conversion for date formatting
   - ✅ Created centralized language utilities with proper documentation
   - ✅ Added optimization notes for future database implementation

5. **Logging System**:
   - ✅ Implemented centralized logging utility with multiple log levels
   - ✅ Created environment-aware logging (development vs. production)
   - ✅ Added component-specific logging with context
   - ✅ Implemented LogProvider for app-wide logging configuration
   - ✅ Created development-only LogViewer for in-app log inspection
   - ✅ Added global error handling and logging
   - ✅ Added user-controlled toggle for error reporting

6. **Performance Measurement System**:
   - ✅ Implemented lightweight performance metrics store
   - ✅ Added app startup time measurement
   - ✅ Added component render time measurement
   - ✅ Added screen render time measurement
   - ✅ Added data fetch time measurement
   - ✅ Added interaction time measurement
   - ✅ Created development-only performance dashboard
   - ✅ Implemented performance measurement hooks and utilities

### Next Steps

1. ✅ Database integration (COMPLETED)
2. ✅ Replace mock data with real database queries (COMPLETED)
3. ✅ Implement data synchronization (COMPLETED)
4. ✅ Implement remote configuration (COMPLETED)
5. ⚠️ UI refinements:
   - Add Brata Ghara date indicators in Calendar Grid
   - Optimize calendar grid for readability
   - ✅ Implement animations for month transitions (swipe navigation)
   - Add share functionality for date details
6. ⚠️ Additional performance optimization:
   - Create necessary indexes for frequently queried fields
   - Implement proper cleanup in useEffect hooks
   - Optimize background update frequency
   - Implement lazy loading for non-critical components
7. ⚠️ Testing & Finalization:
   - Test on various devices
   - Test offline functionality
   - Test edge cases (data corruption, etc.)
   - Perform final code review

### Recent Accomplishments

1. **Database Integration**:
   - ✅ Implemented SQLite database initialization with proper error handling
   - ✅ Created database service for accessing calendar data
   - ✅ Implemented caching mechanism for efficient data access
   - ✅ Added loading states and error handling to all components
   - ✅ Fixed SQLite table query issue (see docs/DATABASE_ISSUES.md)
   - ✅ Implemented proper database copying from assets
   - ✅ Added schema versioning and migration support
   - ✅ Added reminder tables (user_reminders and scheduled_notifications)

2. **Data Synchronization**:
   - ✅ Implemented Supabase client with proper initialization
   - ✅ Created sync service for background updates
   - ✅ Implemented year data download mechanism
   - ✅ Added version tracking for updates
   - ✅ Integrated user settings (auto-update, Wi-Fi only)
   - ✅ Added manual update trigger with status feedback
   - ✅ Implemented update prompt modal
   - ✅ Fixed year data version synchronization to only update version when actual data exists

3. **Remote Configuration**:
   - ✅ Implemented RemoteConfigService for Supabase connection details
   - ✅ Created fast initialization approach with cached/default config
   - ✅ Added background refresh for updates
   - ✅ Implemented support for migrating from Supabase Cloud to self-hosted
   - ✅ Added fallback mechanism for offline operation

4. **Performance Optimizations**:
   - ✅ Implemented in-memory caching with TTL
   - ✅ Added adjacent month prefetching for smoother navigation
   - ✅ Optimized startup time with fast initialization
   - ✅ Implemented simplified performance measurement system
   - ✅ Fixed Supabase initialization warning in Sentry logs by changing log level to DEBUG
   - ✅ Enhanced ProGuard rules for React Native Fabric architecture to fix splash screen issues in production builds

5. **Feature Additions**:
   - ✅ Added Brata Ghara dates list to the home page
   - ✅ Implemented useBrataGharaDates hook for fetching brata ghara dates
   - ✅ Added methods to data coordinator and database service for brata ghara dates
   - ✅ Added styling and translations for brata ghara dates
   - ✅ Added "Delete Year Data" feature to settings screen for managing storage
     - ✅ Prevents deletion of current year data to ensure app functionality
     - ✅ Provides clear error messages for different deletion scenarios
     - ✅ Automatically filters out current year from available years list
   - ✅ Reorganized settings screen for better UX (grouped related settings, prioritized important ones)
   - ✅ Added Tithi-Based Reminder feature
     - ✅ Implemented monthly and yearly tithi-based reminders
     - ✅ Created reminder management UI (list, add/edit, details)
     - ✅ Added notification scheduling based on tithi occurrences
     - ✅ Implemented tithi calculation utilities
     - ✅ Added reminder tab to bottom navigation

6. **UI Improvements**:
   - ✅ Improved the Date Details Modal UI:
     - ✅ Combined Odia date and Panchang sections into one block
     - ✅ Reorganized content in a more logical sequence
     - ✅ Moved the banner ad below the combined section
     - ✅ Added marriage/brataghara eligibility information to the main section
     - ✅ Ensured proper display of all text in Odia when Odia language is selected
   - ✅ Fixed dark mode visibility issues:
     - ✅ Added borders to festival, marriage, and brataghara cards in dark mode
     - ✅ Enhanced text visibility with better font weight and text shadows
     - ✅ Improved contrast between text and background in dark mode
   - ✅ Updated the calendar header:
     - ✅ Added Odia month display beside the red dot in the header
     - ✅ Created a utility function to map English months to corresponding Odia months
     - ✅ Removed the "Month:" label for a cleaner UI
     - ✅ Ensured consistent display of Odia month names regardless of selected language
   - ✅ Fixed the Odia name for May month:
     - ✅ Updated the translation from "ମେ" to "ମଇ" as requested
   - ✅ Implemented swipe navigation for calendar months:
     - ✅ Created SwipeableCalendarView component with smooth animations
     - ✅ Used React Native Gesture Handler for touch gestures
     - ✅ Used Reanimated for smooth animations
     - ✅ Ensured compatibility with the date details modal
     - ✅ Optimized for performance with shared values

7. **Production Build Improvements**:
   - ✅ Configured proper release signing with keystore credentials
   - ✅ Enabled code minification and resource shrinking for smaller APK size
   - ✅ Updated version code to 3 for Play Store release
   - ✅ Generated mapping file for Sentry crash reporting
   - ✅ Successfully built and tested production AAB file

This checklist will be updated as implementation progresses.
