/**
 * Utility functions for date operations
 */
import { CalendarGrid, CalendarWeek, SupportedLanguage } from '@/types';
import { logger } from '@/utils/logging-sentry'; // Import the logger

/**
 * Format date as YYYY-MM-DD
 * @param date The date to format
 * @returns The formatted date string
 */
export const formatDateString = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Get the number of days in a month
 * @param year The year
 * @param month The month (1-12)
 * @returns The number of days in the month
 */
export const getDaysInMonth = (year: number, month: number): number => {
  return new Date(year, month, 0).getDate();
};

/**
 * Get the first day of the month (0 = Sunday, 1 = Monday, etc.)
 * @param year The year
 * @param month The month (1-12)
 * @returns The day of the week for the first day of the month
 */
export const getFirstDayOfMonth = (year: number, month: number): number => {
  return new Date(year, month - 1, 1).getDay();
};

/**
 * Check if two dates are the same day
 * @param date1 The first date
 * @param date2 The second date
 * @returns True if the dates are the same day
 */
export const isSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

/**
 * Get month name from month number
 * @param month The month (1-12)
 * @param locale The locale to use for formatting
 * @returns The month name
 */
export const getMonthName = (month: number, locale: SupportedLanguage = 'en'): string => {
  const date = new Date();
  date.setMonth(month - 1);

  return date.toLocaleString(locale === 'or' ? 'en' : locale, { month: 'long' });
};

/**
 * Get array of month names
 * @param locale The locale to use for formatting
 * @returns Array of month names
 */
export const getMonthNames = (locale: SupportedLanguage = 'en'): string[] => {
  const months: string[] = [];
  for (let i = 0; i < 12; i++) {
    const date = new Date(2000, i, 1);
    months.push(date.toLocaleString(locale === 'or' ? 'en' : locale, { month: 'long' }));
  }
  return months;
};

/**
 * Get array of weekday names (short format)
 * @param locale The locale to use for formatting
 * @returns Array of weekday names
 */
export const getWeekdayNames = (locale: SupportedLanguage = 'en'): string[] => {
  const weekdays: string[] = [];
  for (let i = 0; i < 7; i++) {
    const date = new Date(2000, 0, 2 + i); // Start with Sunday (Jan 2, 2000 was a Sunday)
    weekdays.push(date.toLocaleString(locale === 'or' ? 'en' : locale, { weekday: 'short' }));
  }
  return weekdays;
};

/**
 * Generate calendar grid data for a month
 * @param year The year
 * @param month The month (1-12)
 * @returns A 6x7 grid representing the calendar month
 */
export const generateCalendarGrid = (year: number, month: number): CalendarGrid => {
  const daysInMonth = getDaysInMonth(year, month);
  const firstDay = getFirstDayOfMonth(year, month);

  // Create a 6x7 grid (6 weeks, 7 days per week)
  const grid: CalendarGrid = Array(6).fill(null).map(() => Array(7).fill(null));

  let date = 1;
  for (let row = 0; row < 6; row++) {
    for (let col = 0; col < 7; col++) {
      if (row === 0 && col < firstDay) {
        // Empty cells before the first day of the month
        grid[row][col] = null;
      } else if (date > daysInMonth) {
        // Empty cells after the last day of the month
        grid[row][col] = null;
      } else {
        // Fill in the date
        grid[row][col] = date;
        date++;
      }
    }
  }

  return grid;
};

/**
 * Parse a date string in YYYY-MM-DD format
 * @param dateString The date string to parse
 * @returns A Date object
 */
export const parseDateString = (dateString: string): Date => {
  const [year, month, day] = dateString.split('-').map(Number);
  return new Date(year, month - 1, day);
};

/**
 * Get the date string for a specific day in a month
 * @param year The year
 * @param month The month (1-12)
 * @param day The day
 * @returns The date string in YYYY-MM-DD format
 */
export const getDateString = (year: number, month: number, day: number): string => {
  return formatDateString(new Date(year, month - 1, day));
};

/**
 * Format a timestamp into a readable date and time string
 * @param timestamp The timestamp number (milliseconds since epoch)
 * @returns A formatted string like "YYYY-MM-DD HH:MM" or null if timestamp is invalid
 */
export const formatLastCheckedTime = (timestamp: number | null): string | null => {
  if (!timestamp || typeof timestamp !== 'number' || timestamp <= 0) {
    return null;
  }
  try {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (e) {
    logger.error("Error formatting timestamp", { error: e, timestamp });
    return null;
  }
};
