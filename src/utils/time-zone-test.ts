/**
 * Test utility for time zone handling
 * This file contains functions to test and verify the time zone handling in the app
 */

import { logger } from '@/utils/logging-sentry';
import { getISTDateString, istDateStringToLocalDate, getCurrentISTDateString, logTimeZoneInfo } from './time-zone-utils';

/**
 * Run a comprehensive test of time zone utilities
 * This function tests all the time zone conversion functions
 * and logs the results for verification
 */
export function testTimeZoneUtils(): string[] {
  const results: string[] = [];

  results.push('Running time zone utility tests');

  // Test current date
  const now = new Date();
  const nowIST = getCurrentISTDateString();

  results.push(`Current date test:
  - Local date: ${now.toString()}
  - ISO string: ${now.toISOString()}
  - IST date: ${nowIST}`);

  // Also log to console for debugging
  logger.info('Current date test', {
    localDate: now.toString(),
    localISOString: now.toISOString(),
    currentISTDateString: nowIST
  });

  // Test conversion from local to IST
  const testDates = [
    new Date(), // Now
    new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
    new Date(2025, 0, 1), // January 1, 2025
    new Date(2025, 5, 15), // June 15, 2025
    new Date(2025, 11, 31), // December 31, 2025
  ];

  for (const date of testDates) {
    const istDateStr = getISTDateString(date);
    const backToLocal = istDateStringToLocalDate(istDateStr);

    results.push(`Date conversion test:
    - Original (local): ${date.toString()}
    - Converted to IST: ${istDateStr}
    - Back to local: ${backToLocal.toString()}`);

    // Also log to console
    logger.info('Date conversion test', {
      originalLocal: date.toString(),
      originalISO: date.toISOString(),
      convertedToIST: istDateStr,
      backToLocal: backToLocal.toString(),
      backToLocalISO: backToLocal.toISOString()
    });
  }

  // Test edge cases
  const midnight = new Date();
  midnight.setHours(0, 0, 0, 0);

  const almostMidnight = new Date();
  almostMidnight.setHours(23, 59, 59, 999);

  results.push(`Edge case tests:
  - Midnight: ${midnight.toString()} → IST: ${getISTDateString(midnight)}
  - Almost midnight: ${almostMidnight.toString()} → IST: ${getISTDateString(almostMidnight)}`);

  // Also log to console
  logger.info('Edge case tests', {
    midnight: midnight.toString(),
    midnightIST: getISTDateString(midnight),
    almostMidnight: almostMidnight.toString(),
    almostMidnightIST: getISTDateString(almostMidnight)
  });

  // Test time zone offset
  const offset = new Date().getTimezoneOffset();
  const offsetHours = -offset / 60; // Convert to hours (negative because getTimezoneOffset returns minutes west of UTC)
  const timeZoneName = Intl.DateTimeFormat().resolvedOptions().timeZone;

  results.push(`Time zone information:
  - Offset: ${offset} minutes (${offsetHours} hours)
  - Time zone: ${timeZoneName}`);

  // Also log to console
  logger.info('Time zone information', {
    timeZoneOffset: offset,
    timeZoneOffsetHours: offsetHours,
    timeZoneName: timeZoneName
  });

  results.push('Time zone utility tests completed');

  return results;
}

/**
 * Test the reminder date calculation with time zones
 * This simulates finding the next occurrence of a tithi
 * and scheduling a notification for it
 */
export function testReminderDateCalculation(): string[] {
  const results: string[] = [];

  // Simulate a tithi occurrence date from panchang data (in IST)
  const istDateString = '2025-06-15';

  // Convert to local date for notification scheduling
  const localDate = istDateStringToLocalDate(istDateString);

  // Set notification time (e.g., 8:00 AM)
  const notificationTime = '08:00';
  const [hours, minutes] = notificationTime.split(':').map(Number);

  const notificationDate = new Date(localDate);
  notificationDate.setHours(hours, minutes, 0, 0);

  results.push(`Reminder date calculation test:
  - IST date from panchang: ${istDateString}
  - Converted to local date: ${localDate.toString()}
  - Notification time: ${notificationTime}
  - Final notification date: ${notificationDate.toString()}`);

  // Also log to console
  logger.info('Reminder date calculation test', {
    istDateString,
    localDate: localDate.toString(),
    notificationTime,
    notificationDate: notificationDate.toString(),
    notificationDateISO: notificationDate.toISOString()
  });

  return results;
}
