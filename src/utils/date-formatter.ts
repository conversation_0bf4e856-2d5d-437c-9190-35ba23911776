import { useSettingsStore } from '@/store/settings-store';

/**
 * Format a date for display
 * @param date The date to format
 * @param includeTime Whether to include the time
 * @returns The formatted date string
 */
export function formatDate(date: Date, includeTime: boolean = false): string {
  const language = useSettingsStore.getState().language;
  const use24HourFormat = false; // Default to 12-hour format
  
  try {
    // Format options
    const dateOptions: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    
    // Add time options if requested
    if (includeTime) {
      dateOptions.hour = use24HourFormat ? '2-digit' : 'numeric';
      dateOptions.minute = '2-digit';
      dateOptions.hour12 = !use24HourFormat;
    }
    
    // Format based on language
    const locale = language === 'or' ? 'or-IN' : 'en-US';
    return new Intl.DateTimeFormat(locale, dateOptions).format(date);
  } catch (error) {
    // Fallback to simple format
    return date.toDateString() + (includeTime ? ' ' + date.toLocaleTimeString() : '');
  }
}

/**
 * Format a time string (HH:MM) for display
 * @param timeString The time string in 24-hour format (HH:MM)
 * @returns The formatted time string
 */
export function formatTime(timeString: string): string {
  const use24HourFormat = false; // Default to 12-hour format
  
  try {
    // Parse the time string
    const [hours, minutes] = timeString.split(':').map(Number);
    
    // Create a date object with today's date and the specified time
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    
    // Format options
    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: use24HourFormat ? '2-digit' : 'numeric',
      minute: '2-digit',
      hour12: !use24HourFormat,
    };
    
    // Format the time
    return new Intl.DateTimeFormat('en-US', timeOptions).format(date);
  } catch (error) {
    // Return the original string if parsing fails
    return timeString;
  }
}
