/**
 * Simplified Performance Monitoring Utility
 *
 * Provides basic mark and measure functionality for development performance tracking.
 * Logs measurements directly to the console using the logger utility when in DEV mode.
 */

import { logger } from './logging-sentry'; // Use the existing logger

// Store performance marks with timestamps
const marks: Record<string, number> = {};
// Store performance measures (duration between marks)
const measures: Record<string, number> = {};

/**
 * Set a performance mark with the current timestamp.
 * Only active in development mode.
 * @param markName Name of the mark
 */
export const mark = (markName: string): void => {
  if (!__DEV__) return; // Only run in development
  marks[markName] = Date.now();
  // Optional: Log the mark itself if needed for debugging sequence
  // logger.debug(`[PERF_MARK] ${markName}`);
};

/**
 * Measure time between two marks or from a mark to now.
 * Logs the result directly using the logger in development mode.
 * @param measureName Name of the measurement (used for logging)
 * @param startMark Starting mark name
 * @param endMark Ending mark name (optional, defaults to current time)
 * @returns Duration in milliseconds, or 0 if marks are invalid or not in DEV mode.
 */
export const measure = (measureName: string, startMark: string, endMark?: string): number => {
  if (!__DEV__) return 0; // Only run in development

  const startTime = marks[startMark];

  if (startTime === undefined) {
    // Use logger.warn for potential issues
    logger.warn(`[PERF] Start mark "${startMark}" not found for measure "${measureName}"`);
    return 0;
  }

  const endTime = endMark ? marks[endMark] : Date.now();

  if (endMark && endTime === undefined) {
    logger.warn(`[PERF] End mark "${endMark}" not found for measure "${measureName}"`);
    return 0;
  }

  const duration = endTime - startTime;
  measures[measureName] = duration; // Store the measure

  // Log the performance measurement using logger.debug
  // Use a consistent prefix like [PERF] for easy filtering
  logger.debug(`[PERF] ${measureName}: ${duration.toFixed(1)}ms`);

  // Optionally remove marks after measurement to prevent memory buildup if needed
  // delete marks[startMark];
  // if (endMark) delete marks[endMark];

  return duration;
};

/**
 * Get all recorded performance measures.
 * @returns Object containing all measures recorded so far.
 */
export const getAllMeasures = (): Record<string, number> => {
  // Return a copy to prevent external modification
  return { ...measures };
};

/**
 * Clear all performance marks and measures.
 */
export const clearPerformanceData = (): void => {
  if (!__DEV__) return;
  Object.keys(marks).forEach(key => delete marks[key]);
  Object.keys(measures).forEach(key => delete measures[key]);
  logger.debug('[PERF] Performance data cleared');
};

/**
 * Log all recorded performance measures to the console via the logger.
 * Only active in development mode.
 */
export const logPerformanceSummary = (): void => {
  if (!__DEV__) return;

  logger.debug('=== PERFORMANCE SUMMARY ===');
  if (Object.keys(measures).length === 0) {
    logger.debug('No performance measures recorded.');
  } else {
    // Sort measures alphabetically for consistent output
    const sortedMeasures = Object.entries(measures).sort((a, b) => a[0].localeCompare(b[0]));
    sortedMeasures.forEach(([name, duration]) => {
      logger.debug(`${name}: ${duration.toFixed(1)}ms`);
    });
  }
  logger.debug('===========================');
};

/**
 * Helper function to wrap an async function call with performance measurement.
 * @param measureName Name for the measurement log.
 * @param asyncFunc The async function to execute and measure.
 * @returns The result of the async function.
 */
export async function measureAsync<T>(measureName: string, asyncFunc: () => Promise<T>): Promise<T> {
  if (!__DEV__) return asyncFunc();

  const startMarkName = `${measureName}_start_${Date.now()}_${Math.random()}`;
  mark(startMarkName);
  try {
    const result = await asyncFunc();
    measure(measureName, startMarkName);
    return result;
  } catch (error) {
    // Measure time even if it fails, log error context
    const duration = measure(measureName, startMarkName);
    logger.error(`[PERF_ERROR] ${measureName} failed after ${duration.toFixed(1)}ms`, { error });
    throw error; // Re-throw the original error
  }
}

/**
 * Helper function to wrap a synchronous function call with performance measurement.
 * @param measureName Name for the measurement log.
 * @param syncFunc The synchronous function to execute and measure.
 * @returns The result of the synchronous function.
 */
export function measureSync<T>(measureName: string, syncFunc: () => T): T {
    if (!__DEV__) return syncFunc();

    const startMarkName = `${measureName}_start_${Date.now()}_${Math.random()}`;
    mark(startMarkName);
    try {
        const result = syncFunc();
        measure(measureName, startMarkName);
        return result;
    } catch (error) {
        // Measure time even if it fails, log error context
        const duration = measure(measureName, startMarkName);
        logger.error(`[PERF_ERROR] ${measureName} failed after ${duration.toFixed(1)}ms`, { error });
        throw error; // Re-throw the original error
    }
}
