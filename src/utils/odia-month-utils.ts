/**
 * @deprecated This file is deprecated in favor of the new data-coordinator approach.
 *
 * Use the following instead:
 * - dataCoordinator.getOdiaMonthsForMonth() for direct access
 * - useOdiaMonths() hook for React components
 *
 * The new approach utilizes cached month data for better performance and accuracy.
 *
 * Migration guide:
 * - Replace getOdiaMonthsForEnglishMonth() with dataCoordinator.getOdiaMonthsForMonth()
 * - Replace getOdiaMonthsFromDatabase() with useOdiaMonths() hook in components
 *
 * This file will be removed in a future version.
 */

// This file is kept for reference but should not be used in new code.
// All functionality has been moved to data-coordinator.ts with better caching and performance.
