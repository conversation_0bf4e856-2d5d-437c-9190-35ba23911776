/**
 * Utility functions for language-related operations
 *
 * IMPORTANT USAGE NOTES:
 *
 * 1. These conversion functions should be used for:
 *    - Numbers generated in the app (like calendar grid days, years)
 *    - Formatting dates from JavaScript Date objects
 *    - Any dynamic numeric content not coming from the database
 *
 * 2. For data coming from the database:
 *    - The database should store pre-formatted strings in both languages
 *    - Use the appropriate language version directly from the database
 *    - Do NOT apply these conversions to data already in Odia format
 *
 * This approach minimizes unnecessary conversions and improves performance.
 */
import { SupportedLanguage } from '@/types';

/**
 * Mapping of English digits to Odia digits
 */
export const ODIA_DIGITS: Record<string, string> = {
  '0': '୦',
  '1': '୧',
  '2': '୨',
  '3': '୩',
  '4': '୪',
  '5': '୫',
  '6': '୬',
  '7': '୭',
  '8': '୮',
  '9': '୯'
};

/**
 * Convert English digits to Odia digits
 * @param value The string or number to convert
 * @param language The current language
 * @returns The converted string with Odia digits if language is 'or', otherwise the original string
 */
export const convertToLocalDigits = (value: string | number, language: SupportedLanguage): string => {
  if (language !== 'or') return String(value);

  return String(value).replace(/[0-9]/g, match => ODIA_DIGITS[match] || match);
};

/**
 * Format a date with localized digits
 * @param date The date to format
 * @param language The current language
 * @param options The Intl.DateTimeFormatOptions to use
 * @returns The formatted date string with localized digits
 */
export const formatDateWithLocalDigits = (
  date: Date,
  language: SupportedLanguage,
  options: Intl.DateTimeFormatOptions = { day: 'numeric', month: 'long', year: 'numeric' }
): string => {
  // First get the formatted date using the locale
  const formattedDate = date.toLocaleDateString(
    language === 'or' ? 'en-IN' : language,
    options
  );

  // Then convert digits if needed
  return convertToLocalDigits(formattedDate, language);
};
