import { databaseService } from '@/services/database-service';
import { PanchangData } from '@/types/database';
import { OccurrenceInfo } from '@/types/reminders';
import { logger } from '@/utils/logging-sentry';
import { getISTDateString, istDateStringToLocalDate } from '@/utils/time-zone-utils';

/**
 * Finds the next occurrence of a specific tithi
 * @param odiaMonth The Odia month (optional, for yearly reminders)
 * @param paksha The paksha (lunar phase)
 * @param tithi The tithi (lunar day)
 * @param fromDate The date to start searching from
 * @returns The next occurrence date or null if not found
 */
export async function findNextTithiOccurrence(
  odiaMonth: string | null | undefined,
  paksha: string | undefined,
  tithi: string | undefined,
  fromDate: Date = new Date()
): Promise<OccurrenceInfo | null> {
  if (!paksha || !tithi) {
    logger.error('Missing required parameters for tithi calculation', { paksha, tithi });
    return null;
  }

  try {
    if (!databaseService.isDbInitialized) {
      throw new Error('Database not initialized');
    }

    // Format the from date as YYYY-MM-DD in IST
    const fromDateStr = getISTDateString(fromDate);

    logger.debug('Finding next tithi occurrence (using LIKE query)', {
      fromDate: fromDate.toString(),
      fromDateIST: fromDateStr,
      paksha,
      tithi,
      odiaMonth
    });

    // Build the query based on whether we're looking for a specific month
    // Use eng_date > ? (strictly greater than) to avoid finding the same date again
    // This is important when scheduling the next occurrence after a notification is delivered
    // Use LIKE operator for tithi_name to handle days with multiple tithis (e.g., 'ସପ୍ତମୀ ଅଷ୍ଟମୀକ୍ଷ')
    let query = `
      SELECT * FROM panchang_data
      WHERE eng_date > ?
      AND paksha = ?
      AND tithi_name LIKE ?
    `;

    const params: any[] = [fromDateStr, paksha, `%${tithi}%`];

    // Add month condition if specified
    if (odiaMonth) {
      query += ' AND odia_month = ?';
      params.push(odiaMonth);
    }

    // Order by date and limit to 1 to get the next occurrence
    query += ' ORDER BY eng_date ASC LIMIT 1';

    // Execute the query with parameters
    const results = await databaseService.executeQuery(query, params);
    const result = results.length > 0 ? results[0] as PanchangData : null;

    if (!result) {
      logger.warn('No occurrence found for tithi', { odiaMonth, paksha, tithi, fromDate: fromDateStr });
      return null;
    }

    // Parse the date - convert from IST date string to local date
    const occurrenceDate = istDateStringToLocalDate(result.eng_date);

    logger.debug('Found tithi occurrence', {
      engDate: result.eng_date,
      localDate: occurrenceDate.toString(),
      odiaDate: result.odia_date,
      paksha: result.paksha,
      tithi: result.tithi_name
    });

    return {
      date: occurrenceDate,
      odiaDate: result.odia_date,
      odiaMonth: result.odia_month,
      paksha: result.paksha,
      tithi: result.tithi_name
    };
  } catch (error) {
    logger.error('Error finding next tithi occurrence', { error, odiaMonth, paksha, tithi });
    return null;
  }
}

/**
 * Finds all occurrences of a specific tithi within a date range
 * @param odiaMonth The Odia month (optional, for yearly reminders)
 * @param paksha The paksha (lunar phase)
 * @param tithi The tithi (lunar day)
 * @param fromDate The date to start searching from
 * @param toDate The date to end searching at
 * @param limit Maximum number of occurrences to return
 * @returns Array of occurrence dates
 */
export async function findTithiOccurrencesInRange(
  odiaMonth: string | null | undefined,
  paksha: string | undefined,
  tithi: string | undefined,
  fromDate: Date = new Date(),
  toDate: Date = new Date(fromDate.getTime() + 365 * 24 * 60 * 60 * 1000), // Default to 1 year ahead
  limit: number = 10
): Promise<OccurrenceInfo[]> {
  if (!paksha || !tithi) {
    logger.error('Missing required parameters for tithi calculation', { paksha, tithi });
    return [];
  }

  try {
    if (!databaseService.isDbInitialized) {
      throw new Error('Database not initialized');
    }

    // Format the dates as YYYY-MM-DD in IST
    const fromDateStr = getISTDateString(fromDate);
    const toDateStr = getISTDateString(toDate);

    logger.debug('Finding tithi occurrences in range (using LIKE query)', {
      fromDate: fromDate.toString(),
      toDate: toDate.toString(),
      fromDateIST: fromDateStr,
      toDateIST: toDateStr,
      paksha,
      tithi,
      odiaMonth
    });

    // Build the query based on whether we're looking for a specific month
    // Use eng_date >= ? (greater than or equal) to include today's date
    // This is important for initial scheduling of reminders
    // Use LIKE operator for tithi_name to handle days with multiple tithis (e.g., 'ସପ୍ତମୀ ଅଷ୍ଟମୀକ୍ଷ')
    let query = `
      SELECT * FROM panchang_data
      WHERE eng_date >= ?
      AND eng_date <= ?
      AND paksha = ?
      AND tithi_name LIKE ?
    `;

    const params: any[] = [fromDateStr, toDateStr, paksha, `%${tithi}%`];

    // Add month condition if specified
    if (odiaMonth) {
      query += ' AND odia_month = ?';
      params.push(odiaMonth);
    }

    // Order by date and limit results
    query += ` ORDER BY eng_date ASC LIMIT ${limit}`;

    // Execute the query with parameters
    const results = await databaseService.executeQuery(query, params);

    if (!results || results.length === 0) {
      logger.warn('No occurrences found for tithi in range', {
        odiaMonth, paksha, tithi,
        fromDate: fromDateStr,
        toDate: toDateStr
      });
      return [];
    }

    // Map results to occurrence info - convert from IST date string to local date
    const occurrences = results.map((result: PanchangData) => {
      const localDate = istDateStringToLocalDate(result.eng_date);
      return {
        date: localDate,
        odiaDate: result.odia_date,
        odiaMonth: result.odia_month,
        paksha: result.paksha,
        tithi: result.tithi_name
      };
    });

    logger.debug('Found tithi occurrences', {
      count: occurrences.length,
      dates: occurrences.map(o => o.date.toString())
    });

    return occurrences;
  } catch (error) {
    logger.error('Error finding tithi occurrences in range', {
      error, odiaMonth, paksha, tithi,
      fromDate: fromDate.toISOString(),
      toDate: toDate.toISOString()
    });
    return [];
  }
}

/**
 * Gets the Odia date details for a specific Gregorian date
 * @param date The Gregorian date
 * @returns The Odia date details or null if not found
 */
export async function getOdiaDateDetails(date: Date): Promise<PanchangData | null> {
  try {
    // Format the date as YYYY-MM-DD in IST
    const dateStr = getISTDateString(date);

    logger.debug('Getting Odia date details', {
      date: date.toString(),
      dateIST: dateStr
    });

    // Get the date data from the database
    return await databaseService.getDateData(dateStr);
  } catch (error) {
    logger.error('Error getting Odia date details', { error, date: date.toISOString() });
    return null;
  }
}

/**
 * Checks if the lack of occurrences is due to missing data rather than the combination not existing
 * @param odiaMonth The Odia month (optional, for yearly reminders)
 * @param paksha The paksha (lunar phase)
 * @param tithi The tithi (lunar day)
 * @returns A promise that resolves to an object with information about data availability
 */
export async function checkDataAvailabilityForTithi(
  odiaMonth: string | null | undefined,
  paksha: string | undefined,
  tithi: string | undefined
): Promise<{
  combinationExists: boolean;
  latestAvailableYear: number | null;
  nextYearAvailable: boolean;
  nextYearDownloadable: boolean;
}> {
  // Default result
  const result = {
    combinationExists: false,
    latestAvailableYear: null,
    nextYearAvailable: false,
    nextYearDownloadable: false
  };

  if (!paksha || !tithi) {
    logger.error('Missing required parameters for data availability check', { paksha, tithi });
    return result;
  }

  try {
    if (!databaseService.isDbInitialized) {
      throw new Error('Database not initialized');
    }

    // First, check if the combination exists at all
    result.combinationExists = await validateTithiCombination(odiaMonth, paksha, tithi);

    if (!result.combinationExists) {
      // If the combination doesn't exist at all, no need to check further
      return result;
    }

    // Get the current year
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    // Find the latest year for which we have data
    const latestYearQuery = `
      SELECT MAX(SUBSTR(eng_date, 1, 4)) as latest_year
      FROM panchang_data
    `;

    const latestYearResult = await databaseService.executeQuery(latestYearQuery);
    const latestYear = latestYearResult[0]?.latest_year;

    if (latestYear) {
      result.latestAvailableYear = parseInt(latestYear, 10);
    }

    // Check if we have any data for the next year
    const nextYear = currentYear + 1;
    const nextYearQuery = `
      SELECT COUNT(*) as count
      FROM panchang_data
      WHERE eng_date LIKE '${nextYear}-%'
    `;

    const nextYearResult = await databaseService.executeQuery(nextYearQuery);
    const nextYearCount = nextYearResult[0]?.count || 0;

    result.nextYearAvailable = nextYearCount > 0;

    // Check if next year data is available for download
    // This requires checking with the data coordinator service
    // We'll implement this in the UI layer to avoid circular dependencies

    return result;
  } catch (error) {
    logger.error('Error checking data availability for tithi', {
      error, odiaMonth, paksha, tithi
    });
    return result;
  }
}

/**
 * Validates if a combination of month, paksha, and tithi exists in the panchang data
 * @param odiaMonth The Odia month (optional, for yearly reminders)
 * @param paksha The paksha (lunar phase)
 * @param tithi The tithi (lunar day)
 * @returns A promise that resolves to true if the combination exists, false otherwise
 */
export async function validateTithiCombination(
  odiaMonth: string | null | undefined,
  paksha: string | undefined,
  tithi: string | undefined
): Promise<boolean> {
  if (!paksha || !tithi) {
    logger.error('Missing required parameters for tithi validation', { paksha, tithi });
    return false;
  }

  try {
    if (!databaseService.isDbInitialized) {
      throw new Error('Database not initialized');
    }

    // Build the query based on whether we're looking for a specific month
    // Use LIKE operator for tithi_name to handle days with multiple tithis (e.g., 'ସପ୍ତମୀ ଅଷ୍ଟମୀକ୍ଷ')
    let query = `
      SELECT COUNT(*) as count FROM panchang_data
      WHERE paksha = ?
      AND tithi_name LIKE ?
    `;

    const params: any[] = [paksha, `%${tithi}%`];

    // Add month condition if specified
    if (odiaMonth) {
      query += ' AND odia_month = ?';
      params.push(odiaMonth);
    }

    // Execute the query with parameters
    const results = await databaseService.executeQuery(query, params);
    const count = results[0]?.count || 0;

    logger.debug('Validated tithi combination (using LIKE query)', {
      odiaMonth,
      paksha,
      tithi,
      exists: count > 0,
      count
    });

    return count > 0;
  } catch (error) {
    logger.error('Error validating tithi combination', { error, odiaMonth, paksha, tithi });
    return false;
  }
}
