/**
 * Time zone utilities for the Odia Calendar app
 * 
 * These utilities handle conversions between:
 * - Local time (device time zone)
 * - IST (Indian Standard Time, UTC+5:30)
 * 
 * The panchang data is calculated for IST, so we need to ensure
 * that date calculations use IST, while notifications are delivered
 * in the user's local time.
 */

import { logger } from '@/utils/logging-sentry';

// IST offset from UTC in minutes (5 hours and 30 minutes)
const IST_OFFSET_MINUTES = 5 * 60 + 30;

/**
 * Get the IST date string (YYYY-MM-DD) for a given date
 * This is used when querying the panchang data
 * 
 * @param date The date to convert (in any time zone)
 * @returns The date string in IST (YYYY-MM-DD)
 */
export function getISTDateString(date: Date): string {
  try {
    // Get the UTC time in milliseconds
    const utcTime = date.getTime() + (date.getTimezoneOffset() * 60000);
    
    // Convert to IST by adding the IST offset
    const istTime = utcTime + (IST_OFFSET_MINUTES * 60000);
    
    // Create a new date object with the IST time
    const istDate = new Date(istTime);
    
    // Format as YYYY-MM-DD
    const year = istDate.getUTCFullYear();
    const month = String(istDate.getUTCMonth() + 1).padStart(2, '0');
    const day = String(istDate.getUTCDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    logger.error('Error converting to IST date string', { error });
    // Fallback to simple date string without time zone conversion
    return date.toISOString().split('T')[0];
  }
}

/**
 * Convert a date string (YYYY-MM-DD) from IST to a local Date object
 * This is used when converting panchang dates to the device's time zone
 * 
 * @param istDateString The date string in IST (YYYY-MM-DD)
 * @returns A Date object in the local time zone
 */
export function istDateStringToLocalDate(istDateString: string): Date {
  try {
    // Parse the IST date string
    const [year, month, day] = istDateString.split('-').map(Number);
    
    // Create a Date object representing midnight on this date in IST
    // We use UTC methods to avoid automatic time zone conversion
    const istDate = new Date(Date.UTC(year, month - 1, day, 0, 0, 0));
    
    // Adjust for IST offset (UTC+5:30)
    istDate.setUTCMinutes(istDate.getUTCMinutes() - IST_OFFSET_MINUTES);
    
    // Convert to local time
    return new Date(istDate);
  } catch (error) {
    logger.error('Error converting IST date string to local date', { 
      error, 
      istDateString 
    });
    // Fallback to simple date parsing
    return new Date(istDateString);
  }
}

/**
 * Get the current date string in IST
 * This is used when querying for current or future occurrences
 * 
 * @returns The current date string in IST (YYYY-MM-DD)
 */
export function getCurrentISTDateString(): string {
  return getISTDateString(new Date());
}

/**
 * Debug function to log time zone information
 * This is useful for debugging time zone issues
 */
export function logTimeZoneInfo(context: string, date?: Date): void {
  const testDate = date || new Date();
  const localString = testDate.toString();
  const isoString = testDate.toISOString();
  const istString = getISTDateString(testDate);
  const localOffset = testDate.getTimezoneOffset();
  
  logger.debug('Time zone debug info', {
    context,
    localString,
    isoString,
    istString,
    localOffset,
    localOffsetHours: -localOffset / 60
  });
}
