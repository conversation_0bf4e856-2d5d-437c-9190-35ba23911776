/**
 * Streamlined logging system for Odia Calendar Lite with Sentry integration
 *
 * This module provides a centralized logging system with Sentry integration.
 * It's optimized for minimal code size in production builds while maintaining
 * comprehensive logging capabilities.
 *
 * Usage:
 * ```typescript
 * import { logger } from '@/utils/logging-sentry';
 *
 * // Basic logging
 * logger.debug('Debug message');
 * logger.info('Info message');
 * logger.warn('Warning message');
 * logger.error('Error message');
 *
 * // Logging with context
 * logger.info('User logged in', { userId: 123, username: 'john' });
 *
 * // Component-specific logging
 * const componentLogger = createLogger('MyComponent');
 * componentLogger.info('Component initialized');
 *
 * // Using the hook in a component
 * const logger = useComponentLogger();
 * logger.info('Component rendered');
 * ```
 *
 * IMPORTANT: Never use console.log/info/warn/error directly in the app code.
 * Always use this logger to ensure consistent logging behavior.
 */

import { Platform } from 'react-native';
import Constants from 'expo-constants';
import * as Sentry from '@sentry/react-native';
import React, { ReactNode } from 'react';

/**
 * Log levels enum
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4,
}

/**
 * App information
 */
export const APP_INFO = {
  name: 'OdiaCalendarLite',
  version: Constants.expoConfig?.version || '1.0.0',
  buildNumber: Constants.expoConfig?.ios?.buildNumber || Constants.expoConfig?.android?.versionCode || '1',
  platform: Platform.OS,
};

/**
 * Logger configuration
 */
interface LoggerConfig {
  minLevel: LogLevel;
  sentryEnabled: boolean;
}

/**
 * Default configuration
 */
const DEFAULT_CONFIG: LoggerConfig = {
  minLevel: __DEV__ ? LogLevel.DEBUG : LogLevel.WARN,
  sentryEnabled: !__DEV__, // Only enable Sentry in production by default
};

/**
 * Core Logger class
 */
class Logger {
  private config: LoggerConfig;
  private isLogging = false;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Update logger configuration
   */
  public configure(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Log a debug message
   */
  public debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Log an info message
   */
  public info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Log a warning message
   */
  public warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Log an error message
   */
  public error(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context);
  }

  /**
   * Internal log method
   */
  private log(level: LogLevel, message: string, context?: Record<string, any>): void {
    // Skip if below minimum log level
    if (level < this.config.minLevel) {
      return;
    }

    // Prevent infinite recursion
    if (this.isLogging) {
      return;
    }

    try {
      // Set flag to prevent recursive calls
      this.isLogging = true;

      // Format context with app info
      const enrichedContext = {
        ...context,
        app: APP_INFO.name,
        version: APP_INFO.version,
        platform: APP_INFO.platform,
      };

      // Console logging (in development)
      if (__DEV__) {
        this.logToConsole(level, message, enrichedContext);
      }

      // Sentry logging (for warnings and errors in production)
      // Only send to Sentry if error reporting is enabled in settings
      const { errorReportingEnabled } = require('@/store/settings-store').useSettingsStore.getState();
      if (this.config.sentryEnabled && level >= LogLevel.WARN && errorReportingEnabled) {
        this.logToSentry(level, message, enrichedContext);
      }
    } finally {
      // Always reset the flag, even if an error occurs
      this.isLogging = false;
    }
  }

  /**
   * Log to console with appropriate formatting
   */
  private logToConsole(level: LogLevel, message: string, context?: Record<string, any>): void {
    const prefix = this.getLogLevelPrefix(level);

    // Add to log store for the LogViewer (only in development)
    if (__DEV__) {
      try {
        // Import dynamically to avoid circular dependencies
        const { addLogToStore } = require('@/components/common/SentryLogViewer');
        addLogToStore(level, message, context);
      } catch (error) {
        // Ignore errors in development tools
      }
    }

    switch (level) {
      case LogLevel.DEBUG:
        if (context) {
          console.debug(prefix, message, context);
        } else {
          console.debug(prefix, message);
        }
        break;
      case LogLevel.INFO:
        if (context) {
          console.info(prefix, message, context);
        } else {
          console.info(prefix, message);
        }
        break;
      case LogLevel.WARN:
        if (context) {
          console.warn(prefix, message, context);
        } else {
          console.warn(prefix, message);
        }
        break;
      case LogLevel.ERROR:
        if (context) {
          console.error(prefix, message, context);
        } else {
          console.error(prefix, message);
        }
        break;
    }
  }

  /**
   * Get prefix for log level
   */
  private getLogLevelPrefix(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG:
        return '[DEBUG]';
      case LogLevel.INFO:
        return '[INFO]';
      case LogLevel.WARN:
        return '[WARN]';
      case LogLevel.ERROR:
        return '[ERROR]';
      default:
        return '';
    }
  }

  /**
   * Log to Sentry
   */
  private logToSentry(level: LogLevel, message: string, context?: Record<string, any>): void {
    // Map our log level to Sentry severity
    const severity = this.mapLogLevelToSentrySeverity(level);

    // Add breadcrumb for context
    Sentry.addBreadcrumb({
      category: 'app',
      message,
      level: severity as Sentry.SeverityLevel,
      data: context,
    });

    // For errors, capture an exception
    if (level === LogLevel.ERROR) {
      if (context?.error instanceof Error) {
        // If we have an actual Error object, use it
        Sentry.captureException(context.error);
      } else {
        // Otherwise create a new Error with our message
        const error = new Error(message);
        Sentry.captureException(error);
      }
    } else {
      // For non-errors, just capture a message
      Sentry.captureMessage(message, {
        level: severity as Sentry.SeverityLevel
      });
    }
  }

  /**
   * Map our log level to Sentry severity
   */
  private mapLogLevelToSentrySeverity(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG:
        return 'debug';
      case LogLevel.INFO:
        return 'info';
      case LogLevel.WARN:
        return 'warning';
      case LogLevel.ERROR:
        return 'error';
      default:
        return 'info';
    }
  }

  /**
   * Initialize Sentry
   */
  public initializeSentry(): void {
    // Sentry is already initialized by the wizard
    // This method is just a placeholder for any additional configuration

    // Set tags for better filtering in Sentry
    Sentry.setTag('app.version', APP_INFO.version);
    Sentry.setTag('app.platform', APP_INFO.platform);
  }

  /**
   * Set user information in Sentry
   * Call this after user login
   */
  public setUser(user: { id: string, email?: string, username?: string }): void {
    // Only set user if error reporting is enabled in settings
    const { errorReportingEnabled } = require('@/store/settings-store').useSettingsStore.getState();
    if (this.config.sentryEnabled && errorReportingEnabled) {
      Sentry.setUser(user);
    }
  }

  /**
   * Clear user information from Sentry
   * Call this after user logout
   */
  public clearUser(): void {
    // Always clear user data regardless of settings for privacy
    if (this.config.sentryEnabled) {
      Sentry.setUser(null);
    }
  }

  /**
   * Flush any pending logs
   */
  public async flushLogs(): Promise<void> {
    // Only flush logs if error reporting is enabled in settings
    const { errorReportingEnabled } = require('@/store/settings-store').useSettingsStore.getState();
    if (this.config.sentryEnabled && errorReportingEnabled) {
      await Sentry.flush();
    }
  }
}

// Create and export a singleton instance
export const logger = new Logger();

/**
 * Create a component-specific logger
 */
export function createLogger(component: string) {
  return {
    debug: (message: string, context?: Record<string, any>) =>
      logger.debug(message, { component, ...context }),
    info: (message: string, context?: Record<string, any>) =>
      logger.info(message, { component, ...context }),
    warn: (message: string, context?: Record<string, any>) =>
      logger.warn(message, { component, ...context }),
    error: (message: string, context?: Record<string, any>) =>
      logger.error(message, { component, ...context }),
  };
}

// Component name context for logging
let currentComponent: string | undefined = undefined;

/**
 * Props for the LogProvider component
 */
export interface LogProviderProps {
  children: ReactNode;
  componentName?: string;
}

/**
 * LogProvider component - simplified version without React context
 * to reduce bundle size
 */
export const LogProvider: React.FC<LogProviderProps> = ({
  children,
  componentName
}) => {
  // Set the current component name
  currentComponent = componentName;

  // Just render children
  return React.createElement(React.Fragment, null, children);
};

/**
 * Hook to use component-specific logger
 */
export function useComponentLogger(componentName?: string) {
  // Use provided component name or the current component
  const effectiveComponentName = componentName || currentComponent;

  return {
    debug: (message: string, context?: Record<string, any>) =>
      logger.debug(message, { component: effectiveComponentName, ...context }),
    info: (message: string, context?: Record<string, any>) =>
      logger.info(message, { component: effectiveComponentName, ...context }),
    warn: (message: string, context?: Record<string, any>) =>
      logger.warn(message, { component: effectiveComponentName, ...context }),
    error: (message: string, context?: Record<string, any>) =>
      logger.error(message, { component: effectiveComponentName, ...context }),
  };
}

/**
 * Initialize the logging system
 */
export function initializeLogging(): void {
  // Initialize Sentry
  if (!__DEV__) {
    logger.initializeSentry();
  }

  // Log initialization
  logger.info('Logging system initialized', {
    environment: __DEV__ ? 'development' : 'production',
  });
}
