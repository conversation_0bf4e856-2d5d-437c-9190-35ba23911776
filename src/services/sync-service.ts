import * as SQLite from 'expo-sqlite';
import { supabase } from './supabase-client'; // Use our configured client
import { databaseService } from './database-service';
// REMOVE: import { dataCoordinator } from './data-coordinator'; // Import dataCoordinator
import { PanchangData } from '@/types/database'; // Use our type definition
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo, { NetInfoStateType } from '@react-native-community/netinfo'; // Import NetInfoStateType
import { useSettingsStore } from '@/store/settings-store'; // Import settings store
import { logger } from '@/utils/logging-sentry'; // Import the centralized logger

// Interfaces (can potentially move to types/sync.ts later)
export interface CalendarYearVersion {
  year: string;
  version: number;
  last_updated?: string; // Make optional as it might not always be fetched/used
}

export interface CalendarUpdate {
  eng_date: string;
  year: string;
  update_version: number;
  panchang_data: PanchangData; // Use our PanchangData type
}

// Constants
const UPDATE_CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 2000; // 2 seconds
const LOCK_TIMEOUT = 5 * 60 * 1000; // 5 minutes
const STORAGE_KEYS = {
  LAST_UPDATE_CHECK: 'last_sync_check_timestamp',
  UPDATE_LOCK: 'sync_update_lock',
  UPDATE_PENDING: 'sync_update_pending', // Flag for pending manual update
  // Settings keys are now managed by Zustand store, no need to duplicate here
};

// --- Lock Management ---
const acquireUpdateLock = async (): Promise<boolean> => {
  try {
    const lockData = await AsyncStorage.getItem(STORAGE_KEYS.UPDATE_LOCK);
    if (lockData) {
      const lock = JSON.parse(lockData);
      if (Date.now() - lock.timestamp < LOCK_TIMEOUT) {
        logger.debug('Update lock is active, another sync is in progress');
        return false;
      }
      logger.warn('Found stale update lock, overriding');
    }
    await AsyncStorage.setItem(STORAGE_KEYS.UPDATE_LOCK, JSON.stringify({ timestamp: Date.now() }));
    logger.debug('Acquired update lock');
    return true;
  } catch (error) {
    logger.error('Error acquiring update lock', { error });
    return false;
  }
};

const releaseUpdateLock = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.UPDATE_LOCK);
    logger.debug('Released update lock');
  } catch (error) {
    logger.error('Error releasing update lock', { error });
  }
};

// --- Core Sync Logic ---

class SyncService {

  /**
   * Initializes version tracking in the local database.
   */
  async initializeSync(): Promise<void> {
    logger.info('Initializing sync service and local version tracking...');
    try {
      // Ensure the database service itself is initialized first
      await databaseService.initialize();
      // Now initialize the year versions based on existing panchang data
      await databaseService.initializeYearVersions();
      logger.info('Sync service initialization complete.');
    } catch (error) {
      logger.error('Failed to initialize sync service', { error });
      // Decide if we should re-throw or handle gracefully
    }
  }

  /**
   * Checks if an update is pending manual confirmation.
   * @returns {Promise<boolean>} True if an update is pending.
   */
  async isUpdatePending(): Promise<boolean> {
    try {
      const pending = await AsyncStorage.getItem(STORAGE_KEYS.UPDATE_PENDING);
      return pending === 'true';
    } catch (error) {
      logger.error('Error checking update pending status', { error });
      return false;
    }
  }

  /**
   * Orchestrates the background sync process. Checks settings before proceeding.
   * Checks for updates to existing year data at any time, but only checks for new year data during Q4.
   * Returns true if an update was found (either applied or pending manual confirmation).
   */
  async runBackgroundSync(forceCheck: boolean = false): Promise<boolean> {
    logger.info(`Running background sync (forceCheck: ${forceCheck})...`);
    let updateFound = false; // Track if any update is identified

    const lockAcquired = await acquireUpdateLock();
    if (!lockAcquired) return updateFound; // Return false if lock not acquired

    try {
      // 1. Check network
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected || netInfo.isInternetReachable === false) {
        logger.info('Network unavailable, skipping background sync.');
        return updateFound; // Return false if network unavailable
      }

      // 2. Check if update check is needed
      const lastCheckStr = await AsyncStorage.getItem(STORAGE_KEYS.LAST_UPDATE_CHECK);
      const lastCheck = lastCheckStr ? parseInt(lastCheckStr, 10) : 0;
      const now = Date.now();

      if (!forceCheck && (now - lastCheck < UPDATE_CHECK_INTERVAL)) {
        logger.debug('Skipping sync check - checked recently.');
        return updateFound; // Return false if check interval not met
      }

      // 3. Perform checks and apply updates/downloads conditionally
      logger.info('Checking for calendar updates...');
      const updateNeeded = await this.applyUpdatesIfNeeded(); // Returns true if update found (applied or pending)

      logger.info('Checking for new year data (only proceeds in Q4)...');
      const newYearNeeded = await this.downloadNewYearIfNeeded(); // Returns true if new year found (downloaded or pending) and only checks in Q4

      updateFound = updateNeeded || newYearNeeded;

      // 4. Update last check timestamp only if a check was actually performed
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_UPDATE_CHECK, now.toString());

      if (updateFound) {
        const { autoUpdateEnabled } = useSettingsStore.getState();
        if (autoUpdateEnabled) {
          logger.info('Background sync completed: Data was updated automatically.');
        } else {
          logger.info('Background sync completed: Update available, pending user confirmation.');
        }
      } else {
        logger.info('Background sync completed: No updates or new data found.');
        // Clear pending flag if no updates are found anymore
        await AsyncStorage.removeItem(STORAGE_KEYS.UPDATE_PENDING);
      }

      return updateFound; // Return whether an update was found

    } catch (error) {
      logger.error('Error during background sync', { error });
      // Ensure updateFound is returned even on error, though it will likely be false
    } finally {
      await releaseUpdateLock(); // Ensure lock is always released
    }
    return updateFound; // Return the final status
  }

  /**
   * Checks for updates and applies them if necessary or sets pending flag.
   * @returns True if an update was found (either applied or pending), false otherwise.
   */
  private async applyUpdatesIfNeeded(): Promise<boolean> {
    let updateFound = false;
    try {
      const localYears = await databaseService.getAvailableYearsFromVersions();
      if (localYears.length === 0) {
        logger.info('No local years found to check for updates.');
        return updateFound; // false
      }

      logger.debug(`Checking updates for local years: ${localYears.join(', ')}`);

      // Get settings
      const { autoUpdateEnabled, autoUpdateWifiOnly } = useSettingsStore.getState();

      // Fetch server versions for local years
      const { data: serverVersionsData, error: serverVersionError } = await supabase
        .from('calendar_year_versions')
        .select('year, version')
        .in('year', localYears);

      if (serverVersionError) {
        logger.error('Error fetching server versions', { error: serverVersionError });
        return updateFound; // false
      }
      if (!serverVersionsData) {
        logger.warn('No server version data returned.');
        return updateFound; // false
      }

      const serverVersions = serverVersionsData as CalendarYearVersion[];
      let updatesAppliedCount = 0;

      for (const serverVersion of serverVersions) {
        const localVersion = await databaseService.getYearVersion(serverVersion.year);

        if (localVersion === null) {
           logger.warn(`Local version for year ${serverVersion.year} not found, skipping update check.`);
           continue;
        }

        if (serverVersion.version > localVersion) {
          logger.info(`Update needed for year ${serverVersion.year} (Local: ${localVersion}, Server: ${serverVersion.version})`);

          // Fetch updates from calendar_updates table
          const { data: updatesData, error: updatesError } = await supabase
            .from('calendar_updates')
            .select('eng_date, year, update_version, panchang_data')
            .eq('year', serverVersion.year)
            .gt('update_version', localVersion)
            .order('update_version', { ascending: true });

          if (updatesError) {
            logger.error(`Error fetching updates for ${serverVersion.year}`, { error: updatesError });
            continue; // Skip this year on error
          }

          if (updatesData && updatesData.length > 0) {
            updateFound = true; // Mark that an update is available
            logger.info(`Found ${updatesData.length} updates for year ${serverVersion.year}.`);

            // Check settings before proceeding with download/apply
            if (!autoUpdateEnabled) {
              logger.info(`Auto-update disabled. Setting pending flag for year ${serverVersion.year}.`);
              await AsyncStorage.setItem(STORAGE_KEYS.UPDATE_PENDING, 'true');
              continue; // Skip applying this year, move to the next
            }

            // Check Wi-Fi if required
            if (autoUpdateWifiOnly) {
              const netInfo = await NetInfo.fetch();
              if (netInfo.type !== NetInfoStateType.wifi) {
                logger.info(`Wi-Fi only enabled, but not on Wi-Fi. Skipping update for ${serverVersion.year}.`);
                // Don't set pending flag here, let it check again later
                continue; // Skip applying this year
              }
            }

            // Proceed with applying updates
            const updates = updatesData as CalendarUpdate[];
            const validUpdates: PanchangData[] = [];
            let invalidCount = 0;

            // Validate and map updates
            updates.forEach(u => {
              // Validate eng_date from the top-level column
              if (u.eng_date && typeof u.eng_date === 'string' && u.eng_date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                const { eng_date: _, ...restOfPanchangData } = u.panchang_data || {}; // Handle null panchang_data
                validUpdates.push({
                  ...restOfPanchangData, // Spread the rest of the fields from JSONB
                  eng_date: u.eng_date // Set the correct eng_date from the update record
                });
              } else {
                invalidCount++;
                logger.warn(`Skipping invalid update record for year ${serverVersion.year}: Invalid or missing eng_date ('${u.eng_date}'). Update version: ${u.update_version}`);
              }
            });

            if (invalidCount > 0) {
              logger.warn(`Skipped ${invalidCount} invalid update records for year ${serverVersion.year}.`);
            }

            if (validUpdates.length > 0) {
              // Apply valid updates and update local version within a try/catch
              try {
                logger.info(`Attempting to apply ${validUpdates.length} valid updates for year ${serverVersion.year}.`);
                await databaseService.insertOrReplacePanchangData(validUpdates);
                // Only update version if data insertion succeeds
                await databaseService.setYearVersion(serverVersion.year, serverVersion.version);
                logger.info(`Successfully applied ${validUpdates.length} updates and updated version for year ${serverVersion.year}.`);
                updatesAppliedCount += validUpdates.length;

                // // --- Invalidate Cache --- // REMOVED - Will be handled by caller
                // // Extract unique {year, month} pairs from the applied updates
                // const affectedMonths = [...new Set(validUpdates.map(upd => {
                //   const [year, month] = upd.eng_date.split('-').map(Number);
                //   return JSON.stringify({ year, month }); // Use JSON stringify for Set uniqueness
                // }))].map(str => JSON.parse(str));
                //
                // if (affectedMonths.length > 0) {
                //   dataCoordinator.invalidateCacheForMonths(affectedMonths);
                // }
                // // --- End Invalidate Cache ---

              } catch (applyError) {
                logger.error(`Failed to apply updates or set version for year ${serverVersion.year}`, { error: applyError });
                // Transaction should have rolled back in databaseService, version not updated
                // Do not proceed for this year
                continue;
              }
            } else {
               logger.info(`No valid updates to apply for ${serverVersion.year} > v${localVersion}.`);
               // If no valid updates but server version is higher, still update version number if auto-update is on
               if (autoUpdateEnabled) {
                 logger.info(`Updating version number only for ${serverVersion.year} as server version (${serverVersion.version}) is higher than local (${localVersion}).`);
                 try {
                    await databaseService.setYearVersion(serverVersion.year, serverVersion.version);
                 } catch (versionError) {
                    logger.error(`Failed to update version number for year ${serverVersion.year}`, { error: versionError });
                 }
               } else {
                 logger.info(`Auto-update disabled. Version number update for ${serverVersion.year} deferred.`);
                 await AsyncStorage.setItem(STORAGE_KEYS.UPDATE_PENDING, 'true');
               }
            }
          } else {
             logger.info(`No specific update entries found for ${serverVersion.year} > v${localVersion}, but server version is higher.`);
             updateFound = true; // Still consider this an available update
             // Still update the version number locally if auto-update is on
             if (autoUpdateEnabled) {
               logger.info(`Updating version number only for ${serverVersion.year}.`);
               try {
                  await databaseService.setYearVersion(serverVersion.year, serverVersion.version);
               } catch (versionError) {
                  logger.error(`Failed to update version number for year ${serverVersion.year}`, { error: versionError });
               }
             } else {
               logger.info(`Auto-update disabled. Version number update for ${serverVersion.year} deferred.`);
               await AsyncStorage.setItem(STORAGE_KEYS.UPDATE_PENDING, 'true');
             }
          }
        }
      }
      return updateFound;
    } catch (error) {
      logger.error('Error checking/applying updates', { error });
      return updateFound; // false or true depending on when error occurred
    }
  }

   /**
   * Checks for and downloads new year data if available and appropriate, or sets pending flag.
   * Only checks for new year data during Q4 (October-December).
   * @returns True if new year data was found (either downloaded or pending), false otherwise.
   */
  private async downloadNewYearIfNeeded(): Promise<boolean> {
     let newYearFound = false;
     try {
        const now = new Date();
        const currentMonth = now.getMonth() + 1; // 1-12
        const currentYear = now.getFullYear();
        const nextYear = (currentYear + 1).toString();

        // Only check for new year data during Q4 (October-December)
        const isQ4 = currentMonth >= 10;
        if (!isQ4) {
          logger.info(`Not in Q4 (current month: ${currentMonth}), skipping new year ${nextYear} check.`);
          return newYearFound; // false
        }

        // Get settings
        const { autoUpdateEnabled, autoUpdateWifiOnly } = useSettingsStore.getState();

        // Check if next year is already available locally
        const localVersion = await databaseService.getYearVersion(nextYear);
        if (localVersion !== null) {
          logger.debug(`Next year ${nextYear} already available locally (version ${localVersion}).`);
          return newYearFound; // false
        }

        // Check server for next year's version
        const { data: serverVersionData, error: serverVersionError } = await supabase
          .from('calendar_year_versions')
          .select('year, version')
          .eq('year', nextYear)
          .maybeSingle(); // Use maybeSingle to handle null result gracefully

        if (serverVersionError) {
          logger.error(`Error checking server for year ${nextYear}`, { error: serverVersionError });
          return newYearFound; // false
        }

        if (!serverVersionData) {
          logger.info(`Next year ${nextYear} not yet available on server.`);
          return newYearFound; // false
        }

        newYearFound = true; // Mark that new year data is available
        const serverVersion = serverVersionData as CalendarYearVersion;
        logger.info(`Next year ${nextYear} (Version ${serverVersion.version}) found on server. Checking download conditions...`);

        // Check settings before proceeding
        if (!autoUpdateEnabled) {
          logger.info(`Auto-update disabled. Setting pending flag for new year ${nextYear}.`);
          await AsyncStorage.setItem(STORAGE_KEYS.UPDATE_PENDING, 'true');
          return newYearFound; // true, but pending
        }

        // Check Wi-Fi if required
        if (autoUpdateWifiOnly) {
          const netInfo = await NetInfo.fetch();
          if (netInfo.type !== NetInfoStateType.wifi) {
            logger.info(`Wi-Fi only enabled, but not on Wi-Fi. Skipping download for new year ${nextYear}.`);
            // Don't set pending flag, let it check again later
            return newYearFound; // true, but skipped for now
          }
        }

        logger.info(`Downloading data for new year: ${nextYear}`);

        // Fetch all data for the new year from panchang_data table
        const { data: yearData, error: yearDataError } = await supabase
          .from('panchang_data')
          .select('*') // Select all columns defined in PanchangData
          .like('eng_date', `${nextYear}-%`) // Filter by year prefix
          .order('eng_date', { ascending: true });

        if (yearDataError) {
          logger.error(`Error fetching data for year ${nextYear}`, { error: yearDataError });
          return newYearFound; // true, but failed fetch
        }

        if (!yearData || yearData.length === 0) {
          logger.warn(`No panchang data found on server for year ${nextYear}, although version exists.`);
          // DO NOT update version locally if no data is found, but still mark as pending
          // This ensures future checks will attempt to download the data again

          // Still mark as pending because the version exists on the server
          await AsyncStorage.setItem(STORAGE_KEYS.UPDATE_PENDING, 'true');

          logger.info(`Not updating local version for ${nextYear} because no data was found. Will try again on next check.`);
          return newYearFound; // true, but no data found
        }

        logger.info(`Fetched ${yearData.length} records for year ${nextYear}. Storing locally...`);
        const panchangDataToInsert = yearData as unknown as PanchangData[]; // FIX: Type assertion

        // Insert data and set version within a try/catch
        try {
          // Insert the data and get the count of inserted records
          const insertedCount = await databaseService.insertOrReplacePanchangData(panchangDataToInsert);

          // Verify that data was actually inserted by checking the database
          const dataExists = await databaseService.verifyYearDataExists(nextYear);

          if (dataExists && insertedCount > 0) {
            // Only set version if data insertion succeeds and verification passes
            await databaseService.setYearVersion(nextYear, serverVersion.version);
            logger.info(`Successfully downloaded, stored data (${insertedCount} records), and set version for year ${nextYear}.`);

            // // --- Invalidate Cache for the entire downloaded year --- // REMOVED - Will be handled by caller
            // const yearNum = parseInt(nextYear, 10);
            // const allMonths = Array.from({ length: 12 }, (_, i) => ({ year: yearNum, month: i + 1 }));
            // dataCoordinator.invalidateCacheForMonths(allMonths);
            // // --- End Invalidate Cache ---

            return newYearFound; // true, downloaded
          } else {
            logger.warn(`Data insertion reported success but verification failed for year ${nextYear}. Inserted: ${insertedCount}, Exists: ${dataExists}`);
            // Do not update version if verification fails
            return newYearFound; // true, but verification failed
          }
        } catch (downloadError) {
           logger.error(`Failed to store data or set version for new year ${nextYear}`, { error: downloadError });
           // Transaction should have rolled back in databaseService
           return newYearFound; // true, but failed store
        }

     } catch (error) {
        logger.error('Error checking/downloading new year data', { error });
        return newYearFound; // false or true depending on when error occurred
     }
  }

  /**
   * Triggers a manual update check and application, bypassing auto-update settings.
   * Returns array of affected months {year, month} if updates were applied/downloaded, empty array otherwise.
   */
  async triggerManualUpdate(): Promise<{ year: number; month: number }[]> {
    logger.info('Triggering manual update...');
    let affectedMonths: { year: number; month: number }[] = [];

    // Ensure database is initialized before attempting manual update
    if (!databaseService.isDbInitialized) { // Use the public getter
        logger.error('Manual update failed: Database not initialized.');
        // Optionally: Show error to user
        return affectedMonths; // Return empty array
    }

    const lockAcquired = await acquireUpdateLock();
    if (!lockAcquired) {
      logger.warn('Manual update trigger failed: Sync already in progress.');
      return affectedMonths; // Return empty array
    }

    let success = false;
    try {
      // 1. Check network
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected || netInfo.isInternetReachable === false) {
        logger.error('Manual update failed: Network unavailable.');
        // Optionally: Show error to user
        return affectedMonths; // Return empty array
      }

      // 2. Force apply updates (internal function bypasses settings checks)
      const updateAffectedMonths = await this.forceApplyUpdates();

      // 3. Force download new year (internal function bypasses settings checks)
      const newYearAffectedMonths = await this.forceDownloadNewYear();

      // Combine affected months
      affectedMonths = [...updateAffectedMonths, ...newYearAffectedMonths];
      // Remove duplicates just in case (though unlikely)
      affectedMonths = [...new Set(affectedMonths.map(m => JSON.stringify(m)))].map(s => JSON.parse(s));

      success = affectedMonths.length > 0; // Success if any months were affected

      if (success) {
        logger.info('Manual update completed successfully, affected months:', { months: affectedMonths.map(m => `${m.year}-${m.month}`) });
        // Clear the pending flag as the user initiated the update
        await AsyncStorage.removeItem(STORAGE_KEYS.UPDATE_PENDING);
      } else {
        logger.info('Manual update completed: No updates or new data found/applied.');
        // Also clear flag if nothing was found
        await AsyncStorage.removeItem(STORAGE_KEYS.UPDATE_PENDING);
      }

    } catch (error) {
      logger.error('Error during manual update', { error });
      // Optionally: Show error to user
      success = false;
    } finally {
      await releaseUpdateLock();
    }
    return affectedMonths; // Return array of affected months
  }

  // --- Internal Force Methods (Bypass Settings Checks) ---

  /**
   * Checks if a specific year's data is available on the server.
   * @param targetYear The year string to check.
   * @returns Promise<{ available: boolean, version: number | null, error?: any }>
   */
  async checkServerYearAvailability(
    targetYear: string
  ): Promise<{ available: boolean; version: number | null; error?: any }> {
    logger.info(`Checking server availability for year ${targetYear}...`);
    try {
      const { data, error } = await supabase
        .from('calendar_year_versions')
        .select('year, version')
        .eq('year', targetYear)
        .maybeSingle();

      if (error) {
        logger.error(`Error checking server for year ${targetYear}`, { error });
        return { available: false, version: null, error };
      }

      if (data) {
        logger.info(`Year ${targetYear} found on server with version ${data.version}.`);
        return { available: true, version: data.version as number, error: null };
      } else {
        logger.info(`Year ${targetYear} not found on server.`);
        return { available: false, version: null, error: null };
      }
    } catch (catchError) {
      logger.error(`Unexpected error checking server availability for year ${targetYear}`, { catchError });
      return { available: false, version: null, error: catchError };
    }
  }

  /**
   * Force downloads all data for a specific year, bypassing settings checks and Q4 restriction.
   * This method is used for manual downloads triggered by the user.
   * @param targetYear The year string to download.
   * @returns Array of affected months {year, month} (all 12 for the new year) if downloaded, empty array otherwise.
   */
  async forceDownloadSpecificYearData(targetYear: string): Promise<{ year: number; month: number }[]> {
    logger.info(`Force downloading data for specific year: ${targetYear}`);
    let affectedMonths: { year: number; month: number }[] = [];

    // Ensure database is initialized
    if (!databaseService.isDbInitialized) {
      logger.error(`Force download for year ${targetYear} failed: Database not initialized.`);
      return affectedMonths;
    }

    // Acquire lock
    const lockAcquired = await acquireUpdateLock();
    if (!lockAcquired) {
      logger.warn(`Force download for year ${targetYear} failed: Sync already in progress.`);
      return affectedMonths;
    }

    try {
      // Check if year already exists locally, though force implies we might re-download/overwrite if version is higher.
      // For simplicity, this function will assume it needs to fetch server version and then data.
      // A more robust version might compare local/server versions first.

      const { data: serverVersionData, error: serverVersionError } = await supabase
        .from('calendar_year_versions')
        .select('year, version')
        .eq('year', targetYear)
        .maybeSingle();

      if (serverVersionError) {
        logger.error(`Error fetching server version for year ${targetYear} during specific year force download`, { error: serverVersionError });
        return affectedMonths;
      }

      if (!serverVersionData) {
        logger.info(`Year ${targetYear} not available on server. Cannot force download.`);
        return affectedMonths;
      }

      const serverVersion = serverVersionData as CalendarYearVersion;
      logger.info(`Server version for ${targetYear} is ${serverVersion.version}. Fetching data...`);

      const { data: yearData, error: yearDataError } = await supabase
        .from('panchang_data') // Assuming 'panchang_data' is the correct table for full year data
        .select('*')
        .like('eng_date', `${targetYear}-%`)
        .order('eng_date', { ascending: true });

      if (yearDataError) {
        logger.error(`Error fetching data for year ${targetYear} during specific year force download`, { error: yearDataError });
        return affectedMonths;
      }

      if (!yearData || yearData.length === 0) {
        logger.warn(`No panchang data found on server for year ${targetYear} during specific year force download, although version exists.`);
        // DO NOT update version if no data is found, even during specific year force download
        // This ensures future checks will attempt to download the data again
        logger.info(`Not updating local version for ${targetYear} because no data was found. Will try again on next check.`);
        return affectedMonths; // No data to process
      }

      const panchangDataToInsert = yearData as unknown as PanchangData[]; // FIX: Type assertion
      logger.info(`Fetched ${panchangDataToInsert.length} records for year ${targetYear}. Storing locally...`);

      // Insert the data and get the count of inserted records
      const insertedCount = await databaseService.insertOrReplacePanchangData(panchangDataToInsert);

      // Verify that data was actually inserted by checking the database
      const dataExists = await databaseService.verifyYearDataExists(targetYear);

      if (dataExists && insertedCount > 0) {
        // Only set version if data insertion succeeds and verification passes
        await databaseService.setYearVersion(targetYear, serverVersion.version);
        logger.info(`Successfully force downloaded, stored data (${insertedCount} records), and set version for year ${targetYear}.`);

        const yearNum = parseInt(targetYear, 10);
        affectedMonths = Array.from({ length: 12 }, (_, i) => ({ year: yearNum, month: i + 1 }));
        return affectedMonths;
      } else {
        logger.warn(`Force data insertion reported success but verification failed for year ${targetYear}. Inserted: ${insertedCount}, Exists: ${dataExists}`);
        // Do not update version if verification fails
        return affectedMonths; // Return empty array on verification failure
      }

    } catch (error) {
      logger.error(`Error during forceDownloadSpecificYearData for ${targetYear}`, { error });
      return affectedMonths; // Return empty array on error
    } finally {
      await releaseUpdateLock();
    }
  }

  /**
   * Internal method to apply updates, bypassing settings checks.
   * @returns Array of affected months {year, month} if updates were applied, empty array otherwise.
   */
  private async forceApplyUpdates(): Promise<{ year: number; month: number }[]> {
    // This logic is similar to applyUpdatesIfNeeded but without the
    // autoUpdateEnabled and autoUpdateWifiOnly checks.
    // Refactoring common logic into helper functions would be ideal,
    // but for now, we duplicate and modify.
    let allAffectedMonths: { year: number; month: number }[] = [];
    try {
      const localYears = await databaseService.getAvailableYearsFromVersions();
      if (localYears.length === 0) return allAffectedMonths;

      const { data: serverVersionsData, error: serverVersionError } = await supabase
        .from('calendar_year_versions')
        .select('year, version')
        .in('year', localYears);

      if (serverVersionError || !serverVersionsData) {
        logger.error('Error fetching server versions during force update', { error: serverVersionError });
        return allAffectedMonths;
      }

      const serverVersions = serverVersionsData as CalendarYearVersion[];

      for (const serverVersion of serverVersions) {
        const localVersion = await databaseService.getYearVersion(serverVersion.year);
        if (localVersion === null) continue;

        if (serverVersion.version > localVersion) {
          const { data: updatesData, error: updatesError } = await supabase
            .from('calendar_updates')
            .select('eng_date, year, update_version, panchang_data')
            .eq('year', serverVersion.year)
            .gt('update_version', localVersion)
            .order('update_version', { ascending: true });

          if (updatesError) {
             logger.error(`Error fetching updates for ${serverVersion.year} during force update`, { error: updatesError });
             continue;
          }

          if (updatesData && updatesData.length > 0) {
            const updates = updatesData as CalendarUpdate[];
            const validUpdates: PanchangData[] = updates
              .map(u => {
                if (u.eng_date && typeof u.eng_date === 'string' && u.eng_date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                  const { eng_date: _, ...restOfPanchangData } = u.panchang_data || {};
                  return { ...restOfPanchangData, eng_date: u.eng_date };
                }
                return null;
              })
              .filter((u): u is PanchangData => u !== null); // Type guard

            if (validUpdates.length > 0) {
              try {
                logger.info(`Force applying ${validUpdates.length} updates for year ${serverVersion.year}.`);
                await databaseService.insertOrReplacePanchangData(validUpdates);
                await databaseService.setYearVersion(serverVersion.year, serverVersion.version);
                // updatesAppliedCount += validUpdates.length; // No longer needed

                const affectedMonthsForYear = [...new Set(validUpdates.map(upd => JSON.stringify({ year: parseInt(upd.eng_date.substring(0, 4)), month: parseInt(upd.eng_date.substring(5, 7)) })))].map(str => JSON.parse(str));
                if (affectedMonthsForYear.length > 0) {
                  // dataCoordinator.invalidateCacheForMonths(affectedMonths); // REMOVED
                  allAffectedMonths = [...allAffectedMonths, ...affectedMonthsForYear]; // Collect affected months
                }
              } catch (applyError) {
                logger.error(`Failed to force apply updates for year ${serverVersion.year}`, { error: applyError });
                continue; // Skip this year on error
              }
            } else {
               // No valid updates, but version is higher. Force update version.
               logger.info(`Force updating version number only for ${serverVersion.year}.`);
               try {
                  await databaseService.setYearVersion(serverVersion.year, serverVersion.version);
               } catch (versionError) {
                  logger.error(`Failed to force update version number for year ${serverVersion.year}`, { error: versionError });
               }
            }
          } else {
             // No specific updates, but version is higher. Force update version.
             logger.info(`Force updating version number only for ${serverVersion.year}.`);
             try {
                await databaseService.setYearVersion(serverVersion.year, serverVersion.version);
             } catch (versionError) {
                logger.error(`Failed to force update version number for year ${serverVersion.year}`, { error: versionError });
             }
          }
        }
      }
    } catch (error) {
      logger.error('Error during forceApplyUpdates', { error });
    }
    // Remove duplicates before returning
    return [...new Set(allAffectedMonths.map(m => JSON.stringify(m)))].map(s => JSON.parse(s));
  }

  /**
   * Internal method to download new year data, bypassing settings checks and Q4 restriction.
   * This method is used for manual updates triggered by the user.
   * @returns Array of affected months {year, month} (all 12 for the new year) if downloaded, empty array otherwise.
   */
  private async forceDownloadNewYear(): Promise<{ year: number; month: number }[]> {
    // Similar to downloadNewYearIfNeeded but without settings checks or Q4 restriction
    let affectedMonths: { year: number; month: number }[] = [];
    try {
      const currentYear = new Date().getFullYear();
      const nextYear = (currentYear + 1).toString();

      const localVersion = await databaseService.getYearVersion(nextYear);
      if (localVersion !== null) return affectedMonths; // Already exists

      const { data: serverVersionData, error: serverVersionError } = await supabase
        .from('calendar_year_versions')
        .select('year, version')
        .eq('year', nextYear)
        .maybeSingle();

      if (serverVersionError || !serverVersionData) {
        logger.info(`Next year ${nextYear} not available on server during force check.`);
        return affectedMonths;
      }

      const serverVersion = serverVersionData as CalendarYearVersion;
      logger.info(`Force downloading data for new year: ${nextYear}`);

      const { data: yearData, error: yearDataError } = await supabase
        .from('panchang_data')
        .select('*')
        .like('eng_date', `${nextYear}-%`)
        .order('eng_date', { ascending: true });

      if (yearDataError) {
        logger.error(`Error fetching data for year ${nextYear} during force download`, { error: yearDataError });
        return affectedMonths;
      }

      if (!yearData || yearData.length === 0) {
        logger.warn(`No panchang data found on server for year ${nextYear} during force download.`);
        // DO NOT update version if no data is found, even during force download
        // This ensures future checks will attempt to download the data again
        logger.info(`Not updating local version for ${nextYear} because no data was found. Will try again on next check.`);
        return affectedMonths;
      }

      const panchangDataToInsert = yearData as unknown as PanchangData[]; // FIX: Type assertion
      try {
        // Insert the data and get the count of inserted records
        const insertedCount = await databaseService.insertOrReplacePanchangData(panchangDataToInsert);

        // Verify that data was actually inserted by checking the database
        const dataExists = await databaseService.verifyYearDataExists(nextYear);

        if (dataExists && insertedCount > 0) {
          // Only set version if data insertion succeeds and verification passes
          await databaseService.setYearVersion(nextYear, serverVersion.version);
          logger.info(`Successfully force downloaded and stored data (${insertedCount} records) for year ${nextYear}.`);

          const yearNum = parseInt(nextYear, 10);
          affectedMonths = Array.from({ length: 12 }, (_, i) => ({ year: yearNum, month: i + 1 }));
          // dataCoordinator.invalidateCacheForMonths(allMonths); // REMOVED
          return affectedMonths; // Return all months for the new year
        } else {
          logger.warn(`Force data insertion reported success but verification failed for year ${nextYear}. Inserted: ${insertedCount}, Exists: ${dataExists}`);
          // Do not update version if verification fails
          return affectedMonths; // Return empty array on verification failure
        }
      } catch (downloadError) {
         logger.error(`Failed to force store data for new year ${nextYear}`, { error: downloadError });
         return affectedMonths; // Return empty array on failure
      }
    } catch (error) {
      logger.error('Error during forceDownloadNewYear', { error });
      return affectedMonths; // Return empty array on error
    }
  }
}

// Export a singleton instance
export const syncService = new SyncService();
