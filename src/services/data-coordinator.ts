import { databaseService } from './database-service';
import { cacheService } from './cache-service';
import { syncService } from './sync-service'; // Import the sync service
import { PanchangData } from '@/types/database';
import { useCalendarStore } from '@/store/calendar-store'; // Import the calendar store
import { logger } from '@/utils/logging-sentry'; // Import the logger

/**
 * Calendar data coordinator
 * Manages database access and caching
 */

class CalendarDataCoordinator {
  private isInitialized = false;
  private initPromise: Promise<void> | null = null; // Promise to track initialization
  private prefetchInProgress = new Set<string>();
  private inFlightRequests = new Map<string, Promise<PanchangData[]>>();
  private reminderRequests = new Map<string, Promise<Set<number>>>();

  /**
   * Initialize the data coordinator
   * This will initialize the database and sync services.
   * Ensures initialization runs only once and subsequent calls wait for completion.
   */
  async initialize(): Promise<void> {
    // If initialization promise already exists, return it to wait for completion
    if (this.initPromise) {
      logger.debug('[DataCoordinator] Initialization already in progress, waiting for completion');
      return this.initPromise;
    }

    // Create the initialization promise
    this.initPromise = (async () => {
      // Log only when actual initialization starts
      logger.info('[DataCoordinator] Initializing data coordinator...');
      try {
        // Initialize the database service first
        logger.debug('[DataCoordinator] Starting database service initialization');
        try {
          await databaseService.initialize();
          logger.info('[DataCoordinator] Database service initialized successfully.');
        } catch (dbError) {
          logger.error('[DataCoordinator] Database service initialization failed', {
            error: dbError instanceof Error ? dbError.message : String(dbError),
            stack: dbError instanceof Error ? dbError.stack : 'No stack trace'
          });
          throw dbError; // Re-throw to be caught by the outer try/catch
        }

        // Initialize the sync service (which includes local version tracking)
        logger.debug('[DataCoordinator] Starting sync service initialization');
        try {
          await syncService.initializeSync();
          logger.info('[DataCoordinator] Sync service initialized successfully.');
        } catch (syncError) {
          logger.error('[DataCoordinator] Sync service initialization failed', {
            error: syncError instanceof Error ? syncError.message : String(syncError),
            stack: syncError instanceof Error ? syncError.stack : 'No stack trace'
          });
          throw syncError; // Re-throw to be caught by the outer try/catch
        }

        this.isInitialized = true;
        logger.info('[DataCoordinator] Initialization complete. Data coordinator is ready.');

        // Trigger background sync after a short delay (fire and forget)
        logger.debug('[DataCoordinator] Scheduling background sync for 5 seconds from now');
        setTimeout(() => {
          logger.info('[DataCoordinator] Triggering background sync...');
          syncService.runBackgroundSync()
            .then(() => {
              logger.info('[DataCoordinator] Background sync completed successfully');
            })
            .catch(error => {
              logger.error('[DataCoordinator] Error during background sync', {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : 'No stack trace'
              });
            });
        }, 5000); // 5-second delay

      } catch (error) {
        logger.error('[DataCoordinator] Initialization failed', {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : 'No stack trace'
        });
        this.isInitialized = false; // Ensure state reflects failure
        this.initPromise = null; // Clear promise on failure to allow retry
        throw error; // Re-throw error so callers know initialization failed
      }
      // Note: We don't clear the promise on success, so subsequent calls to initialize()
      // will return the already resolved promise.
    })();

    return this.initPromise;
  }

  // Helper to ensure initialization before running methods
  private async ensureInitialized(): Promise<void> {
      if (!this.isInitialized) {
          await this.initialize();
          // After awaiting, check again if it succeeded
          if (!this.isInitialized) {
              throw new Error('DataCoordinator failed to initialize.');
          }
      }
  }

  /**
   * Checks the availability status of a given year's data (local and remote).
   * @param year The year number to check.
   * @returns Promise<{ status: 'AVAILABLE_LOCALLY' | 'AVAILABLE_REMOTELY' | 'NOT_AVAILABLE_ANYWHERE' | 'CHECKING_AVAILABILITY' | 'ERROR_CHECKING_AVAILABILITY', serverVersion?: number | null }>
   */
  async getYearDisplayInfo(
    year: number
  ): Promise<{
    status: 'AVAILABLE_LOCALLY' | 'AVAILABLE_REMOTELY' | 'NOT_AVAILABLE_ANYWHERE' | 'CHECKING_AVAILABILITY' | 'ERROR_CHECKING_AVAILABILITY';
    serverVersion?: number | null;
  }> {
    const yearStr = year.toString();
    logger.debug(`[DataCoordinator] Checking year display info for ${yearStr}`);

    try {
      await this.ensureInitialized(); // Ensure initialization is complete

      // 1. Check local database
      const localVersion = await databaseService.getYearVersion(yearStr);
      if (localVersion !== null) {
        logger.debug(`[DataCoordinator] Year ${yearStr} found locally with version ${localVersion}`);
        return { status: 'AVAILABLE_LOCALLY' };
      }

      logger.debug(`[DataCoordinator] Year ${yearStr} not found locally. Checking server...`);

      // 2. Check remote server (Supabase)
      const serverCheckResult = await syncService.checkServerYearAvailability(yearStr);

      if (serverCheckResult.error) {
        logger.error(`[DataCoordinator] Error checking server availability for year ${yearStr}`, { error: serverCheckResult.error });
        return { status: 'ERROR_CHECKING_AVAILABILITY' };
      }

      if (serverCheckResult.available) {
        logger.debug(`[DataCoordinator] Year ${yearStr} found remotely with version ${serverCheckResult.version}`);
        return { status: 'AVAILABLE_REMOTELY', serverVersion: serverCheckResult.version };
      } else {
        logger.debug(`[DataCoordinator] Year ${yearStr} not available anywhere.`);
        return { status: 'NOT_AVAILABLE_ANYWHERE' };
      }
    } catch (error) {
      logger.error(`[DataCoordinator] Unexpected error in getYearDisplayInfo for year ${yearStr}`, { error });
      return { status: 'ERROR_CHECKING_AVAILABILITY' };
    }
  }

   /**
   * Manually triggers the download of data for a specific year.
   * @param yearToDownload The year number to download.
   * @returns Promise<boolean> True if download was successful and cache invalidated, false otherwise.
   */
  async manuallyDownloadYearData(yearToDownload: number): Promise<boolean> {
    const yearStr = yearToDownload.toString();
    logger.info(`[DataCoordinator] Triggering manual download for year ${yearStr}...`);

    try {
      await this.ensureInitialized(); // Ensure initialization is complete

      const affectedMonths = await syncService.forceDownloadSpecificYearData(yearStr);

      if (affectedMonths.length > 0) {
        logger.info(`[DataCoordinator] Download for year ${yearStr} successful. Invalidating cache...`);
        this.invalidateCacheForMonths(affectedMonths);
        return true;
      } else {
        logger.warn(`[DataCoordinator] Download for year ${yearStr} completed, but no months were affected (or download failed in syncService).`);
        // Check if the year is now locally available even if sync returned empty array (e.g., version updated without data)
        const localVersion = await databaseService.getYearVersion(yearStr);
        if (localVersion !== null) {
           logger.info(`[DataCoordinator] Year ${yearStr} is now locally available (version ${localVersion}). Invalidating cache anyway.`);
           // Invalidate cache even if sync reported no affected months, as version might have updated
           const allMonths = Array.from({ length: 12 }, (_, i) => ({ year: yearToDownload, month: i + 1 }));
           this.invalidateCacheForMonths(allMonths);
           return true; // Consider it a success if the version is now local
        }
        return false; // Download likely failed or year wasn't available
      }
    } catch (error) {
      logger.error(`[DataCoordinator] Error during manual download trigger for year ${yearStr}`, { error });
      return false;
    }
  }


  /**
   * Get month data with caching
   * @param year The year
   * @param month The month (1-12)
   * @returns The month data
   */
  async getMonthData(year: number, month: number): Promise<PanchangData[]> {
    await this.ensureInitialized(); // Ensure initialization is complete

    const key = `${year}-${month}`;

    // Return existing in-flight request if one exists
    const existingRequest = this.inFlightRequests.get(key);
    if (existingRequest) {
      logger.debug(`Using in-flight request for ${year}-${month}`);
      return existingRequest;
    }

    // Check cache first
    const cachedData = cacheService.getMonthData(year, month);
    if (cachedData) {
      // Cache logging is handled by cache service
      // Trigger prefetch after returning cached data
      queueMicrotask(() => this.prefetchAdjacentMonths(year, month));
      return cachedData;
    }

    // Create new request
    const request = (async () => {
      try {
        // If not in cache, get from database
        logger.debug(`Fetching data for ${year}-${month} from database`);
        const dbData = await databaseService.getMonthData(year, month);

        // Store in cache
        cacheService.setMonthData(year, month, dbData);

        // Queue prefetch as microtask to not block the return of current data
        queueMicrotask(() => this.prefetchAdjacentMonths(year, month));

        return dbData;
      } finally {
        // Clean up the in-flight request
        this.inFlightRequests.delete(key);
      }
    })();

    // Store the in-flight request
    this.inFlightRequests.set(key, request);

    return request;
  }

  /**
   * Get date data (can extract from month data if available)
   * @param dateString The date string in YYYY-MM-DD format
   * @returns The date data or null if not found
   */
  async getDateData(dateString: string): Promise<PanchangData | null> {
    await this.ensureInitialized(); // Ensure initialization is complete

    // Extract year and month from date string
    const [yearStr, monthStr] = dateString.split('-');
    const year = parseInt(yearStr);
    const month = parseInt(monthStr);

    // Check if month data is in cache
    const monthData = cacheService.getMonthData(year, month);

    if (monthData) {
      // Find the specific date in the cached month data
      const dateData = monthData.find(item => item.eng_date === dateString);
      if (dateData) {
        return dateData;
      }
    }

    // If not found in cache, get directly from database
    return databaseService.getDateData(dateString);
  }

  /**
   * Get festivals for month (can extract from month data if available)
   * @param year The year
   * @param month The month (1-12)
   * @returns The festival dates for the month
   */
  async getFestivalsForMonth(year: number, month: number): Promise<PanchangData[]> {
    await this.ensureInitialized(); // Ensure initialization is complete

    // Check if month data is in cache
    const monthData = cacheService.getMonthData(year, month);

    if (monthData) {
      // Filter festival dates from cached month data
      return monthData.filter(item => item.festivals && item.festivals.trim() !== '');
    }

    // If month data not in cache, get festivals directly from database
    return databaseService.getFestivalsForMonth(year, month);
  }

  /**
   * Get marriage dates for month (can extract from month data if available)
   * @param year The year
   * @param month The month (1-12)
   * @returns The marriage dates for the month
   */
  async getMarriageDatesForMonth(year: number, month: number): Promise<PanchangData[]> {
    await this.ensureInitialized(); // Ensure initialization is complete

    // Check if month data is in cache
    const monthData = cacheService.getMonthData(year, month);

    if (monthData) {
      // Filter marriage dates from cached month data
      return monthData.filter(item => item.is_marriage_date === 1);
    }

    // If month data not in cache, get marriage dates directly from database
    return databaseService.getMarriageDatesForMonth(year, month);
  }

  /**
   * Get brata ghara dates for month (can extract from month data if available)
   * @param year The year
   * @param month The month (1-12)
   * @returns The brata ghara dates for the month
   */
  async getBrataGharaDatesForMonth(year: number, month: number): Promise<PanchangData[]> {
    await this.ensureInitialized(); // Ensure initialization is complete

    // Check if month data is in cache
    const monthData = cacheService.getMonthData(year, month);

    if (monthData) {
      // Filter brata ghara dates from cached month data
      return monthData.filter(item => item.is_brata_ghara_date === 1);
    }

    // If month data not in cache, get brata ghara dates directly from database
    return databaseService.getBrataGharaDatesForMonth(year, month);
  }

  /**
   * Get Odia months for a Gregorian month (utilizes cached month data)
   * @param year The year
   * @param month The month (1-12)
   * @returns A string with the Odia month names (e.g., "ମାଘ-ଫାଲ୍ଗୁନ")
   */
  async getOdiaMonthsForMonth(year: number, month: number): Promise<string> {
    await this.ensureInitialized(); // Ensure initialization is complete

    // Check if month data is in cache first
    const monthData = cacheService.getMonthData(year, month);

    if (monthData && monthData.length > 0) {
      // Extract unique Odia months from cached data
      const uniqueOdiaMonths = Array.from(
        new Set(monthData.map(day => day.odia_month).filter(Boolean))
      );

      if (uniqueOdiaMonths.length === 0) {
        // Fallback to static mapping if no odia_month data
        return this.getFallbackOdiaMonths(month);
      } else if (uniqueOdiaMonths.length === 1) {
        return uniqueOdiaMonths[0];
      } else {
        // Sort months to ensure consistent order (first to last in the month)
        return `${uniqueOdiaMonths[0]}-${uniqueOdiaMonths[uniqueOdiaMonths.length - 1]}`;
      }
    }

    // If month data not in cache, fetch from database and extract Odia months
    try {
      const monthData = await this.getMonthData(year, month);

      if (monthData.length > 0) {
        const uniqueOdiaMonths = Array.from(
          new Set(monthData.map(day => day.odia_month).filter(Boolean))
        );

        if (uniqueOdiaMonths.length === 0) {
          return this.getFallbackOdiaMonths(month);
        } else if (uniqueOdiaMonths.length === 1) {
          return uniqueOdiaMonths[0];
        } else {
          return `${uniqueOdiaMonths[0]}-${uniqueOdiaMonths[uniqueOdiaMonths.length - 1]}`;
        }
      }
    } catch (error) {
      logger.error('Error fetching Odia months from database', { year, month, error });
    }

    // Final fallback to static mapping
    return this.getFallbackOdiaMonths(month);
  }

  /**
   * Fallback method for Odia months using static mapping
   * @param month The month (1-12)
   * @returns A string with the Odia month names
   */
  private getFallbackOdiaMonths(month: number): string {
    const FALLBACK_MAPPING: Record<number, string[]> = {
      1: ['ପୌଷ', 'ମାଘ'],       // January: Pausha, Magha
      2: ['ମାଘ', 'ଫାଲ୍ଗୁନ'],    // February: Magha, Phalguna
      3: ['ଫାଲ୍ଗୁନ', 'ଚୈତ୍ର'],   // March: Phalguna, Chaitra
      4: ['ଚୈତ୍ର', 'ବୈଶାଖ'],     // April: Chaitra, Vaisakha
      5: ['ବୈଶାଖ', 'ଜ୍ୟେଷ୍ଠ'],   // May: Vaisakha, Jyestha
      6: ['ଜ୍ୟେଷ୍ଠ', 'ଅଷାଢ଼'],    // June: Jyestha, Ashadha
      7: ['ଅଷାଢ଼', 'ଶ୍ରାବଣ'],     // July: Ashadha, Shravana
      8: ['ଶ୍ରାବଣ', 'ଭାଦ୍ରବ'],    // August: Shravana, Bhadrava
      9: ['ଭାଦ୍ରବ', 'ଆଶ୍ୱିନ'],    // September: Bhadrava, Ashwin
      10: ['ଆଶ୍ୱିନ', 'କାର୍ତ୍ତିକ'],  // October: Ashwin, Kartika
      11: ['କାର୍ତ୍ତିକ', 'ମାର୍ଗଶିର'],  // November: Kartika, Margashira
      12: ['ମାର୍ଗଶିର', 'ପୌଷ']    // December: Margashira, Pausha
    };

    const odiaMonths = FALLBACK_MAPPING[month] || [];

    if (odiaMonths.length === 0) {
      return '';
    } else if (odiaMonths.length === 1) {
      return odiaMonths[0];
    } else {
      return `${odiaMonths[0]}-${odiaMonths[1]}`;
    }
  }

  /**
   * Get reminder dates for month with caching and deduplication
   * @param year The year
   * @param month The month (1-12)
   * @returns Set of day numbers that have reminders
   */
  async getReminderDatesForMonth(year: number, month: number): Promise<Set<number>> {
    await this.ensureInitialized(); // Ensure initialization is complete

    const key = `reminder-${year}-${month}`;

    // Return existing in-flight request if one exists
    const existingRequest = this.reminderRequests.get(key);
    if (existingRequest) {
      logger.debug(`Using in-flight reminder request for ${year}-${month}`);
      return existingRequest;
    }

    // Check cache first
    const cachedData = cacheService.getReminderDates(year, month);
    if (cachedData) {
      return cachedData;
    }

    // Create new request
    const request = (async () => {
      try {
        logger.debug(`Fetching reminder dates for ${year}-${month} from scheduled_notifications`);

        // Simple query: get all scheduled notifications for this month
        // Convert UTC stored dates to local timezone for proper calendar display
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${year}-${month.toString().padStart(2, '0')}-31`;

        // Get timezone offset in minutes and convert to hours/minutes for SQLite
        const timezoneOffsetMinutes = new Date().getTimezoneOffset();
        const offsetHours = Math.floor(Math.abs(timezoneOffsetMinutes) / 60);
        const offsetMinutes = Math.abs(timezoneOffsetMinutes) % 60;
        const offsetSign = timezoneOffsetMinutes <= 0 ? '+' : '-'; // Note: getTimezoneOffset returns negative for positive offsets

        const timezoneModifier = `'${offsetSign}${offsetHours} hours', '${offsetSign}${offsetMinutes} minutes'`;

        logger.debug('Timezone conversion for reminder dates', {
          timezoneOffsetMinutes,
          offsetHours,
          offsetMinutes,
          offsetSign,
          timezoneModifier
        });

        const query = `
          SELECT DISTINCT DATE(scheduled_date, ${timezoneModifier}) as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, ${timezoneModifier}) >= '${startDate}'
          AND DATE(scheduled_date, ${timezoneModifier}) <= '${endDate}'
        `;

        const results = await databaseService.executeQuery(query);

        // Extract day numbers from the results
        const datesWithReminders = new Set<number>();
        results.forEach((row: any) => {
          const dateStr = row.date_only;
          const day = parseInt(dateStr.split('-')[2]);
          datesWithReminders.add(day);
        });

        // Cache the result
        cacheService.setReminderDates(year, month, datesWithReminders);

        logger.debug('Found reminder dates for month from scheduled_notifications', {
          year,
          month,
          scheduledCount: results.length,
          datesWithReminders: Array.from(datesWithReminders)
        });

        return datesWithReminders;
      } finally {
        // Clean up the in-flight request
        this.reminderRequests.delete(key);
      }
    })();

    // Store the in-flight request
    this.reminderRequests.set(key, request);

    return request;
  }

  /**
   * Get reminder details for a specific date
   * @param dateString The date string in YYYY-MM-DD format
   * @returns Array of reminders for the date
   */
  async getReminderDetailsForDate(dateString: string): Promise<any[]> {
    await this.ensureInitialized(); // Ensure initialization is complete

    try {
      logger.debug(`Fetching reminder details for date: ${dateString}`);

      // Query to get reminder details for the specific date
      // Convert UTC stored dates to local timezone for proper date matching

      // Get timezone offset in minutes and convert to hours/minutes for SQLite
      const timezoneOffsetMinutes = new Date().getTimezoneOffset();
      const offsetHours = Math.floor(Math.abs(timezoneOffsetMinutes) / 60);
      const offsetMinutes = Math.abs(timezoneOffsetMinutes) % 60;
      const offsetSign = timezoneOffsetMinutes <= 0 ? '+' : '-'; // Note: getTimezoneOffset returns negative for positive offsets

      const timezoneModifier = `'${offsetSign}${offsetHours} hours', '${offsetSign}${offsetMinutes} minutes'`;

      const query = `
        SELECT
          ur.id,
          ur.title,
          ur.description,
          ur.notification_time as notificationTime,
          ur.sound_name as soundName,
          ur.reminder_type as reminderType,
          ur.odia_month as odiaMonth,
          ur.paksha,
          ur.tithi,
          sn.scheduled_date as scheduledDate
        FROM user_reminders ur
        JOIN scheduled_notifications sn ON ur.id = sn.reminder_id
        WHERE sn.status = 'scheduled'
        AND DATE(sn.scheduled_date, ${timezoneModifier}) = '${dateString}'
        AND ur.is_enabled = 1
        ORDER BY ur.notification_time
      `;

      const results = await databaseService.executeQuery(query);

      logger.debug('Found reminder details for date', {
        dateString,
        reminderCount: results.length
      });

      return results;
    } catch (error) {
      logger.error('Error fetching reminder details for date', { dateString, error });
      return [];
    }
  }

  /**
   * Get scheduled notifications for a specific reminder
   * @param reminderId The reminder ID
   * @returns Array of scheduled notifications with timezone conversion
   */
  async getScheduledNotificationsForReminder(reminderId: number): Promise<any[]> {
    await this.ensureInitialized(); // Ensure initialization is complete

    try {
      logger.debug(`Fetching scheduled notifications for reminder: ${reminderId}`);

      // Get timezone offset for proper date conversion
      const timezoneOffsetMinutes = new Date().getTimezoneOffset();
      const offsetHours = Math.floor(Math.abs(timezoneOffsetMinutes) / 60);
      const offsetMinutes = Math.abs(timezoneOffsetMinutes) % 60;
      const offsetSign = timezoneOffsetMinutes <= 0 ? '+' : '-';

      const timezoneModifier = `'${offsetSign}${offsetHours} hours', '${offsetSign}${offsetMinutes} minutes'`;

      // Query to get scheduled notifications with panchang data
      const query = `
        SELECT
          sn.id,
          sn.notification_id as notificationId,
          sn.scheduled_date as scheduledDate,
          sn.status,
          DATE(sn.scheduled_date, ${timezoneModifier}) as localDate,
          pd.odia_date as odiaDate,
          pd.odia_month as odiaMonth,
          pd.paksha,
          pd.tithi_name as tithi
        FROM scheduled_notifications sn
        LEFT JOIN panchang_data pd ON DATE(sn.scheduled_date, ${timezoneModifier}) = pd.eng_date
        WHERE sn.reminder_id = ${reminderId}
        AND sn.status = 'scheduled'
        AND DATE(sn.scheduled_date, ${timezoneModifier}) >= DATE('now', 'localtime')
        ORDER BY sn.scheduled_date ASC
      `;

      const results = await databaseService.executeQuery(query);

      logger.debug('Found scheduled notifications for reminder', {
        reminderId,
        notificationCount: results.length
      });

      return results;
    } catch (error) {
      logger.error('Error fetching scheduled notifications for reminder', { reminderId, error });
      return [];
    }
  }

  /**
   * Prefetch adjacent months (calendar + reminder data)
   * @param year The year
   * @param month The month (1-12)
   */
  private async prefetchAdjacentMonths(year: number, month: number): Promise<void> {
    // Calculate adjacent months
    const prevMonth = month === 1 ? 12 : month - 1;
    const prevYear = month === 1 ? year - 1 : year;
    const nextMonth = month === 12 ? 1 : month + 1;
    const nextYear = month === 12 ? year + 1 : year;

    // Helper to generate a unique key for tracking prefetch operations
    const getPrefetchKey = (y: number, m: number) => `${y}-${m}`;

    // Function to prefetch a single month if needed
    const prefetchMonth = async (y: number, m: number) => {
      const key = getPrefetchKey(y, m);

      // Skip if already in cache or currently being prefetched
      if (cacheService.hasMonthData(y, m) || this.prefetchInProgress.has(key)) {
        return;
      }

      // Mark as in progress
      this.prefetchInProgress.add(key);

      try {
        // Prefetch calendar data
        const monthData = await databaseService.getMonthData(y, m);
        cacheService.setMonthData(y, m, monthData);

        // Prefetch reminder data (lightweight query)
        const reminderDates = await this.getReminderDatesForMonth(y, m);
        // getReminderDatesForMonth already handles caching internally

        logger.debug(`Prefetched data for ${y}-${m}`, {
          calendarDays: monthData.length,
          reminderDays: reminderDates.size
        });
      } catch (error) {
        logger.error(`Failed to prefetch ${y}-${m}`, { error });
      } finally {
        this.prefetchInProgress.delete(key);
      }
    };

    // Prefetch both months in parallel
    await Promise.all([
      prefetchMonth(prevYear, prevMonth),
      prefetchMonth(nextYear, nextMonth)
    ]).catch(error => {
      logger.error('Error during prefetch operations', { error });
    });
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    cacheService.clearAll();
    // Also notify UI that cache was cleared (might affect displayed data)
    useCalendarStore.getState().notifyDataUpdated();
  }

  /**
   * Invalidate reminder cache and refresh current month
   * Called when reminders are added/updated/deleted
   */
  async invalidateReminderCache(): Promise<void> {
    logger.debug('Invalidating reminder cache');

    // Clear all reminder cache
    cacheService.clearAllReminderCache();

    // Get current month from store to refresh it immediately
    const { selectedYear, selectedMonth } = useCalendarStore.getState();

    // Pre-fetch current month reminder data to refresh cache
    try {
      await this.getReminderDatesForMonth(selectedYear, selectedMonth);
      logger.debug('Refreshed reminder cache for current month', {
        year: selectedYear,
        month: selectedMonth
      });
    } catch (error) {
      logger.error('Error refreshing reminder cache for current month', {
        year: selectedYear,
        month: selectedMonth,
        error
      });
    }

    // Notify UI that reminder data was updated
    useCalendarStore.getState().notifyDataUpdated();
  }

  /**
   * Delete data for a specific year and invalidate cache
   * @param year The year to delete
   * @returns Result of the deletion operation
   */
  async deleteYearData(year: number): Promise<{ deletedRecords: number; success: boolean; errorCode?: string }> {
    logger.info(`[DataCoordinator] Deleting data for year ${year}...`);

    try {
      await this.ensureInitialized(); // Ensure initialization is complete

      // Delete the year data from the database
      const result = await databaseService.deleteYearData(year.toString());

      if (result.success) {
        logger.info(`[DataCoordinator] Successfully deleted ${result.deletedRecords} records for year ${year}. Invalidating cache...`);

        // Invalidate cache for all months of the year
        const allMonths = Array.from({ length: 12 }, (_, i) => ({ year, month: i + 1 }));
        this.invalidateCacheForMonths(allMonths);
      } else {
        if (result.errorCode === 'CURRENT_YEAR') {
          logger.warn(`[DataCoordinator] Cannot delete current year ${year}.`);
        } else if (result.errorCode === 'NO_DATA') {
          logger.warn(`[DataCoordinator] No data found for year ${year}, nothing to delete.`);
        } else {
          logger.warn(`[DataCoordinator] Failed to delete data for year ${year}.`);
        }
      }

      return result;
    } catch (error) {
      logger.error(`[DataCoordinator] Error deleting data for year ${year}`, { error });
      return { deletedRecords: 0, success: false, errorCode: 'ERROR' };
    }
  }

  /**
   * Invalidate cache for specific months and notify UI
   * @param months Array of { year, month } objects to invalidate
   */
  invalidateCacheForMonths(months: { year: number; month: number }[]): void {
    if (!months || months.length === 0) {
      return;
    }

    logger.info(`[DataCoordinator] Invalidating cache for months`, { months: months.map(m => `${m.year}-${m.month}`) });
    let cacheCleared = false;
    months.forEach(({ year, month }) => {
      if (cacheService.hasMonthData(year, month)) {
        cacheService.clearMonthData(year, month);
        cacheCleared = true;
      }
    });

    // If any cache entries were actually cleared, notify the UI
    if (cacheCleared) {
      logger.info('[DataCoordinator] Cache invalidated, notifying UI.');
      useCalendarStore.getState().notifyDataUpdated();
    }
  }
}

// Export a singleton instance
export const dataCoordinator = new CalendarDataCoordinator();
