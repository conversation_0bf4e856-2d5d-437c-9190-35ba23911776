import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import * as StoreReview from 'expo-store-review';
import { appConfig } from '@/config/appConfig';
import * as Sentry from '@sentry/react-native';
import { Platform, Linking } from 'react-native';
import { logger } from '@/utils/logging-sentry';

// Constants for review prompt logic
const REVIEW_STORAGE_KEYS = {
  APP_OPEN_COUNT: 'appOpenCount',
  LAST_REVIEW_REQUEST_TIMESTAMP: 'lastReviewRequestTimestamp',
  LAST_REVIEW_REQUEST_APP_VERSION: 'lastReviewRequestAppVersion',
  LAST_CHECK_TIMESTAMP: 'lastReviewCheckTimestamp',
};

// Time between checks to prevent multiple rapid checks
const CHECK_INTERVAL_MS = 5000; // 5 seconds

/** Get major version from version string (e.g., "1.2.3" -> "1") */
const getMajorVersion = (version: string | null | undefined): string | null => {
  if (!version) return null;
  return version.split('.')[0] || null;
};

class ReviewService {
  private static _instance: ReviewService;
  private lastCheckTime: number = 0;

  private constructor() {
    // Initialize any required state here
  }

  public static get instance(): ReviewService {
    if (!ReviewService._instance) {
      ReviewService._instance = new ReviewService();
    }
    return ReviewService._instance;
  }

  /** Check if enough time has passed since the last check */
  private shouldCheck(): boolean {
    const now = Date.now();
    if (now - this.lastCheckTime < CHECK_INTERVAL_MS) {
      logger.debug('[Review Service] Skipping check - checked recently');
      return false;
    }
    this.lastCheckTime = now;
    return true;
  }

  /** Get the store URL for direct store navigation */
  private getStoreUrl(): string {
    return Platform.select({
      ios: `https://apps.apple.com/app/odia-calendar/id6738234567?action=write-review`,
      android: `https://play.google.com/store/apps/details?id=${appConfig.store.androidPackageName}&showAllReviews=true`,
      default: `https://play.google.com/store/apps/details?id=${appConfig.store.androidPackageName}&showAllReviews=true`,
    });
  }

  /**
   * Request in-app review with automatic fallback to store
   * This is for automatic prompts based on usage patterns
   */
  public async requestInAppReview(): Promise<boolean> {
    try {
      // Skip if checked recently
      if (!this.shouldCheck()) {
        return false;
      }

      // Get stored values
      const [
        openCountStr,
        lastRequestTimestampStr,
        lastRequestAppVersion,
      ] = await Promise.all([
        AsyncStorage.getItem(REVIEW_STORAGE_KEYS.APP_OPEN_COUNT),
        AsyncStorage.getItem(REVIEW_STORAGE_KEYS.LAST_REVIEW_REQUEST_TIMESTAMP),
        AsyncStorage.getItem(REVIEW_STORAGE_KEYS.LAST_REVIEW_REQUEST_APP_VERSION),
      ]);

      const openCount = parseInt(openCountStr || '0', 10);
      const lastRequestTimestamp = parseInt(lastRequestTimestampStr || '0', 10);
      const currentAppVersion = Constants.expoConfig?.version;
      const currentTimestamp = Date.now();

      // Check conditions for showing review
      const meetsOpenCountThreshold = openCount >= appConfig.review.minOpens;
      const timeSinceLastRequest = currentTimestamp - lastRequestTimestamp;
      const meetsTimeThreshold = timeSinceLastRequest >= appConfig.review.minDaysBetweenRequests * 24 * 60 * 60 * 1000;
      const isNewMajorVersion = getMajorVersion(currentAppVersion) !== getMajorVersion(lastRequestAppVersion);

      // Log review check status
      logger.info('[Review Service] Auto-review check:', {
        opens: openCount,
        lastRequestTime: new Date(lastRequestTimestamp).toISOString(),
        lastRequestVersion: lastRequestAppVersion,
        currentVersion: currentAppVersion,
      });

      if (meetsOpenCountThreshold && (meetsTimeThreshold || isNewMajorVersion)) {
        logger.info('[Review Service] Conditions met, requesting in-app review...');

        // Try in-app review first, fallback to store if not available
        const result = await this.tryInAppReviewWithFallback();

        if (result) {
          // Update tracking info
          await Promise.all([
            AsyncStorage.setItem(REVIEW_STORAGE_KEYS.LAST_REVIEW_REQUEST_TIMESTAMP, currentTimestamp.toString()),
            currentAppVersion ? AsyncStorage.setItem(REVIEW_STORAGE_KEYS.LAST_REVIEW_REQUEST_APP_VERSION, currentAppVersion) : null,
          ].filter(Boolean));

          logger.info('[Review Service] Review requested and tracking updated');
          return true;
        } else {
          logger.info('[Review Service] Review request failed');
          return false;
        }
      } else {
        logger.info('[Review Service] Auto-review conditions not met');
        return false;
      }
    } catch (error) {
      logger.error('[ReviewService] Error in auto-review logic', { error });
      Sentry.captureException(error);
      return false;
    }
  }

  /** Increment the app open count */
  public async incrementOpenCount(): Promise<void> {
    try {
      const openCountStr = await AsyncStorage.getItem(REVIEW_STORAGE_KEYS.APP_OPEN_COUNT);
      const openCount = parseInt(openCountStr || '0', 10);
      await AsyncStorage.setItem(REVIEW_STORAGE_KEYS.APP_OPEN_COUNT, (openCount + 1).toString());
    } catch (error) {
      logger.error('[ReviewService] Error incrementing open count', { error });
      Sentry.captureException(error);
    }
  }

  /**
   * Try in-app review first, fallback to store if not available
   * Used for automatic prompts
   */
  private async tryInAppReviewWithFallback(): Promise<boolean> {
    try {
      // Check if in-app review is available
      if (await StoreReview.hasAction()) {
        logger.info('[Review Service] In-app review available, requesting...');
        await StoreReview.requestReview();
        return true;
      } else {
        logger.info('[Review Service] In-app review not available, using store fallback');
        return await this.openStoreForReview();
      }
    } catch (error) {
      logger.error('[ReviewService] Error in in-app review, trying store fallback', { error });
      return await this.openStoreForReview();
    }
  }

  /**
   * Open store directly for review (for manual button presses)
   * Always goes to store, no in-app review
   */
  public async openStoreForReview(): Promise<boolean> {
    try {
      const storeUrl = this.getStoreUrl();
      const canOpen = await Linking.canOpenURL(storeUrl);
      if (canOpen) {
        await Linking.openURL(storeUrl);
        logger.info('[Review Service] Opened store for review');
        return true;
      }
      return false;
    } catch (error) {
      logger.error('[ReviewService] Error opening store for review', { error });
      Sentry.captureException(error);
      return false;
    }
  }

  /**
   * Force in-app review (for settings screen)
   * Tries in-app first, then store fallback
   */
  public async requestManualReview(): Promise<boolean> {
    try {
      logger.info('[Review Service] Manual review requested');
      return await this.tryInAppReviewWithFallback();
    } catch (error) {
      logger.error('[ReviewService] Error in manual review request', { error });
      Sentry.captureException(error);
      return false;
    }
  }
}

// Export singleton instance
export const reviewService = ReviewService.instance;
