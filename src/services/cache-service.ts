import { PanchangData } from '@/types/database';
import { logger } from '@/utils/logging-sentry';
import { appConfig } from '@/config/appConfig';

/**
 * Cache entry with data and timestamp
 */
type CacheEntry<T> = {
  data: T;
  timestamp: number;
};

/**
 * Calendar cache service
 * Provides in-memory caching for calendar data
 */
class CalendarCache {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private readonly DEFAULT_TTL = appConfig.cache.defaultTTL; // Default TTL from appConfig
  private cacheHitTracking = new Map<string, number>();

  /**
   * Store month data in cache
   * @param year The year
   * @param month The month (1-12)
   * @param data The month data
   * @param ttl Time to live in milliseconds (optional)
   */
  setMonthData(year: number, month: number, data: PanchangData[], ttl?: number): void {
    const key = this.getMonthKey(year, month);
    this.cache.set(key, {
      data,
      timestamp: Date.now() + (ttl || this.DEFAULT_TTL)
    });
  }

  /**
   * Get month data from cache
   * @param year The year
   * @param month The month (1-12)
   * @returns The month data or null if not in cache or expired
   */
  getMonthData(year: number, month: number): PanchangData[] | null {
    const key = this.getMonthKey(year, month);
    const entry = this.cache.get(key);

    if (!entry) return null;

    // Check if entry has expired
    if (entry.timestamp < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    // Track cache hits and only log first hit within a time window
    const now = Date.now();
    const lastHitTime = this.cacheHitTracking.get(key) || 0;
    if (now - lastHitTime > 1000) { // 1 second window
      this.cacheHitTracking.set(key, now);
      logger.debug(`Using cached data for ${year}-${month}`);
    }

    return entry.data as PanchangData[];
  }

  /**
   * Check if month data exists in cache
   * @param year The year
   * @param month The month (1-12)
   * @returns True if month data exists in cache and is not expired
   */
  hasMonthData(year: number, month: number): boolean {
    const key = this.getMonthKey(year, month);
    const entry = this.cache.get(key);

    if (!entry) return false;

    // Check if entry has expired
    if (entry.timestamp < Date.now()) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Clear specific month data from cache
   * @param year The year
   * @param month The month (1-12)
   */
  clearMonthData(year: number, month: number): void {
    const key = this.getMonthKey(year, month);
    this.cache.delete(key);
  }

  /**
   * Store reminder dates in cache
   * @param year The year
   * @param month The month (1-12)
   * @param dates Set of day numbers that have reminders
   * @param ttl Time to live in milliseconds (optional)
   */
  setReminderDates(year: number, month: number, dates: Set<number>, ttl?: number): void {
    const key = this.getReminderKey(year, month);
    this.cache.set(key, {
      data: dates,
      timestamp: Date.now() + (ttl || this.DEFAULT_TTL)
    });
  }

  /**
   * Get reminder dates from cache
   * @param year The year
   * @param month The month (1-12)
   * @returns Set of day numbers or null if not in cache or expired
   */
  getReminderDates(year: number, month: number): Set<number> | null {
    const key = this.getReminderKey(year, month);
    const entry = this.cache.get(key);

    if (!entry) return null;

    // Check if entry has expired
    if (entry.timestamp < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    // Track cache hits
    const now = Date.now();
    const lastHitTime = this.cacheHitTracking.get(key) || 0;
    if (now - lastHitTime > 1000) { // 1 second window
      this.cacheHitTracking.set(key, now);
      logger.debug(`Using cached reminder dates for ${year}-${month}`);
    }

    return entry.data as Set<number>;
  }

  /**
   * Check if reminder dates exist in cache
   * @param year The year
   * @param month The month (1-12)
   * @returns True if reminder dates exist in cache and are not expired
   */
  hasReminderDates(year: number, month: number): boolean {
    const key = this.getReminderKey(year, month);
    const entry = this.cache.get(key);

    if (!entry) return false;

    // Check if entry has expired
    if (entry.timestamp < Date.now()) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Clear specific reminder dates from cache
   * @param year The year
   * @param month The month (1-12)
   */
  clearReminderDates(year: number, month: number): void {
    const key = this.getReminderKey(year, month);
    this.cache.delete(key);
  }

  /**
   * Clear all reminder cache entries
   */
  clearAllReminderCache(): void {
    const keysToDelete: string[] = [];

    // Find all reminder cache keys
    for (const key of this.cache.keys()) {
      if (key.startsWith('reminder:')) {
        keysToDelete.push(key);
      }
    }

    // Delete all reminder cache entries
    keysToDelete.forEach(key => this.cache.delete(key));

    logger.debug('Cleared all reminder cache entries', {
      clearedCount: keysToDelete.length
    });
  }

  /**
   * Clear all cache
   */
  clearAll(): void {
    this.cache.clear();
  }

  /**
   * Get the cache key for a month
   * @param year The year
   * @param month The month (1-12)
   * @returns The cache key
   */
  private getMonthKey(year: number, month: number): string {
    return `month:${year}-${month < 10 ? '0' + month : month}`;
  }

  /**
   * Get the cache key for reminder dates
   * @param year The year
   * @param month The month (1-12)
   * @returns The cache key
   */
  private getReminderKey(year: number, month: number): string {
    return `reminder:${year}-${month < 10 ? '0' + month : month}`;
  }
}

// Export a singleton instance
export const cacheService = new CalendarCache();
