import * as SQLite from 'expo-sqlite';
import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import { PanchangData } from '@/types/database';
import { logger } from '@/utils/logging-sentry'; // Import the logger

// Database file name
const DB_NAME = 'odia_calendar_data.db';

// Get the database directory path
const getDBDirectoryPath = () => {
  return `${FileSystem.documentDirectory}SQLite`;
};

// Get the full database file path
const getDBFilePath = () => {
  return `${getDBDirectoryPath()}/${DB_NAME}`;
};

// Define the latest schema version expected by the app code
const LATEST_APP_SCHEMA_VERSION = 1;

/**
 * Database service for the Odia Calendar app
 * Handles database initialization and provides methods for accessing calendar data
 */
class CalendarDatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;
  private initPromise: Promise<void> | null = null;

  /** Public getter to check initialization status */
  get isDbInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Initialize the database
   * This will copy the database from assets if it doesn't exist
   */
  async initialize(): Promise<void> {
    // If already initialized, return
    if (this.isInitialized) {
      logger.debug('Database already initialized, skipping initialization');
      return;
    }

    // If initialization is in progress, wait for it to complete
    if (this.initPromise) {
      logger.debug('Database initialization already in progress, waiting for completion');
      return this.initPromise;
    }

    logger.info('Starting database initialization process');

    // Start initialization
    this.initPromise = this._initializeDatabase();

    try {
      logger.debug('Waiting for database initialization to complete');
      await this.initPromise;
      this.isInitialized = true;
      logger.info('Database initialization completed successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : 'No stack trace';

      logger.error('Failed to initialize database', {
        error: errorMessage,
        stack: errorStack
      });

      this.initPromise = null;
      throw error;
    }
  }

  /**
   * Internal method to initialize the database
   */
  private async _initializeDatabase(): Promise<void> {
    try {
      const dbDirectoryPath = getDBDirectoryPath();
      const dbFilePath = getDBFilePath();

      logger.debug('Database directory path', { path: dbDirectoryPath });
      logger.debug('Database file path', { path: dbFilePath });

      // Create the SQLite directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(dbDirectoryPath);
      if (!dirInfo.exists) {
        logger.debug('Creating SQLite directory...');
        await FileSystem.makeDirectoryAsync(dbDirectoryPath, { intermediates: true });
      }

      // Check if the database file already exists
      const fileInfo = await FileSystem.getInfoAsync(dbFilePath);

      if (!fileInfo.exists) {
        logger.info('Database file does not exist, copying from assets...');

        // Load the database asset
        logger.debug('Loading database asset...');
        const asset = Asset.fromModule(require('../../assets/db/odia_calendar_data.db'));
        await asset.downloadAsync();

        if (!asset.localUri) {
          throw new Error('Failed to download database asset: localUri is null');
        }

        logger.debug('Asset downloaded successfully', { uri: asset.localUri });

        // Copy the database file to the SQLite directory
        await FileSystem.copyAsync({
          from: asset.localUri,
          to: dbFilePath
        });

        logger.info('Database copied successfully', { path: dbFilePath });
      } else {
        logger.debug('Database file already exists', { path: dbFilePath, size: fileInfo.size });
      }

      // Open the database
      logger.debug('Opening database...');
      try {
        // First try with openDatabaseAsync
        try {
          logger.debug('Trying to open database with openDatabaseAsync...');
          this.db = await SQLite.openDatabaseAsync(DB_NAME);

          if (!this.db) {
            throw new Error('Failed to open database: db is null');
          }

          logger.debug('Database opened successfully with openDatabaseAsync');
        } catch (openError) {
          logger.error('Error opening database with openDatabaseAsync', { error: openError });

          // Try with a different approach - create a new database
          logger.warn('Trying alternative approach after open failure...');

          // Delete the existing database file if it exists
          await FileSystem.deleteAsync(dbFilePath, { idempotent: true });

          // Copy the database file again
          logger.debug('Recopying database file...');
          const asset = Asset.fromModule(require('../../assets/db/odia_calendar_data.db'));
          await asset.downloadAsync();

          if (!asset.localUri) {
            throw new Error('Failed to download database asset: localUri is null');
          }

          await FileSystem.copyAsync({
            from: asset.localUri,
            to: dbFilePath
          });

          logger.debug('Database recopied successfully, trying to open again...');
          this.db = await SQLite.openDatabaseAsync(DB_NAME);

          if (!this.db) {
            throw new Error('Failed to open database after recopy: db is null');
          }

          logger.debug('Database opened successfully after recopy');
        }

        // Enable WAL mode for better performance
        await this.db.execAsync('PRAGMA journal_mode = WAL;');
        logger.debug('WAL mode enabled');

        // Ensure calendar_year_versions table exists
        await this.db.execAsync(`
          CREATE TABLE IF NOT EXISTS calendar_year_versions (
            year TEXT PRIMARY KEY NOT NULL,
            version INTEGER NOT NULL DEFAULT 1,
            last_updated TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
          );
        `);
        logger.debug('Ensured calendar_year_versions table exists');

        // --- Schema Migration Logic ---
        await this.runMigrations();

        // Verify the database has the required table
        logger.debug('Verifying database tables using getAllAsync...');
        // Use getAllAsync for SELECT queries as it returns rows directly
        const tables = await this.db.getAllAsync<{ name: string }>(`SELECT name FROM sqlite_master WHERE type='table'`);

        logger.debug('Raw result from sqlite_master query (getAllAsync)', { tables: JSON.stringify(tables, null, 2) });

        // Check if the result is a valid array
        if (!Array.isArray(tables)) {
          logger.error('Invalid structure received from sqlite_master query (getAllAsync)', { expected: 'array', got: JSON.stringify(tables, null, 2) });
          throw new Error('Failed to query database tables: result structure is invalid (expected array)');
        }

        logger.debug('Database tables', { tables });

        // Check if panchang_data table exists
        const hasPanchangData = tables.some((table: any) => table.name === 'panchang_data');

        if (!hasPanchangData) {
          // Also log tables if the specific table is missing
          logger.error('panchang_data table not found', { availableTables: tables });
          throw new Error('panchang_data table not found in the database');
        }

        // Verify we can query the table
        const testQuery = await this.db.getAllAsync('SELECT COUNT(*) as count FROM panchang_data');
        logger.debug('Test query result', { result: testQuery });

        // Removed the block that logs the table schema for panchang_data

        logger.info('Database initialized successfully');
      } catch (error) {
        logger.error('Error opening or verifying database', { error });
        throw error;
      }
    } catch (error) {
      logger.error('Error initializing database', { error });
      throw error;
    }
  }

  /**
   * Runs necessary database schema migrations.
   */
  private async runMigrations(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not available for migrations.');
    }
    logger.info('[DB Migration] Checking database schema version...');
    try {
      // Get the current version stored in the database
      const result = await this.db.getFirstAsync<{ user_version: number }>('PRAGMA user_version;');
      let currentDbVersion = result?.user_version ?? 0;
      logger.info(`[DB Migration] Current DB version: ${currentDbVersion}`);

      // If the database is unversioned (version 0), set it to the initial version (1)
      // This happens on the very first initialization after copying the bundled DB.
      if (currentDbVersion === 0) {
        logger.info('[DB Migration] Database unversioned, setting initial version to 1.');
        await this.db.execAsync('PRAGMA user_version = 1;');
        currentDbVersion = 1;
      }

      if (currentDbVersion < LATEST_APP_SCHEMA_VERSION) {
        logger.info(`[DB Migration] Migrating database from version ${currentDbVersion} to ${LATEST_APP_SCHEMA_VERSION}...`);

        for (let targetVersion = currentDbVersion + 1; targetVersion <= LATEST_APP_SCHEMA_VERSION; targetVersion++) {
          logger.info(`[DB Migration] Applying migration for version ${targetVersion}...`);
          await this.db.execAsync('BEGIN TRANSACTION;');
          try {
            // --- Get and Execute Migration SQL ---
            // In a real scenario, you would load SQL from a file or embedded string
            // based on `targetVersion`. Example for version 2:
            // if (targetVersion === 2) {
            //   const migrationSQL = `ALTER TABLE panchang_data ADD COLUMN user_notes TEXT;`;
            //   await this.db.execAsync(migrationSQL);
            // }
            // Add more else if blocks for subsequent versions

            // --- Update DB Version ---
            // This MUST be inside the transaction, after executing the migration SQL
            await this.db.execAsync(`PRAGMA user_version = ${targetVersion};`);

            await this.db.execAsync('COMMIT;');
            logger.info(`[DB Migration] Successfully migrated to version ${targetVersion}.`);
          } catch (migrationError) {
            await this.db.execAsync('ROLLBACK;');
            logger.error(`[DB Migration] Failed to migrate to version ${targetVersion}. Rolled back transaction.`, { error: migrationError });
            throw new Error(`Database migration to version ${targetVersion} failed.`); // Re-throw to stop initialization
          }
        }
      } else if (currentDbVersion > LATEST_APP_SCHEMA_VERSION) {
        // This scenario should ideally not happen if bundled DB is kept up-to-date
        logger.warn(`[DB Migration] Database version (${currentDbVersion}) is newer than app schema version (${LATEST_APP_SCHEMA_VERSION}). This might indicate an issue.`);
        // Decide how to handle this - maybe log an error, inform the user, or attempt downgrade (complex).
        // For now, we'll just warn.
      } else {
        logger.info('[DB Migration] Database schema is up-to-date.');
      }
    } catch (error) {
      logger.error('[DB Migration] Error during migration check/execution', { error });
      throw error; // Re-throw error to potentially halt app initialization
    }
  }


  /**
   * Get calendar data for a specific month
   * @param year The year
   * @param month The month (1-12)
   */
  async getMonthData(year: number, month: number): Promise<PanchangData[]> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const monthStr = month < 10 ? `0${month}` : `${month}`;
    const monthPrefix = `${year}-${monthStr}%`;

    try {
      const data = await this.db.getAllAsync<PanchangData>(
        'SELECT * FROM panchang_data WHERE eng_date LIKE ? ORDER BY eng_date',
        [monthPrefix]
      );
      return data;
    } catch (error) {
      logger.error(`Error fetching month data`, { year, month, error });
      throw error;
    }
  }

  /**
   * Get data for a specific date
   * @param dateString The date string in YYYY-MM-DD format
   */
  async getDateData(dateString: string): Promise<PanchangData | null> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      const data = await this.db.getFirstAsync<PanchangData>(
        'SELECT * FROM panchang_data WHERE eng_date = ?',
        [dateString]
      );
      return data;
    } catch (error) {
      logger.error(`Error fetching date data`, { dateString, error });
      throw error;
    }
  }

  /**
   * Get all festival dates for a month
   * @param year The year
   * @param month The month (1-12)
   */
  async getFestivalsForMonth(year: number, month: number): Promise<PanchangData[]> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const monthStr = month < 10 ? `0${month}` : `${month}`;
    const monthPrefix = `${year}-${monthStr}%`;

    try {
      const data = await this.db.getAllAsync<PanchangData>(
        'SELECT * FROM panchang_data WHERE eng_date LIKE ? AND festivals IS NOT NULL AND festivals != \'\' ORDER BY eng_date',
        [monthPrefix]
      );
      return data;
    } catch (error) {
      logger.error(`Error fetching festivals for month`, { year, month, error });
      throw error;
    }
  }

  /**
   * Get all marriage dates for a month
   * @param year The year
   * @param month The month (1-12)
   */
  async getMarriageDatesForMonth(year: number, month: number): Promise<PanchangData[]> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const monthStr = month < 10 ? `0${month}` : `${month}`;
    const monthPrefix = `${year}-${monthStr}%`;

    try {
      const data = await this.db.getAllAsync<PanchangData>(
        'SELECT * FROM panchang_data WHERE eng_date LIKE ? AND is_marriage_date = 1 ORDER BY eng_date',
        [monthPrefix]
      );
      return data;
    } catch (error) {
      logger.error(`Error fetching marriage dates for month`, { year, month, error });
      throw error;
    }
  }

  /**
   * Get all brata ghara dates for a month
   * @param year The year
   * @param month The month (1-12)
   */
  async getBrataGharaDatesForMonth(year: number, month: number): Promise<PanchangData[]> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const monthStr = month < 10 ? `0${month}` : `${month}`;
    const monthPrefix = `${year}-${monthStr}%`;

    try {
      const data = await this.db.getAllAsync<PanchangData>(
        'SELECT * FROM panchang_data WHERE eng_date LIKE ? AND is_brata_ghara_date = 1 ORDER BY eng_date',
        [monthPrefix]
      );
      return data;
    } catch (error) {
      logger.error(`Error fetching brata ghara dates for month`, { year, month, error });
      throw error;
    }
  }

  // --- Versioning Methods ---

  /**
   * Robustly initializes the calendar_year_versions table.
   * Ensures that every year present in panchang_data has a corresponding
   * entry in calendar_year_versions, adding a default version 1 if missing.
   * Does not overwrite existing version entries.
   */
  async initializeYearVersions(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized for version initialization');
    }
    logger.info('[DB Service] Starting robust year version initialization...');
    try {
      // 1. Check if panchang_data has any records
      const panchangCountResult = await this.db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM panchang_data'
      );
      const panchangCount = panchangCountResult?.count ?? 0;

      if (panchangCount === 0) {
        logger.info('[DB Service] No panchang data found, skipping version initialization.');
        return;
      }
      logger.info(`[DB Service] Found ${panchangCount} records in panchang_data.`);

      // 2. Get distinct years from panchang_data
      const panchangYearsResult = await this.db.getAllAsync<{ year: string }>(
        `SELECT DISTINCT strftime('%Y', eng_date) as year FROM panchang_data WHERE year IS NOT NULL ORDER BY year`
      );
      const panchangYears = new Set(panchangYearsResult.map(r => r.year));
      if (panchangYears.size === 0) {
         logger.info('[DB Service] No distinct years found in panchang_data.');
         return;
      }
      logger.info(`[DB Service] Distinct years in panchang_data: ${[...panchangYears].join(', ')}`);

      // 3. Get years already present in calendar_year_versions
      const versionedYearsResult = await this.db.getAllAsync<{ year: string }>(
        'SELECT year FROM calendar_year_versions'
      );
      const versionedYears = new Set(versionedYearsResult.map(r => r.year));
      logger.info(`[DB Service] Years already in versions table: ${[...versionedYears].join(', ')}`);

      // 4. Identify missing years
      const missingYears: string[] = [];
      for (const year of panchangYears) {
        if (!versionedYears.has(year)) {
          missingYears.push(year);
        }
      }

      // 5. Add missing years with default version 1
      if (missingYears.length > 0) {
        logger.info(`[DB Service] Years missing from versions table: ${missingYears.join(', ')}. Adding with default version 1...`);
        await this.db.execAsync('BEGIN TRANSACTION');
        try {
          const now = new Date().toISOString();
          // Prepare the statement outside the loop for efficiency if possible,
          // but execAsync requires the full SQL string each time.
          for (const year of missingYears) {
            // Ensure values are properly escaped/quoted for SQL
            const escapedYear = year.replace(/'/g, "''");
            const escapedNow = now.replace(/'/g, "''");
            const sql = `INSERT OR IGNORE INTO calendar_year_versions (year, version, last_updated) VALUES ('${escapedYear}', 1, '${escapedNow}');`;
            await this.db.execAsync(sql);
          }
          await this.db.execAsync('COMMIT');
          logger.info(`[DB Service] Successfully added default versions for missing years.`);
        } catch (error) {
          await this.db.execAsync('ROLLBACK');
          logger.error('[DB Service] Error adding default versions (transaction rolled back)', { error });
          // Decide whether to re-throw or just log
          // throw error;
        }
      } else {
        logger.info('[DB Service] All years from panchang_data are already present in versions table.');
      }
      logger.info('[DB Service] Year version initialization check complete.');

    } catch (error) {
      logger.error('[DB Service] Error during robust version initialization', { error });
      // Don't throw, allow app to continue if possible
    }
  }

  /**
   * Get the version for a specific year
   * @param year The year string
   * @returns The version number or null if not found
   */
  async getYearVersion(year: string): Promise<number | null> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    try {
      const result = await this.db.getFirstAsync<{ version: number }>(
        'SELECT version FROM calendar_year_versions WHERE year = ?',
        [year]
      );
      // Ensure we return null if version is 0 or not found
      return result?.version && result.version > 0 ? result.version : null;
    } catch (error) {
      logger.error(`Error fetching version for year`, { year, error });
      return null; // Return null on error
    }
  }


  /**
   * Set or update the version for a specific year
   * @param year The year string
   * @param version The version number
   */
  async setYearVersion(year: string, version: number): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    try {
      // Ensure values are properly escaped/quoted for SQL
      const escapedYear = year.replace(/'/g, "''");
      const escapedTimestamp = new Date().toISOString().replace(/'/g, "''");
      // Version is a number, no quotes needed
      const sql = `INSERT OR REPLACE INTO calendar_year_versions (year, version, last_updated) VALUES ('${escapedYear}', ${version}, '${escapedTimestamp}');`;
      await this.db.execAsync(sql);
      logger.debug(`Set version for year ${year} to ${version}`);
    } catch (error) {
      logger.error(`Error setting version for year`, { year, version, error });
      throw error; // Re-throw error to be handled by caller
    }
  }

   /**
   * Get all available years from the calendar_year_versions table
   * @returns An array of year strings sorted in descending order (newest first)
   */
  async getAvailableYearsFromVersions(): Promise<string[]> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    try {
      const results = await this.db.getAllAsync<{ year: string }>(
        'SELECT year FROM calendar_year_versions ORDER BY year DESC'
      );
      return results.map(r => r.year);
    } catch (error) {
      logger.error('Error fetching available years from versions table', { error });
      return []; // Return empty array on error
    }
  }

  /**
   * Insert or replace multiple PanchangData records in a transaction
   * @param data Array of PanchangData objects
   * @returns The number of records successfully inserted/replaced
   */
  async insertOrReplacePanchangData(data: PanchangData[]): Promise<number> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    if (data.length === 0) {
      logger.warn('No data provided to insertOrReplacePanchangData.');
      return 0;
    }

    logger.info(`Starting transaction to insert/replace ${data.length} panchang records.`);
    await this.db.execAsync('BEGIN TRANSACTION');
    try {
      // Helper function to quote strings and handle nulls for SQL interpolation
      const escapeSqlValue = (value: any): string => {
        if (value === null || typeof value === 'undefined') return 'NULL';
        if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`; // Escape single quotes
        if (typeof value === 'number') return value.toString();
        // Booleans are already converted to 0 or 1 (isHoliday etc.)
        // Fallback for other types (might need adjustment)
        return `'${String(value).replace(/'/g, "''")}'`;
      };

      let insertedCount = 0;
      for (const item of data) {
        // Ensure boolean/numeric fields are handled correctly (0 or 1)
        const isHoliday = typeof item.is_holiday === 'boolean' ? (item.is_holiday ? 1 : 0) : (item.is_holiday ?? 0);
        const isMarriageDate = typeof item.is_marriage_date === 'boolean' ? (item.is_marriage_date ? 1 : 0) : (item.is_marriage_date ?? 0);
        const isBrataGharaDate = typeof item.is_brata_ghara_date === 'boolean' ? (item.is_brata_ghara_date ? 1 : 0) : (item.is_brata_ghara_date ?? 0);

        // *** Add detailed logging before execution ***
        // logger.debug(`[DB Service] Preparing to insert/replace eng_date: '${item.eng_date}' (Type: ${typeof item.eng_date})`); // Keep this commented out unless debugging specific insert issues

        // Interpolate values directly into the SQL string
        const interpolatedSql = `
          INSERT OR REPLACE INTO panchang_data (
            eng_date, odia_date, odia_month, odia_year, day_name, paksha,
            tithi_name, tithi_end_time, nakshatra_name, nakshatra_end_time,
            yoga_name, karana_name, chandra_rasi, sunrise, sunset, festivals,
            is_holiday, is_marriage_date, is_brata_ghara_date, additional
          ) VALUES (
            ${escapeSqlValue(item.eng_date)},
            ${escapeSqlValue(item.odia_date)},
            ${escapeSqlValue(item.odia_month)},
            ${escapeSqlValue(item.odia_year)},
            ${escapeSqlValue(item.day_name)},
            ${escapeSqlValue(item.paksha)},
            ${escapeSqlValue(item.tithi_name)},
            ${escapeSqlValue(item.tithi_end_time)},
            ${escapeSqlValue(item.nakshatra_name)},
            ${escapeSqlValue(item.nakshatra_end_time)},
            ${escapeSqlValue(item.yoga_name)},
            ${escapeSqlValue(item.karana_name)},
            ${escapeSqlValue(item.chandra_rasi)},
            ${escapeSqlValue(item.sunrise)},
            ${escapeSqlValue(item.sunset)},
            ${escapeSqlValue(item.festivals)},
            ${isHoliday},
            ${isMarriageDate},
            ${isBrataGharaDate},
            ${escapeSqlValue(item.additional)}
          );
        `;
        // execAsync takes only the SQL string
        await this.db.execAsync(interpolatedSql);
        insertedCount++;
      }

      await this.db.execAsync('COMMIT');
      logger.info(`Successfully inserted/replaced ${insertedCount} panchang records.`);
      return insertedCount;
    } catch (error) {
      await this.db.execAsync('ROLLBACK');
      logger.error('Error inserting/replacing panchang data (transaction rolled back)', { error });
      throw error; // Re-throw error
    }
  }

  /**
   * Verify that data for a specific year exists in the panchang_data table
   * @param year The year string to verify
   * @returns True if data exists, false otherwise
   */
  async verifyYearDataExists(year: string): Promise<boolean> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      const yearPrefix = `${year}-%`;
      const result = await this.db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM panchang_data WHERE eng_date LIKE ?',
        [yearPrefix]
      );

      const count = result?.count ?? 0;
      logger.debug(`Verified ${count} records exist for year ${year}`);
      return count > 0;
    } catch (error) {
      logger.error(`Error verifying data for year ${year}`, { error });
      return false;
    }
  }


  /**
   * Delete all data for a specific year
   * @param year The year string to delete
   * @returns The number of records deleted and success status
   */
  async deleteYearData(year: string): Promise<{ deletedRecords: number; success: boolean; errorCode?: string }> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    logger.info(`Attempting to delete data for year ${year}...`);

    // Check if this is the current year - prevent deletion of current year
    const currentYear = new Date().getFullYear().toString();
    if (year === currentYear) {
      logger.warn(`Cannot delete data for current year ${year}.`);
      return { deletedRecords: 0, success: false, errorCode: 'CURRENT_YEAR' };
    }

    // Start a transaction to ensure atomicity
    await this.db.execAsync('BEGIN TRANSACTION');

    try {
      // First, check if the year exists in the database
      const yearExists = await this.verifyYearDataExists(year);

      if (!yearExists) {
        logger.warn(`No data found for year ${year}, nothing to delete.`);
        await this.db.execAsync('ROLLBACK');
        return { deletedRecords: 0, success: false, errorCode: 'NO_DATA' };
      }

      // Get the count of records to be deleted (for reporting)
      const yearPrefix = `${year}-%`;
      const countResult = await this.db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM panchang_data WHERE eng_date LIKE ?',
        [yearPrefix]
      );

      const recordCount = countResult?.count ?? 0;

      // Delete records from panchang_data table
      const deleteDataSql = `DELETE FROM panchang_data WHERE eng_date LIKE '${yearPrefix}'`;
      await this.db.execAsync(deleteDataSql);

      // Delete the year entry from calendar_year_versions table
      const deleteVersionSql = `DELETE FROM calendar_year_versions WHERE year = '${year}'`;
      await this.db.execAsync(deleteVersionSql);

      // Commit the transaction
      await this.db.execAsync('COMMIT');

      logger.info(`Successfully deleted ${recordCount} records for year ${year}`);
      return { deletedRecords: recordCount, success: true };
    } catch (error) {
      // Rollback the transaction on error
      await this.db.execAsync('ROLLBACK');
      logger.error(`Error deleting data for year ${year}`, { error });
      return { deletedRecords: 0, success: false, errorCode: 'ERROR' };
    }
  }

  // --- End Versioning Methods ---

  /**
   * Close the database connection
   */
  async close(): Promise<void> {
    if (this.db) {
      await this.db.closeAsync();
      this.db = null;
      this.isInitialized = false;
      this.initPromise = null;
    }
  }

  /**
   * Debug method to get database file info
   * This is useful for debugging database issues
   */
  async debugGetDatabaseFileInfo(): Promise<void> {
    try {
      const dbFilePath = getDBFilePath();
      const fileInfo = await FileSystem.getInfoAsync(dbFilePath);

      if (fileInfo.exists) {
        logger.debug('Database file exists', { path: dbFilePath, size: fileInfo.size, uri: fileInfo.uri });
      } else {
        logger.warn('Database file does not exist', { path: dbFilePath });
      }
    } catch (error) {
      logger.error('Error getting database file info', { error });
    }
  }

  /**
   * Debug method to log database information
   */
  async debugLogDatabaseInfo(): Promise<void> {
    try {
      if (!this.db) {
        logger.warn('Database is not initialized for debug logging');
        return;
      }

      // Log database tables
      const tables = await this.db.getAllAsync<{ name: string }>(`SELECT name FROM sqlite_master WHERE type='table'`);
      logger.debug('Database tables', { tables });

      // Log database path
      logger.debug('Database path', { path: getDBFilePath() });

      // Log a sample row from panchang_data
      try {
        // Removed generic type <PanchangData> to test TS error resolution
        const sampleData = await this.db.getAllAsync('SELECT * FROM panchang_data LIMIT 1');
        logger.debug('Sample data', { sampleData });
      } catch (error) {
        logger.error('Error fetching sample data for debug log', { error });
      }
    } catch (error) {
      logger.error('Error logging database info', { error });
    }
  }

  /**
   * Execute a query and return the results
   * @param query The SQL query to execute
   * @param params Optional parameters for the query
   * @returns The query results
   */
  async executeQuery(query: string, params?: any[]): Promise<any[]> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      // Log the query for debugging
      if (params) {
        // Create a copy of params for logging
        const paramsCopy = [...params];

        // Create interpolated query for debugging
        let interpolatedQuery = query;
        interpolatedQuery = interpolatedQuery.replace(/\?/g, () => {
          if (paramsCopy.length === 0) return '?';
          const param = paramsCopy.shift();
          return typeof param === 'string' ? `'${param}'` : param;
        });

        logger.debug('Executing query with params', {
          query,
          params,
          interpolatedQuery
        });
      } else {
        logger.debug('Executing query', { query });
      }

      // For SELECT queries
      if (query.trim().toLowerCase().startsWith('select')) {
        if (params) {
          return await this.db.getAllAsync(query, params);
        } else {
          return await this.db.getAllAsync(query);
        }
      } else {
        // For other queries
        if (params) {
          await this.db.execAsync(query, params);
        } else {
          await this.db.execAsync(query);
        }
        return [];
      }
    } catch (error) {
      logger.error('Error executing query', { error, query, params });
      throw error;
    }
  }

  /**
   * Execute an update query
   * @param query The SQL query to execute
   * @returns Success status
   */
  async executeUpdate(query: string): Promise<boolean> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      await this.db.execAsync(query);
      return true;
    } catch (error) {
      logger.error('Error executing update query', { error, query });
      return false;
    }
  }

  /**
   * Begin a transaction
   * @returns Success status
   */
  async beginTransaction(): Promise<boolean> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      await this.db.execAsync('BEGIN TRANSACTION');
      logger.debug('Transaction started');
      return true;
    } catch (error) {
      logger.error('Error beginning transaction', { error });
      return false;
    }
  }

  /**
   * Commit a transaction
   * @returns Success status
   */
  async commitTransaction(): Promise<boolean> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      await this.db.execAsync('COMMIT');
      logger.debug('Transaction committed');
      return true;
    } catch (error) {
      logger.error('Error committing transaction', { error });
      return false;
    }
  }

  /**
   * Rollback a transaction
   * @returns Success status
   */
  async rollbackTransaction(): Promise<boolean> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      await this.db.execAsync('ROLLBACK');
      logger.debug('Transaction rolled back');
      return true;
    } catch (error) {
      logger.error('Error rolling back transaction', { error });
      return false;
    }
  }
}

// Export a singleton instance
export const databaseService = new CalendarDatabaseService();
