import { Platform } from 'react-native';
import * as TaskManager from 'expo-task-manager';
import { reminderService } from './reminder-service';
import { logger } from '@/utils/logging-sentry';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';

// Constants
const REMINDER_RESCHEDULE_TASK = 'REMINDER_RESCHEDULE_TASK';
const LAST_RESCHEDULE_KEY = 'odia_calendar_last_reschedule';
const RESCHEDULE_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours in milliseconds

// Check if we're running in Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

// Conditionally import BackgroundTask to avoid errors in Expo Go
let BackgroundTask: any = null;
if (!isExpoGo) {
  try {
    // Only import in development builds or standalone apps
    BackgroundTask = require('expo-background-task');
  } catch (error) {
    logger.warn('expo-background-task module could not be imported', { error });
  }
}

// Define the background task
TaskManager.defineTask(REMINDER_RESCHEDULE_TASK, async () => {
  try {
    logger.info('Running reminder reschedule background task');

    // Check if we need to reschedule based on last reschedule time
    const lastRescheduleStr = await AsyncStorage.getItem(LAST_RESCHEDULE_KEY);
    const now = Date.now();

    if (lastRescheduleStr) {
      const lastReschedule = parseInt(lastRescheduleStr, 10);
      if (now - lastReschedule < RESCHEDULE_INTERVAL) {
        logger.debug('Skipping reschedule, last reschedule was too recent', {
          lastReschedule: new Date(lastReschedule).toISOString(),
          now: new Date(now).toISOString(),
          interval: RESCHEDULE_INTERVAL / (60 * 60 * 1000) + ' hours'
        });
        // Return success result (handle both with and without BackgroundTask)
        return BackgroundTask?.BackgroundTaskResult?.Success || true;
      }
    }

    // Initialize reminder service and reschedule notifications
    await reminderService.initialize();

    // Update last reschedule time
    await AsyncStorage.setItem(LAST_RESCHEDULE_KEY, now.toString());

    logger.info('Successfully rescheduled reminders in background task');
    // Return success result (handle both with and without BackgroundTask)
    return BackgroundTask?.BackgroundTaskResult?.Success || true;
  } catch (error) {
    logger.error('Error in reminder reschedule background task', { error });
    // Return failure result (handle both with and without BackgroundTask)
    return BackgroundTask?.BackgroundTaskResult?.Failed || false;
  }
});

class AppStartupService {
  private isInitialized = false;

  /**
   * Initialize the app startup service
   * This should be called when the app starts
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      logger.info('Initializing app startup service');

      // Initialize reminder service
      await reminderService.initialize();

      // Register background task for reminder rescheduling
      await this.registerBackgroundTasks();

      // Record startup time for reschedule tracking
      await AsyncStorage.setItem(LAST_RESCHEDULE_KEY, Date.now().toString());

      this.isInitialized = true;
      return true;
    } catch (error) {
      logger.error('Error initializing app startup service', { error });
      return false;
    }
  }

  /**
   * Register background tasks for reminder rescheduling
   */
  private async registerBackgroundTasks(): Promise<void> {
    // Skip background task registration in Expo Go
    if (isExpoGo) {
      logger.info('Skipping background task registration in Expo Go');
      return;
    }

    // Skip if BackgroundTask module is not available
    if (!BackgroundTask) {
      logger.warn('BackgroundTask module not available, skipping registration');
      return;
    }

    try {
      // Check if the task is already registered
      const isRegistered = await BackgroundTask.isTaskRegisteredAsync(REMINDER_RESCHEDULE_TASK);

      if (isRegistered) {
        logger.debug('Reminder reschedule task already registered');
        return;
      }

      // Check if background tasks are available
      const status = await BackgroundTask.getStatusAsync();
      if (status !== BackgroundTask.BackgroundTaskStatus.Available) {
        logger.warn('Background tasks are not available on this device', {
          status: BackgroundTask.BackgroundTaskStatus[status]
        });
        return;
      }

      // Register the background task
      await BackgroundTask.registerTaskAsync(REMINDER_RESCHEDULE_TASK, {
        minimumInterval: 60, // 1 hour minimum (in minutes)
      });

      logger.info('Registered reminder reschedule background task');
    } catch (error) {
      // On some devices or in Expo Go, this might fail
      logger.warn('Failed to register reminder reschedule background task', {
        error,
        platform: Platform.OS,
        version: Platform.Version
      });
    }
  }

  /**
   * Unregister background tasks
   * This can be called when the user disables reminders
   */
  async unregisterBackgroundTasks(): Promise<void> {
    // Skip in Expo Go or if BackgroundTask is not available
    if (isExpoGo || !BackgroundTask) {
      logger.info('Skipping background task unregistration in Expo Go or when module is unavailable');
      return;
    }

    try {
      const isRegistered = await BackgroundTask.isTaskRegisteredAsync(REMINDER_RESCHEDULE_TASK);

      if (isRegistered) {
        await BackgroundTask.unregisterTaskAsync(REMINDER_RESCHEDULE_TASK);
        logger.info('Unregistered reminder reschedule background task');
      }
    } catch (error) {
      logger.error('Error unregistering reminder reschedule background task', { error });
    }
  }

  /**
   * Trigger background task for testing (only works in development)
   * This is useful for testing the background task without waiting for the system to trigger it
   */
  async triggerBackgroundTaskForTesting(): Promise<boolean> {
    try {
      if (!__DEV__) {
        logger.warn('Cannot trigger background task in production');
        return false;
      }

      // In Expo Go or if BackgroundTask is not available, use direct execution
      if (isExpoGo || !BackgroundTask) {
        logger.info('In Expo Go or BackgroundTask unavailable, using direct task execution');
        try {
          await TaskManager.executeTaskAsync(REMINDER_RESCHEDULE_TASK, { data: {} });
          logger.info('Task executed directly');
          return true;
        } catch (directError) {
          logger.error('Error executing task directly', { error: directError });
          return false;
        }
      }

      // If BackgroundTask is available, use the proper API
      logger.info('Triggering background task for testing');
      const result = await BackgroundTask.triggerTaskWorkerForTestingAsync();
      logger.info('Background task triggered for testing', { result });
      return true;
    } catch (error) {
      logger.error('Error triggering background task for testing', { error });

      // Fallback: directly execute the task if the BackgroundTask API fails
      try {
        logger.info('Falling back to direct task execution');
        await TaskManager.executeTaskAsync(REMINDER_RESCHEDULE_TASK, { data: {} });
        logger.info('Task executed directly');
        return true;
      } catch (fallbackError) {
        logger.error('Error executing task directly', { error: fallbackError });
        return false;
      }
    }
  }
}

export const appStartupService = new AppStartupService();
