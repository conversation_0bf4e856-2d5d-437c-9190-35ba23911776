import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { logger } from '@/utils/logging-sentry';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@/config/secrets';
import { appConfig } from '@/config/appConfig';

// Default configuration that's bundled with the app
const DEFAULT_CONFIG = {
  version: 1,
  supabaseUrl: SUPABASE_URL,
  supabaseAnonKey: SUPABASE_ANON_KEY,
  // Add other configuration items as needed
  lastUpdated: new Date().toISOString(),
};

// Remote configuration endpoint from appConfig
const REMOTE_CONFIG_URL = appConfig.remoteConfig.url;

// Cache keys from appConfig
const CONFIG_CACHE_KEY = appConfig.remoteConfig.cacheKeys.config;
const CONFIG_LAST_FETCHED_KEY = appConfig.remoteConfig.cacheKeys.lastFetched;

// How often to check for new configuration (in milliseconds)
const CONFIG_REFRESH_INTERVAL = appConfig.remoteConfig.refreshInterval;

/**
 * Service to manage remote configuration with fallback to bundled defaults
 */
export class RemoteConfigService {
  private static instance: RemoteConfigService;
  private config: typeof DEFAULT_CONFIG;
  private isLoaded: boolean = false;
  private isLoading: boolean = false;

  private constructor() {
    this.config = { ...DEFAULT_CONFIG };
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): RemoteConfigService {
    if (!RemoteConfigService.instance) {
      RemoteConfigService.instance = new RemoteConfigService();
    }
    return RemoteConfigService.instance;
  }

  /**
   * Initialize the configuration with cache or defaults only (fast)
   * This should be called early in the app lifecycle
   * @returns The current configuration
   */
  public async initializeWithCache(): Promise<typeof DEFAULT_CONFIG> {
    if (this.isLoaded) {
      logger.debug('RemoteConfig: Already initialized, returning current config');
      return this.config;
    }

    if (this.isLoading) {
      logger.debug('RemoteConfig: Already initializing, waiting for completion');
      // If already initializing, wait for it to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      return this.config;
    }

    this.isLoading = true;
    logger.info('RemoteConfig: Fast initializing with cache or defaults...');

    try {
      // Try to load from cache
      const cacheLoaded = await this.loadFromCache();
      if (cacheLoaded) {
        logger.info('RemoteConfig: Loaded from cache', {
          supabaseUrl: this.config.supabaseUrl.substring(0, 15) + '...',
          version: this.config.version,
          lastUpdated: this.config.lastUpdated
        });
      } else {
        logger.info('RemoteConfig: No cache found, using defaults');
      }
    } catch (error) {
      logger.error('RemoteConfig: Failed to load from cache', { error });
      // We'll use the default config in this case
    } finally {
      this.isLoaded = true;
      this.isLoading = false;
      logger.info('RemoteConfig: Fast initialization complete', {
        source: this.config === DEFAULT_CONFIG ? 'default' : 'cache',
        version: this.config.version
      });
    }

    return this.config;
  }

  /**
   * Refresh configuration from remote in the background
   * This should be called after initializeWithCache
   * @returns Promise<RefreshResult> - Object with details about the refresh operation
   */
  public async refreshInBackground(): Promise<{
    updated: boolean;
    fetchFailed: boolean;
    skipped: boolean;
    noUpdateNeeded: boolean;
    message: string;
  }> {
    // Make sure we're initialized first
    if (!this.isLoaded) {
      await this.initializeWithCache();
    }

    logger.info('RemoteConfig: Starting background refresh...');

    try {
      // Check if we need to refresh from remote
      const shouldRefresh = await this.shouldRefreshConfig();

      if (!shouldRefresh) {
        logger.info('RemoteConfig: Background refresh not needed (recently checked)');
        return {
          updated: false,
          fetchFailed: false,
          skipped: true,
          noUpdateNeeded: false,
          message: 'Refresh skipped (recently checked)'
        };
      }

      // Check network connectivity before attempting fetch
      try {
        const networkState = await NetInfo.fetch();
        if (!networkState.isConnected || networkState.isInternetReachable === false) {
          logger.info('RemoteConfig: No internet connection, skipping remote config fetch');
          return {
            updated: false,
            fetchFailed: true,
            skipped: false,
            noUpdateNeeded: false,
            message: 'No internet connection'
          };
        }
      } catch (networkError) {
        logger.warn('RemoteConfig: Failed to check network state', { error: networkError });
        // Continue anyway, the fetch will fail if there's no connection
      }

      logger.info('RemoteConfig: Refreshing from remote...');

      try {
        // Fetch the remote configuration
        const response = await fetch(REMOTE_CONFIG_URL, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
          },
        });

        if (!response.ok) {
          logger.info('RemoteConfig: Remote fetch failed with status ' + response.status);
          return {
            updated: false,
            fetchFailed: true,
            skipped: false,
            noUpdateNeeded: false,
            message: 'Remote fetch failed with status ' + response.status
          };
        }

        logger.debug('RemoteConfig: Remote fetch successful, parsing response');
        const remoteConfig = await response.json();

        // Validate the remote config has the required fields
        if (!remoteConfig.supabaseUrl || !remoteConfig.supabaseAnonKey || !remoteConfig.version) {
          logger.info('RemoteConfig: Remote config is missing required fields');
          return {
            updated: false,
            fetchFailed: true,
            skipped: false,
            noUpdateNeeded: false,
            message: 'Remote config is missing required fields'
          };
        }

        logger.debug('RemoteConfig: Remote config validated successfully');

        // Check if the remote version is newer than our current version
        const currentVersion = this.config.version || 0;
        const remoteVersion = remoteConfig.version || 0;

        if (remoteVersion <= currentVersion) {
          logger.info('RemoteConfig: Remote version is not newer than current version, no update needed', {
            currentVersion,
            remoteVersion
          });

          // Still update the last fetched time to avoid frequent checks
          await AsyncStorage.setItem(CONFIG_LAST_FETCHED_KEY, new Date().toISOString());

          return {
            updated: false,
            fetchFailed: false,
            skipped: false,
            noUpdateNeeded: true,
            message: 'Remote version is not newer, no update needed'
          };
        }

        logger.info('RemoteConfig: Remote version is newer, updating configuration', {
          currentVersion,
          remoteVersion
        });

        // Update our config with the remote values
        this.config = {
          ...this.config,
          ...remoteConfig,
          lastUpdated: new Date().toISOString(),
        };

        // Save to cache
        logger.debug('RemoteConfig: Saving to cache');
        await AsyncStorage.setItem(CONFIG_CACHE_KEY, JSON.stringify(this.config));
        await AsyncStorage.setItem(CONFIG_LAST_FETCHED_KEY, new Date().toISOString());

        logger.info('RemoteConfig: Successfully updated configuration from remote', {
          oldVersion: currentVersion,
          newVersion: remoteVersion
        });

        return {
          updated: true,
          fetchFailed: false,
          skipped: false,
          noUpdateNeeded: false,
          message: 'Configuration updated successfully'
        };
      } catch (error) {
        logger.error('RemoteConfig: Failed to fetch remote config', { error });
        return {
          updated: false,
          fetchFailed: true,
          skipped: false,
          noUpdateNeeded: false,
          message: 'Error fetching remote config: ' + (error instanceof Error ? error.message : String(error))
        };
      }
    } catch (error) {
      logger.error('RemoteConfig: Failed to refresh configuration', { error });
      return {
        updated: false,
        fetchFailed: true,
        skipped: false,
        noUpdateNeeded: false,
        message: 'Error during refresh: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  /**
   * Initialize the configuration (legacy method for backward compatibility)
   * This should be called early in the app lifecycle
   */
  public async initialize(): Promise<void> {
    // First initialize with cache
    await this.initializeWithCache();

    // Then refresh in background
    this.refreshInBackground().catch(error => {
      logger.error('RemoteConfig: Background refresh failed', { error });
    });
  }

  /**
   * Get the current configuration
   */
  public getConfig(): typeof DEFAULT_CONFIG {
    return this.config;
  }

  /**
   * Get the Supabase URL
   */
  public getSupabaseUrl(): string {
    return this.config.supabaseUrl;
  }

  /**
   * Get the Supabase anon key
   */
  public getSupabaseAnonKey(): string {
    return this.config.supabaseAnonKey;
  }

  /**
   * Force a refresh of the configuration from remote
   */
  public async forceRefresh(): Promise<boolean> {
    return await this.fetchFromRemote();
  }

  /**
   * Check if we should refresh the configuration
   */
  private async shouldRefreshConfig(): Promise<boolean> {
    try {
      const lastFetchedStr = await AsyncStorage.getItem(CONFIG_LAST_FETCHED_KEY);

      if (!lastFetchedStr) return true;

      const lastFetched = new Date(lastFetchedStr).getTime();
      const now = new Date().getTime();

      return (now - lastFetched) > CONFIG_REFRESH_INTERVAL;
    } catch (error) {
      logger.error('Error checking if config should refresh', { error });
      return true; // If in doubt, try to refresh
    }
  }

  /**
   * Load configuration from cache
   */
  private async loadFromCache(): Promise<boolean> {
    try {
      logger.debug('RemoteConfig: Attempting to load from cache');
      const cachedConfig = await AsyncStorage.getItem(CONFIG_CACHE_KEY);

      if (cachedConfig) {
        logger.debug('RemoteConfig: Found cached config');
        this.config = { ...this.config, ...JSON.parse(cachedConfig) };
        return true;
      }

      logger.debug('RemoteConfig: No cached config found');
      return false;
    } catch (error) {
      logger.error('RemoteConfig: Failed to load config from cache', { error });
      return false;
    }
  }

  /**
   * Fetch configuration from remote endpoint
   * @param skipNetworkCheck - Whether to skip the network connectivity check (if already checked)
   */
  private async fetchFromRemote(skipNetworkCheck: boolean = false): Promise<boolean> {
    try {
      // Check if we have internet connectivity (unless told to skip)
      if (!skipNetworkCheck) {
        logger.debug('RemoteConfig: Checking network connectivity');
        const networkState = await NetInfo.fetch();
        if (!networkState.isConnected || networkState.isInternetReachable === false) {
          logger.info('RemoteConfig: No internet connection, skipping remote config fetch');
          return false;
        }
      }

      // Fetch the remote configuration
      logger.debug(`RemoteConfig: Fetching from ${REMOTE_CONFIG_URL}`);
      const response = await fetch(REMOTE_CONFIG_URL, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch remote config: ${response.status}`);
      }

      logger.debug('RemoteConfig: Remote fetch successful, parsing response');
      const remoteConfig = await response.json();

      // Validate the remote config has the required fields
      if (!remoteConfig.supabaseUrl || !remoteConfig.supabaseAnonKey || !remoteConfig.version) {
        throw new Error('Remote config is missing required fields');
      }

      logger.debug('RemoteConfig: Remote config validated successfully');

      // Check if the remote version is newer than our current version
      const currentVersion = this.config.version || 0;
      const remoteVersion = remoteConfig.version || 0;

      if (remoteVersion <= currentVersion) {
        logger.info('RemoteConfig: Remote version is not newer than current version, skipping update', {
          currentVersion,
          remoteVersion
        });

        // Still update the last fetched time to avoid frequent checks
        await AsyncStorage.setItem(CONFIG_LAST_FETCHED_KEY, new Date().toISOString());
        // Return true to indicate successful fetch but no update needed
        return true;
      }

      logger.info('RemoteConfig: Remote version is newer, updating configuration', {
        currentVersion,
        remoteVersion
      });

      // Update our config with the remote values
      this.config = {
        ...this.config,
        ...remoteConfig,
        lastUpdated: new Date().toISOString(),
      };

      // Save to cache
      logger.debug('RemoteConfig: Saving to cache');
      await AsyncStorage.setItem(CONFIG_CACHE_KEY, JSON.stringify(this.config));
      await AsyncStorage.setItem(CONFIG_LAST_FETCHED_KEY, new Date().toISOString());

      logger.info('RemoteConfig: Successfully updated configuration from remote', {
        oldVersion: currentVersion,
        newVersion: remoteVersion
      });
      return true;
    } catch (error) {
      logger.error('RemoteConfig: Failed to fetch remote config', { error });
      return false;
    }
  }
}

// Export a singleton instance
export const remoteConfig = RemoteConfigService.getInstance();
