import AsyncStorage from '@react-native-async-storage/async-storage';
import { appConfig } from '@/config/appConfig';
import { logger } from '@/utils/logging-sentry';

// Storage keys
const LAST_AD_SHOWN_KEY = 'odia_calendar_last_interstitial_shown';
const SESSION_START_KEY = 'odia_calendar_ad_session_start';
const ADS_SHOWN_COUNT_KEY = 'odia_calendar_ads_shown_count';
const PLACEMENT_COUNTERS_KEY = 'odia_calendar_placement_counters';
const APP_INSTALL_TIME_KEY = 'odia_calendar_app_install_time';
const SESSION_COUNT_KEY = 'odia_calendar_session_count';

// Types
export type PlacementType = 'dateDetails' | 'reminderCreation' | 'yearChange' | 'appSession';
type PlacementCounters = Record<PlacementType, number>;

class AdFrequencyService {
  private lastAdShownTime: number = 0;
  private sessionStartTime: number = 0;
  private adsShownInSession: number = 0;
  private placementCounters: PlacementCounters = {
    dateDetails: 0,
    reminderCreation: 0,
    yearChange: 0,
    appSession: 0
  };
  private appInstallTime: number = 0;
  private sessionCount: number = 0;
  private isInitialized: boolean = false;

  /**
   * Initialize the ad frequency service
   * Loads stored values from AsyncStorage
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize app install time (for new user detection)
      const installTimeStr = await AsyncStorage.getItem(APP_INSTALL_TIME_KEY);
      const now = Date.now();

      if (!installTimeStr) {
        // First time app is opened, record install time
        this.appInstallTime = now;
        await AsyncStorage.setItem(APP_INSTALL_TIME_KEY, now.toString());
        this.sessionCount = 1;
        await AsyncStorage.setItem(SESSION_COUNT_KEY, '1');
        logger.info('First app launch detected, recorded install time');
      } else {
        this.appInstallTime = parseInt(installTimeStr, 10);

        // Load session count
        const sessionCountStr = await AsyncStorage.getItem(SESSION_COUNT_KEY);
        this.sessionCount = sessionCountStr ? parseInt(sessionCountStr, 10) : 1;
      }

      // Load last ad shown time
      const lastAdShownStr = await AsyncStorage.getItem(LAST_AD_SHOWN_KEY);
      if (lastAdShownStr) {
        this.lastAdShownTime = parseInt(lastAdShownStr, 10);
      }

      // Check if we need to start a new session
      const sessionStartStr = await AsyncStorage.getItem(SESSION_START_KEY);

      if (sessionStartStr) {
        const sessionStart = parseInt(sessionStartStr, 10);
        // If session timeout has passed, start a new session
        if (now - sessionStart > appConfig.ads.interstitialFrequency.sessionTimeout) {
          this.startNewSession();
        } else {
          this.sessionStartTime = sessionStart;
          // Load ads shown count
          const adsShownStr = await AsyncStorage.getItem(ADS_SHOWN_COUNT_KEY);
          this.adsShownInSession = adsShownStr ? parseInt(adsShownStr, 10) : 0;

          // Load placement counters
          const countersStr = await AsyncStorage.getItem(PLACEMENT_COUNTERS_KEY);
          if (countersStr) {
            this.placementCounters = JSON.parse(countersStr);
          }
        }
      } else {
        // No session exists, start a new one
        this.startNewSession();
      }

      this.isInitialized = true;
      logger.debug('Ad frequency service initialized', {
        lastAdShown: new Date(this.lastAdShownTime).toISOString(),
        sessionStart: new Date(this.sessionStartTime).toISOString(),
        adsShownCount: this.adsShownInSession,
        placementCounters: this.placementCounters
      });
    } catch (error) {
      logger.error('Error initializing ad frequency service', { error });
      // Fallback to default values
      this.startNewSession();
      this.isInitialized = true;
    }
  }

  /**
   * Start a new ad session
   */
  private async startNewSession(): Promise<void> {
    const now = Date.now();
    this.sessionStartTime = now;
    this.adsShownInSession = 0;
    this.placementCounters = {
      dateDetails: 0,
      reminderCreation: 0,
      yearChange: 0,
      appSession: 0
    };

    // Increment session count for new user tracking
    this.sessionCount += 1;

    try {
      await AsyncStorage.setItem(SESSION_START_KEY, now.toString());
      await AsyncStorage.setItem(ADS_SHOWN_COUNT_KEY, '0');
      await AsyncStorage.setItem(PLACEMENT_COUNTERS_KEY, JSON.stringify(this.placementCounters));
      await AsyncStorage.setItem(SESSION_COUNT_KEY, this.sessionCount.toString());
      logger.debug('Started new ad session', { sessionCount: this.sessionCount });
    } catch (error) {
      logger.error('Error saving ad session data', { error });
    }
  }

  /**
   * Increment the counter for a specific placement
   */
  async incrementPlacementCounter(placement: PlacementType): Promise<number> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    this.placementCounters[placement] += 1;

    try {
      await AsyncStorage.setItem(PLACEMENT_COUNTERS_KEY, JSON.stringify(this.placementCounters));
    } catch (error) {
      logger.error('Error saving placement counters', { error });
    }

    return this.placementCounters[placement];
  }

  /**
   * Reset the counter for a specific placement
   */
  async resetPlacementCounter(placement: PlacementType): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    this.placementCounters[placement] = 0;

    try {
      await AsyncStorage.setItem(PLACEMENT_COUNTERS_KEY, JSON.stringify(this.placementCounters));
    } catch (error) {
      logger.error('Error saving placement counters', { error });
    }
  }

  /**
   * Check if the user is considered a "new user" based on configuration
   */
  private isNewUser(): boolean {
    if (!appConfig.ads.interstitialFrequency.newUserProtection) {
      return false; // New user protection is disabled
    }

    const threshold = appConfig.ads.interstitialFrequency.newUserSessionThreshold || 3;
    return this.sessionCount < threshold;
  }

  /**
   * Check if an interstitial ad can be shown based on frequency rules
   */
  async canShowAd(placement: PlacementType): Promise<boolean> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const now = Date.now();

    // Check if placement is enabled
    if (!appConfig.ads.interstitialPlacement[placement].enabled) {
      logger.debug(`Ad blocked: Placement ${placement} is disabled`);
      return false;
    }

    // Check new user protection
    if (this.isNewUser()) {
      const newUserDelay = appConfig.ads.interstitialFrequency.newUserInitialDelay || 300000; // 5 minutes default
      if (now - this.sessionStartTime < newUserDelay) {
        logger.debug(`Ad blocked: New user protection active (session ${this.sessionCount})`);
        return false;
      }
    } else {
      // Check regular initial delay for existing users
      if (now - this.sessionStartTime < appConfig.ads.interstitialFrequency.initialDelay) {
        logger.debug('Ad blocked: Still in initial delay period');
        return false;
      }
    }

    // Check if minimum time between ads has passed
    if (now - this.lastAdShownTime < appConfig.ads.interstitialFrequency.minTimeBetweenAds) {
      logger.debug('Ad blocked: Minimum time between ads not elapsed');
      return false;
    }

    // Check if we've reached the maximum ads per session
    if (this.adsShownInSession >= appConfig.ads.interstitialFrequency.maxAdsPerSession) {
      logger.debug('Ad blocked: Maximum ads per session reached');
      return false;
    }

    // Check placement-specific frequency
    if (placement === 'dateDetails') {
      const frequency = appConfig.ads.interstitialPlacement.dateDetails.frequency || 3;
      if (this.placementCounters.dateDetails < frequency) {
        logger.debug(`Ad blocked: Date details counter (${this.placementCounters.dateDetails}) below threshold (${frequency})`);
        return false;
      }
    }

    // Apply probability factor
    const probability = appConfig.ads.interstitialPlacement[placement].probability || 1.0;
    if (Math.random() > probability) {
      logger.debug(`Ad blocked: Random probability check failed for ${placement}`);
      return false;
    }

    return true;
  }

  /**
   * Record that an ad was shown
   */
  async recordAdShown(placement: PlacementType): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const now = Date.now();
    this.lastAdShownTime = now;
    this.adsShownInSession += 1;

    // Reset the placement counter
    await this.resetPlacementCounter(placement);

    try {
      await AsyncStorage.setItem(LAST_AD_SHOWN_KEY, now.toString());
      await AsyncStorage.setItem(ADS_SHOWN_COUNT_KEY, this.adsShownInSession.toString());
      logger.debug('Recorded ad shown', {
        placement,
        adsShownInSession: this.adsShownInSession,
        lastShownAt: new Date(now).toISOString()
      });
    } catch (error) {
      logger.error('Error recording ad shown', { error });
    }
  }

  /**
   * Reset ad frequency tracking (for testing)
   */
  async reset(): Promise<void> {
    try {
      await AsyncStorage.removeItem(LAST_AD_SHOWN_KEY);
      await AsyncStorage.removeItem(SESSION_START_KEY);
      await AsyncStorage.removeItem(ADS_SHOWN_COUNT_KEY);
      await AsyncStorage.removeItem(PLACEMENT_COUNTERS_KEY);
      this.lastAdShownTime = 0;
      this.sessionStartTime = 0;
      this.adsShownInSession = 0;
      this.placementCounters = {
        dateDetails: 0,
        reminderCreation: 0,
        yearChange: 0,
        appSession: 0
      };
      logger.debug('Ad frequency tracking reset');
    } catch (error) {
      logger.error('Error resetting ad frequency tracking', { error });
    }
  }
}

// Export a singleton instance
export const adFrequencyService = new AdFrequencyService();
