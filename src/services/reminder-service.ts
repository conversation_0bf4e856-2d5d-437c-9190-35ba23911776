import { UserReminder, ScheduledNotification, OccurrenceInfo } from '@/types/reminders';
import { databaseService } from './database-service';
import { notificationService } from './notification-service';
import { findNextTithiOccurrence, findTithiOccurrencesInRange } from '@/utils/tithi-calculator';
import { logger } from '@/utils/logging-sentry';
import { getCurrentISTDateString, getISTDateString } from '@/utils/time-zone-utils';
import { dataCoordinator } from './data-coordinator';

// Constants
const MAX_SCHEDULED_OCCURRENCES = 4; // Maximum number of occurrences to schedule ahead
const LOOKAHEAD_DAYS = 365; // How far ahead to look for occurrences

class ReminderService {
  private isInitialized = false;
  private isRescheduling = false;

  /**
   * Initialize the reminder service
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      logger.info('Initializing reminder service...');

      // Initialize the notification service
      await notificationService.initialize();

      // Ensure the database is initialized
      if (!databaseService.isDbInitialized) {
        await databaseService.initialize();
      }

      // Connect the notification service's scheduleNextOccurrence method to this service's implementation
      // This avoids circular dependencies between the services
      (notificationService as any).scheduleNextOccurrence = this.scheduleNextOccurrence.bind(this);
      logger.debug('Connected notification service to reminder service for next occurrence scheduling');

      // Schedule all active reminders
      await this.scheduleAllReminders();

      this.isInitialized = true;
      return true;
    } catch (error) {
      logger.error('Error initializing reminder service', { error });
      return false;
    }
  }

  /**
   * Create a new reminder
   */
  async createReminder(reminder: UserReminder): Promise<UserReminder | null> {
    try {
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      // Prepare values for SQL interpolation
      const title = `'${reminder.title.replace(/'/g, "''")}'`;
      const description = reminder.description ? `'${reminder.description.replace(/'/g, "''")}'` : "''";
      const reminderType = `'${reminder.reminderType.replace(/'/g, "''")}'`;
      const odiaMonth = reminder.odiaMonth ? `'${reminder.odiaMonth.replace(/'/g, "''")}'` : 'NULL';
      const paksha = reminder.paksha ? `'${reminder.paksha.replace(/'/g, "''")}'` : 'NULL';
      const tithi = reminder.tithi ? `'${reminder.tithi.replace(/'/g, "''")}'` : 'NULL';
      const isRecurring = reminder.isRecurring ? 1 : 0;
      const recurrenceInterval = reminder.recurrenceInterval || 'NULL';
      const notificationTime = `'${reminder.notificationTime.replace(/'/g, "''")}'`;
      const soundName = `'${reminder.soundName.replace(/'/g, "''")}'`;
      const isEnabled = reminder.isEnabled ? 1 : 0;

      // Insert the reminder into the database
      const insertQuery = `
        INSERT INTO user_reminders (
          title, description, reminder_type, odia_month, paksha, tithi,
          is_recurring, recurrence_interval, notification_time, sound_name, is_enabled
        ) VALUES (
          ${title}, ${description}, ${reminderType}, ${odiaMonth}, ${paksha}, ${tithi},
          ${isRecurring}, ${recurrenceInterval}, ${notificationTime}, ${soundName}, ${isEnabled}
        )
      `;

      await databaseService.executeUpdate(insertQuery);

      // Get the inserted ID
      const result = await databaseService.executeQuery('SELECT last_insert_rowid() as id');
      const id = result[0]?.id;

      if (!id) {
        throw new Error('Failed to get inserted reminder ID');
      }

      // Create the reminder object with the ID
      const newReminder: UserReminder = {
        ...reminder,
        id: Number(id)
      };

      logger.info('Created reminder', { id, title: reminder.title });

      // Schedule notifications if the reminder is enabled
      if (reminder.isEnabled) {
        await this.scheduleReminderNotifications(newReminder, true); // isNewReminder = true
      }

      // Invalidate reminder cache to show new reminder immediately
      await dataCoordinator.invalidateReminderCache();

      return newReminder;
    } catch (error) {
      logger.error('Error creating reminder', { error, reminder });
      return null;
    }
  }

  /**
   * Update an existing reminder
   */
  async updateReminder(reminder: UserReminder): Promise<boolean> {
    if (!reminder.id) {
      logger.error('Cannot update reminder without ID');
      return false;
    }

    try {
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      // Prepare values for SQL interpolation
      const title = `'${reminder.title.replace(/'/g, "''")}'`;
      const description = reminder.description ? `'${reminder.description.replace(/'/g, "''")}'` : "''";
      const reminderType = `'${reminder.reminderType.replace(/'/g, "''")}'`;
      const odiaMonth = reminder.odiaMonth ? `'${reminder.odiaMonth.replace(/'/g, "''")}'` : 'NULL';
      const paksha = reminder.paksha ? `'${reminder.paksha.replace(/'/g, "''")}'` : 'NULL';
      const tithi = reminder.tithi ? `'${reminder.tithi.replace(/'/g, "''")}'` : 'NULL';
      const isRecurring = reminder.isRecurring ? 1 : 0;
      const recurrenceInterval = reminder.recurrenceInterval || 'NULL';
      const notificationTime = `'${reminder.notificationTime.replace(/'/g, "''")}'`;
      const soundName = `'${reminder.soundName.replace(/'/g, "''")}'`;
      const isEnabled = reminder.isEnabled ? 1 : 0;

      // Update the reminder in the database
      const updateQuery = `
        UPDATE user_reminders SET
          title = ${title},
          description = ${description},
          reminder_type = ${reminderType},
          odia_month = ${odiaMonth},
          paksha = ${paksha},
          tithi = ${tithi},
          is_recurring = ${isRecurring},
          recurrence_interval = ${recurrenceInterval},
          notification_time = ${notificationTime},
          sound_name = ${soundName},
          is_enabled = ${isEnabled},
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ${reminder.id}
      `;

      const success = await databaseService.executeUpdate(updateQuery);
      if (!success) {
        throw new Error('Failed to update reminder');
      }

      logger.info('Updated reminder', { id: reminder.id, title: reminder.title });

      // Cancel existing notifications
      await notificationService.cancelReminderNotifications(reminder.id);

      // Schedule new notifications if the reminder is enabled
      if (reminder.isEnabled) {
        await this.scheduleReminderNotifications(reminder, false); // isNewReminder = false
      }

      // Invalidate reminder cache to show updated reminder immediately
      await dataCoordinator.invalidateReminderCache();

      return true;
    } catch (error) {
      logger.error('Error updating reminder', { error, reminderId: reminder.id });
      return false;
    }
  }

  /**
   * Delete a reminder
   */
  async deleteReminder(id: number): Promise<boolean> {
    try {
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      // Cancel all notifications for this reminder
      await notificationService.cancelReminderNotifications(id);

      // Delete the reminder from the database
      // The scheduled_notifications will be deleted automatically due to the ON DELETE CASCADE
      const deleteQuery = `DELETE FROM user_reminders WHERE id = ${id}`;
      const success = await databaseService.executeUpdate(deleteQuery);

      if (!success) {
        throw new Error('Failed to delete reminder');
      }

      logger.info('Deleted reminder', { id });

      // Invalidate reminder cache to remove deleted reminder immediately
      await dataCoordinator.invalidateReminderCache();

      return true;
    } catch (error) {
      logger.error('Error deleting reminder', { error, reminderId: id });
      return false;
    }
  }

  /**
   * Toggle a reminder's enabled state
   */
  async toggleReminder(id: number, enabled: boolean): Promise<boolean> {
    try {
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      // Update the enabled state
      const updateQuery = `
        UPDATE user_reminders
        SET is_enabled = ${enabled ? 1 : 0}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ${id}
      `;

      const success = await databaseService.executeUpdate(updateQuery);
      if (!success) {
        throw new Error('Failed to toggle reminder');
      }

      logger.info('Toggled reminder', { id, enabled });

      // Cancel existing notifications if disabling
      if (!enabled) {
        await notificationService.cancelReminderNotifications(id);
      } else {
        // Schedule notifications if enabling
        const reminder = await this.getReminderById(id);
        if (reminder) {
          await this.scheduleReminderNotifications(reminder, false); // isNewReminder = false
        }
      }

      // Invalidate reminder cache to show toggle change immediately
      await dataCoordinator.invalidateReminderCache();

      return true;
    } catch (error) {
      logger.error('Error toggling reminder', { error, reminderId: id });
      return false;
    }
  }

  /**
   * Get all reminders
   */
  async getAllReminders(): Promise<UserReminder[]> {
    try {
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      // Get all reminders from the database
      const query = `
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled,
          created_at as createdAt, updated_at as updatedAt
        FROM user_reminders
        ORDER BY created_at DESC
      `;

      const results = await databaseService.executeQuery(query);

      // Convert boolean fields
      return results.map((row: any) => ({
        ...row,
        isRecurring: Boolean(row.isRecurring),
        isEnabled: Boolean(row.isEnabled)
      }));
    } catch (error) {
      logger.error('Error getting all reminders', { error });
      return [];
    }
  }

  /**
   * Get a reminder by ID
   */
  async getReminderById(id: number): Promise<UserReminder | null> {
    try {
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      // Get the reminder from the database
      const query = `
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled,
          created_at as createdAt, updated_at as updatedAt
        FROM user_reminders
        WHERE id = ${id}
        LIMIT 1
      `;

      const results = await databaseService.executeQuery(query);
      const result = results.length > 0 ? results[0] : null;

      if (!result) {
        return null;
      }

      // Convert boolean fields
      return {
        ...result,
        isRecurring: Boolean(result.isRecurring),
        isEnabled: Boolean(result.isEnabled)
      };
    } catch (error) {
      logger.error('Error getting reminder by ID', { error, reminderId: id });
      return null;
    }
  }

  /**
   * Get the next occurrence of a reminder
   */
  async getNextOccurrence(reminder: UserReminder): Promise<OccurrenceInfo | null> {
    try {
      if (!reminder.id) {
        logger.warn('Cannot get next occurrence for reminder without ID');
        return null;
      }

      // First, check if there are any scheduled notifications for this reminder
      if (databaseService.isDbInitialized) {
        // Query for the next scheduled notification that hasn't been delivered yet
        const query = `
          SELECT
            sn.id,
            sn.reminder_id AS reminderId,
            sn.notification_id AS notificationId,
            sn.scheduled_date AS scheduledDate,
            sn.status,
            pd.odia_date AS odiaDate,
            pd.odia_month AS odiaMonth,
            pd.paksha,
            pd.tithi_name AS tithi
          FROM scheduled_notifications sn
          JOIN panchang_data pd ON DATE(sn.scheduled_date) = pd.eng_date
          WHERE sn.reminder_id = ${reminder.id}
          AND sn.status = 'scheduled'
          AND DATE(sn.scheduled_date) >= '${getCurrentISTDateString()}'
          ORDER BY sn.scheduled_date ASC
          LIMIT 1
        `;

        const results = await databaseService.executeQuery(query);

        if (results.length > 0) {
          const nextNotification = results[0];
          logger.debug('Found next scheduled notification', {
            reminderId: reminder.id,
            notificationId: nextNotification.notificationId,
            scheduledDate: nextNotification.scheduledDate
          });

          // Return the occurrence info from the scheduled notification
          return {
            date: new Date(nextNotification.scheduledDate),
            odiaDate: nextNotification.odiaDate,
            odiaMonth: nextNotification.odiaMonth,
            paksha: nextNotification.paksha,
            tithi: nextNotification.tithi
          };
        }
      }

      // If no scheduled notifications found or database not initialized,
      // fall back to calculating the next occurrence
      logger.debug('No scheduled notifications found, calculating next occurrence', {
        reminderId: reminder.id
      });

      // Find the next occurrence based on the reminder type
      switch (reminder.reminderType) {
        case 'monthly_tithi':
          return await findNextTithiOccurrence(
            null, // No specific month
            reminder.paksha,
            reminder.tithi
          );

        case 'yearly_tithi':
          return await findNextTithiOccurrence(
            reminder.odiaMonth,
            reminder.paksha,
            reminder.tithi
          );

        case 'specific_date':
          // For future implementation
          return null;

        default:
          logger.error('Unknown reminder type', { type: reminder.reminderType });
          return null;
      }
    } catch (error) {
      logger.error('Error getting next occurrence', { error, reminderId: reminder.id });
      return null;
    }
  }

  /**
   * Schedule notifications for a reminder
   * @param reminder The reminder to schedule notifications for
   * @param isNewReminder Whether this is a brand new reminder (skips duplicate checks for performance)
   */
  private async scheduleReminderNotifications(reminder: UserReminder, isNewReminder = false): Promise<void> {
    if (!reminder.id) {
      logger.error('Cannot schedule notifications for reminder without ID');
      return;
    }

    try {
      // First, check how many notifications are already scheduled for this reminder
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      const existingQuery = `
        SELECT COUNT(*) as count
        FROM scheduled_notifications
        WHERE reminder_id = ${reminder.id}
        AND status = 'scheduled'
        AND DATE(scheduled_date) >= '${getCurrentISTDateString()}'
      `;

      const existingResults = await databaseService.executeQuery(existingQuery);
      const existingCount = existingResults[0]?.count || 0;

      logger.debug('Checking existing scheduled notifications', {
        reminderId: reminder.id,
        existingCount
      });

      // If we already have enough notifications scheduled, skip scheduling new ones
      if (existingCount >= MAX_SCHEDULED_OCCURRENCES) {
        logger.info('Skipping notification scheduling - already have enough scheduled', {
          reminderId: reminder.id,
          existingCount,
          maxAllowed: MAX_SCHEDULED_OCCURRENCES
        });
        return;
      }

      // Calculate how many more notifications we need to schedule
      const neededCount = MAX_SCHEDULED_OCCURRENCES - existingCount;

      // Get occurrences based on reminder type
      let occurrences: OccurrenceInfo[] = [];

      const fromDate = new Date();
      const toDate = new Date(fromDate.getTime() + LOOKAHEAD_DAYS * 24 * 60 * 60 * 1000);

      switch (reminder.reminderType) {
        case 'monthly_tithi':
          occurrences = await findTithiOccurrencesInRange(
            null, // No specific month
            reminder.paksha,
            reminder.tithi,
            fromDate,
            toDate,
            neededCount // Only get as many as we need
          );
          break;

        case 'yearly_tithi':
          occurrences = await findTithiOccurrencesInRange(
            reminder.odiaMonth,
            reminder.paksha,
            reminder.tithi,
            fromDate,
            toDate,
            neededCount // Only get as many as we need
          );
          break;

        case 'specific_date':
          // For future implementation
          break;

        default:
          logger.error('Unknown reminder type', { type: reminder.reminderType });
          return;
      }

      if (occurrences.length === 0) {
        logger.warn('No occurrences found for reminder', {
          reminderId: reminder.id,
          type: reminder.reminderType,
          paksha: reminder.paksha,
          tithi: reminder.tithi
        });
        return;
      }

      logger.info('Notification lifecycle', {
        event: 'occurrences_found',
        reminderId: reminder.id,
        title: reminder.title,
        reminderType: reminder.reminderType,
        count: occurrences.length,
        existingCount,
        dates: occurrences.map(o => o.date.toISOString())
      });

      // If no new occurrences to schedule, return early
      if (occurrences.length === 0) {
        return;
      }

      let successCount = 0;
      let failureCount = 0;

      // Begin a transaction for all notification inserts
      const transactionStarted = await databaseService.beginTransaction();
      if (!transactionStarted) {
        logger.error('Failed to start transaction for scheduling notifications', { reminderId: reminder.id });
        throw new Error('Failed to start transaction');
      }

      try {
        for (const occurrence of occurrences) {
          try {
            // Skip duplicate check for new reminders (performance optimization)
            if (!isNewReminder) {
              // Check if we already have a notification scheduled for this exact date
              const checkExistingQuery = `
                SELECT COUNT(*) as count
                FROM scheduled_notifications
                WHERE reminder_id = ${reminder.id}
                AND status = 'scheduled'
                AND DATE(scheduled_date) = '${getISTDateString(occurrence.date)}'
              `;

              const checkResults = await databaseService.executeQuery(checkExistingQuery);
              const hasExisting = checkResults[0]?.count > 0;

              if (hasExisting) {
                logger.debug('Skipping duplicate notification for date', {
                  reminderId: reminder.id,
                  date: occurrence.date.toISOString()
                });
                continue;
              }
            }

            // Schedule the notification
            const notificationId = await notificationService.scheduleNotification(reminder, occurrence.date);

            // Validate notification ID before inserting into database
            if (notificationId && typeof notificationId === 'string' && notificationId.trim() !== '') {
              // Store the scheduled notification in the database
              const insertQuery = `
                INSERT INTO scheduled_notifications (
                  reminder_id, notification_id, scheduled_date, status
                ) VALUES (
                  ${reminder.id},
                  '${notificationId.replace(/'/g, "''")}', -- Escape single quotes
                  '${occurrence.date.toISOString()}',
                  'scheduled'
                )
              `;

              const insertSuccess = await databaseService.executeUpdate(insertQuery);
              if (insertSuccess) {
                successCount++;
              } else {
                logger.error('Failed to insert notification record into database', {
                  reminderId: reminder.id,
                  notificationId,
                  date: occurrence.date.toISOString()
                });
                failureCount++;
              }
            } else {
              // This is expected when the notification time has already passed for today
              logger.debug('Skipping notification scheduling (expected for past times)', {
                reminderId: reminder.id,
                notificationId,
                type: typeof notificationId,
                date: occurrence.date.toISOString(),
                message: 'Notification service returned null - likely past time'
              });
              // Don't count this as a failure since it's expected behavior
            }
          } catch (occurrenceError) {
            logger.error('Error scheduling individual notification', {
              error: occurrenceError,
              reminderId: reminder.id,
              date: occurrence.date.toISOString()
            });
            failureCount++;
          }
        }

        // If at least one notification was scheduled successfully, commit the transaction
        if (successCount > 0) {
          await databaseService.commitTransaction();
          logger.info('Committed transaction for scheduled notifications', {
            reminderId: reminder.id,
            successCount
          });
        } else {
          // If all notifications failed, roll back the transaction
          await databaseService.rollbackTransaction();
          logger.warn('Rolled back transaction - all notifications failed to schedule', {
            reminderId: reminder.id
          });
        }
      } catch (transactionError) {
        // Roll back the transaction on any error
        await databaseService.rollbackTransaction();
        logger.error('Error during notification scheduling transaction', {
          error: transactionError,
          reminderId: reminder.id
        });
        throw transactionError;
      }

      logger.info('Notification scheduling summary', {
        reminderId: reminder.id,
        existingCount,
        newTotal: existingCount + successCount,
        newSuccess: successCount,
        newFailures: failureCount
      });
    } catch (error) {
      logger.error('Error scheduling reminder notifications', { error, reminderId: reminder.id });
    }
  }

  /**
   * Schedule notifications for all active reminders
   * Uses smart detection to only reschedule when notifications are actually missing
   */
  private async scheduleAllReminders(): Promise<void> {
    try {
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      logger.info('Starting smart notification scheduling check');

      // Step 1: Periodic cleanup of old notification records (maintenance)
      await this.cleanupOldNotifications();

      // Step 2: Quick check - are notifications missing?
      const needsReschedule = await this.detectMissingNotifications();

      if (!needsReschedule) {
        logger.info('All notifications are properly scheduled - skipping reschedule');
        return; // Fast exit - no reschedule needed
      }

      logger.info('Missing notifications detected - performing full reschedule');

      // Step 3: Only if needed - perform full reschedule
      await this.performFullReschedule();

    } catch (error) {
      logger.error('Error in smart notification scheduling', { error });
      // Fallback: try basic scheduling without smart detection
      try {
        logger.info('Falling back to basic notification scheduling');
        await this.performBasicScheduling();
      } catch (fallbackError) {
        logger.error('Fallback scheduling also failed', { error: fallbackError });
      }
    }
  }

  /**
   * Detect if notifications are missing by comparing database vs OS scheduled notifications
   * Returns true if reschedule is needed, false if all notifications are properly scheduled
   */
  private async detectMissingNotifications(): Promise<boolean> {
    try {
      logger.debug('Starting notification sync detection');

      // Step 1: Get all OS-level scheduled notifications
      const osNotifications = await notificationService.getAllScheduledNotifications();
      const osNotificationIds = new Set(osNotifications.map((n: any) => n.identifier as string));

      logger.debug('OS notifications found', {
        count: osNotifications.length,
        ids: Array.from(osNotificationIds).slice(0, 5) // Log first 5 for debugging
      });

      // Step 2: Get all database scheduled notifications for future dates only
      // Use datetime comparison to exclude past notifications (including today's past times)
      const now = new Date();
      const currentISOString = now.toISOString();

      const dbQuery = `
        SELECT notification_id as notificationId, scheduled_date as scheduledDate
        FROM scheduled_notifications
        WHERE status = 'scheduled'
        AND scheduled_date > '${currentISOString}'
      `;

      const dbNotifications = await databaseService.executeQuery(dbQuery);

      logger.debug('Database notifications found', {
        count: dbNotifications.length,
        ids: dbNotifications.slice(0, 5).map(n => n.notificationId) // Log first 5 for debugging
      });

      // Step 3: Check for mismatches
      let missingInOsCount = 0;
      let dbNotificationIds = new Set<string>();

      for (const dbNotif of dbNotifications) {
        if (dbNotif.notificationId) {
          dbNotificationIds.add(dbNotif.notificationId);
          if (!osNotificationIds.has(dbNotif.notificationId)) {
            missingInOsCount++;
          }
        }
      }

      // Step 4: Check for orphaned OS notifications (notifications in OS but not in DB)
      let orphanedInOsCount = 0;
      for (const osId of osNotificationIds) {
        if (!dbNotificationIds.has(osId)) {
          orphanedInOsCount++;
        }
      }

      // Step 5: Determine if reschedule is needed
      const totalMismatches = missingInOsCount + orphanedInOsCount;
      const needsReschedule = totalMismatches > 0;

      logger.info('Notification sync analysis', {
        osCount: osNotifications.length,
        dbCount: dbNotifications.length,
        missingInOs: missingInOsCount,
        orphanedInOs: orphanedInOsCount,
        totalMismatches,
        needsReschedule
      });

      return needsReschedule;

    } catch (error) {
      logger.error('Error detecting missing notifications', { error });
      // Err on the side of caution - reschedule if detection fails
      logger.warn('Detection failed, defaulting to reschedule for safety');
      return true;
    }
  }

  /**
   * Perform full reschedule - clear existing notifications and reschedule all
   * This is called when missing notifications are detected
   */
  private async performFullReschedule(): Promise<void> {
    // Prevent concurrent reschedules
    if (this.isRescheduling) {
      logger.info('Reschedule already in progress, skipping');
      return;
    }

    this.isRescheduling = true;

    try {
      logger.info('Starting full notification reschedule');

      // Step 1: Clean up past notifications first (mark as expired)
      const now = new Date();
      const currentISOString = now.toISOString();

      const expireQuery = `
        UPDATE scheduled_notifications
        SET status = 'expired'
        WHERE status = 'scheduled'
        AND scheduled_date <= '${currentISOString}'
      `;

      const expiredCount = await databaseService.executeUpdate(expireQuery);
      logger.info('Marked past notifications as expired', {
        count: expiredCount,
        cutoffTime: currentISOString
      });

      // Step 2: Clear remaining future scheduled notifications from database
      const clearQuery = `
        UPDATE scheduled_notifications
        SET status = 'cleared_on_reschedule'
        WHERE status = 'scheduled'
      `;

      const clearedCount = await databaseService.executeUpdate(clearQuery);
      logger.info('Cleared future scheduled notifications from database', {
        count: clearedCount
      });

      // Step 3: Cancel all OS-level notifications to ensure clean state
      try {
        await notificationService.cancelAllScheduledNotifications();
        logger.info('Cancelled all OS-level scheduled notifications');
      } catch (cancelError) {
        logger.warn('Error cancelling OS notifications, continuing with reschedule', { error: cancelError });
      }

      // Step 4: Perform basic scheduling for all active reminders
      await this.performBasicScheduling();

      logger.info('Successfully completed full notification reschedule', {
        expiredCount,
        clearedCount
      });
    } catch (error) {
      logger.error('Error in full reschedule', { error });
      throw error;
    } finally {
      this.isRescheduling = false;
    }
  }

  /**
   * Clean up old notification records from database
   * This prevents database pollution and improves performance
   */
  private async cleanupOldNotifications(): Promise<void> {
    try {
      // Clean up notifications older than 7 days
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 7);
      const cutoffISOString = cutoffDate.toISOString();

      const cleanupQuery = `
        DELETE FROM scheduled_notifications
        WHERE status IN ('delivered', 'expired', 'cleared_on_reschedule', 'cancelled')
        AND scheduled_date < '${cutoffISOString}'
      `;

      const deletedCount = await databaseService.executeUpdate(cleanupQuery);

      if (deletedCount && typeof deletedCount === 'number' && deletedCount > 0) {
        logger.info('Cleaned up old notification records', {
          deletedCount,
          cutoffDate: cutoffISOString
        });
      }
    } catch (error) {
      logger.warn('Error cleaning up old notifications', { error });
      // Don't throw - this is maintenance, not critical
    }
  }

  /**
   * Perform basic scheduling for all active reminders
   * This is the fallback method and core scheduling logic
   */
  private async performBasicScheduling(): Promise<void> {
    try {
      logger.info('Starting basic notification scheduling');

      // Get all active reminders
      const query = `
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled
        FROM user_reminders
        WHERE is_enabled = 1
      `;

      const reminders = await databaseService.executeQuery(query);

      if (reminders.length === 0) {
        logger.info('No active reminders to schedule');
        return;
      }

      logger.info('Scheduling notifications for active reminders', { count: reminders.length });

      // Convert boolean fields and schedule notifications for each reminder
      for (const reminder of reminders) {
        const processedReminder: UserReminder = {
          ...reminder,
          isRecurring: Boolean(reminder.isRecurring),
          isEnabled: Boolean(reminder.isEnabled)
        };

        await this.scheduleReminderNotifications(processedReminder, false); // isNewReminder = false
      }

      logger.info('Successfully completed basic notification scheduling');
    } catch (error) {
      logger.error('Error in basic scheduling', { error });
      throw error;
    }
  }

  /**
   * Schedule the next occurrence for a reminder after a notification is delivered
   * This is called by the notification service
   */
  async scheduleNextOccurrence(reminderId: number): Promise<void> {
    try {
      logger.debug('Starting to schedule next occurrence', { reminderId });

      // Get the reminder
      const reminder = await this.getReminderById(reminderId);
      if (!reminder) {
        logger.debug('Reminder not found', { reminderId });
        return;
      }

      if (!reminder.isEnabled) {
        logger.debug('Reminder is disabled, not scheduling next occurrence', { reminderId });
        return;
      }

      if (!reminder.isRecurring) {
        logger.debug('Reminder is not recurring, not scheduling next occurrence', {
          reminderId,
          title: reminder.title,
          type: reminder.reminderType
        });
        return;
      }

      logger.debug('Found recurring reminder', {
        reminderId,
        title: reminder.title,
        type: reminder.reminderType,
        paksha: reminder.paksha,
        tithi: reminder.tithi,
        odiaMonth: reminder.odiaMonth
      });

      // Get the next occurrence after the last scheduled one
      if (!databaseService.isDbInitialized) {
        logger.debug('Database not initialized when scheduling next occurrence', { reminderId });
        throw new Error('Database not initialized');
      }

      // Get the last scheduled notification for this reminder with proper column aliases
      const query = `
        SELECT
          id,
          reminder_id AS reminderId,
          notification_id AS notificationId,
          scheduled_date AS scheduledDate,
          status,
          created_at AS createdAt
        FROM scheduled_notifications
        WHERE reminder_id = ${reminderId}
        ORDER BY scheduled_date DESC
        LIMIT 1
      `;

      logger.debug('Querying for last scheduled notification', { reminderId, query });
      const results = await databaseService.executeQuery(query);
      const lastNotification = results.length > 0 ? results[0] as ScheduledNotification : null;

      if (!lastNotification) {
        logger.debug('No previous notifications found for reminder', { reminderId });
        return;
      }

      // Validate the notification data
      if (!lastNotification.scheduledDate) {
        logger.warn('Last notification has invalid scheduled date', {
          reminderId,
          notificationId: lastNotification.notificationId,
          scheduledDate: lastNotification.scheduledDate
        });
        return;
      }

      logger.debug('Found last scheduled notification', {
        reminderId,
        notificationId: lastNotification.notificationId,
        scheduledDate: lastNotification.scheduledDate,
        status: lastNotification.status
      });

      // Calculate the next occurrence after the last scheduled one
      let lastDate: Date;
      try {
        lastDate = new Date(lastNotification.scheduledDate);

        // Validate the date is valid
        if (isNaN(lastDate.getTime())) {
          throw new Error('Invalid date');
        }
      } catch (dateError) {
        logger.error('Error parsing last notification date', {
          error: dateError,
          reminderId,
          scheduledDate: lastNotification.scheduledDate
        });
        return;
      }
      const nextDay = new Date(lastDate.getTime() + 24 * 60 * 60 * 1000); // Start from the day after

      logger.debug('Calculating next occurrence after', {
        reminderId,
        lastDate: lastDate.toISOString(),
        nextDay: nextDay.toISOString()
      });

      let nextOccurrence: OccurrenceInfo | null = null;

      switch (reminder.reminderType) {
        case 'monthly_tithi':
          logger.debug('Finding next monthly tithi occurrence', {
            reminderId,
            paksha: reminder.paksha,
            tithi: reminder.tithi,
            fromDate: nextDay.toISOString()
          });

          nextOccurrence = await findNextTithiOccurrence(
            null,
            reminder.paksha,
            reminder.tithi,
            nextDay
          );
          break;

        case 'yearly_tithi':
          logger.debug('Finding next yearly tithi occurrence', {
            reminderId,
            odiaMonth: reminder.odiaMonth,
            paksha: reminder.paksha,
            tithi: reminder.tithi,
            fromDate: nextDay.toISOString()
          });

          nextOccurrence = await findNextTithiOccurrence(
            reminder.odiaMonth,
            reminder.paksha,
            reminder.tithi,
            nextDay
          );
          break;

        default:
          logger.debug('Unknown reminder type', {
            reminderId,
            type: reminder.reminderType
          });
          break;
      }

      if (!nextOccurrence) {
        logger.warn('No next occurrence found for reminder', {
          reminderId,
          type: reminder.reminderType,
          paksha: reminder.paksha,
          tithi: reminder.tithi,
          odiaMonth: reminder.odiaMonth
        });
        return;
      }

      logger.debug('Found next occurrence', {
        reminderId,
        date: nextOccurrence.date.toISOString(),
        odiaDate: nextOccurrence.odiaDate,
        paksha: nextOccurrence.paksha,
        tithi: nextOccurrence.tithi
      });

      // Check if we already have a notification scheduled for this date
      const checkExistingQuery = `
        SELECT COUNT(*) as count
        FROM scheduled_notifications
        WHERE reminder_id = ${reminderId}
        AND status = 'scheduled'
        AND DATE(scheduled_date) = '${getISTDateString(nextOccurrence.date)}'
      `;

      const checkResults = await databaseService.executeQuery(checkExistingQuery);
      const hasExisting = checkResults[0]?.count > 0;

      if (hasExisting) {
        logger.info('Skipping next occurrence - notification already scheduled for this date', {
          reminderId,
          date: nextOccurrence.date.toISOString()
        });
        return;
      }

      // Schedule the notification
      logger.debug('Scheduling notification for next occurrence', {
        reminderId,
        date: nextOccurrence.date.toISOString()
      });

      const notificationId = await notificationService.scheduleNotification(
        reminder,
        nextOccurrence.date
      );

      // Validate notification ID before inserting into database
      if (notificationId && typeof notificationId === 'string' && notificationId.trim() !== '') {
        // Begin a transaction for the notification insert
        const transactionStarted = await databaseService.beginTransaction();
        if (!transactionStarted) {
          logger.error('Failed to start transaction for next occurrence notification', { reminderId });
          return;
        }

        try {
          logger.debug('Notification scheduled successfully', {
            reminderId,
            notificationId,
            date: nextOccurrence.date.toISOString()
          });

          // Store the scheduled notification
          const insertQuery = `
            INSERT INTO scheduled_notifications (
              reminder_id, notification_id, scheduled_date, status
            ) VALUES (
              ${reminderId},
              '${notificationId.replace(/'/g, "''")}', -- Escape single quotes
              '${nextOccurrence.date.toISOString()}',
              'scheduled'
            )
          `;

          logger.debug('Storing scheduled notification in database', {
            reminderId,
            notificationId
          });

          const insertSuccess = await databaseService.executeUpdate(insertQuery);

          if (insertSuccess) {
            // Commit the transaction
            await databaseService.commitTransaction();

            logger.info('Notification lifecycle', {
              event: 'next_occurrence_scheduled',
              reminderId,
              notificationId,
              date: nextOccurrence.date.toISOString(),
              odiaMonth: nextOccurrence.odiaMonth,
              paksha: nextOccurrence.paksha,
              tithi: nextOccurrence.tithi
            });
          } else {
            // Roll back the transaction if insert failed
            await databaseService.rollbackTransaction();
            logger.error('Failed to insert next occurrence notification record', {
              reminderId,
              notificationId,
              date: nextOccurrence.date.toISOString()
            });
          }
        } catch (dbError) {
          // Roll back the transaction on any error
          await databaseService.rollbackTransaction();
          logger.error('Error storing scheduled notification in database', {
            error: dbError,
            reminderId,
            notificationId
          });
        }
      } else {
        // This is expected when the notification time has already passed for today
        logger.debug('Skipping next occurrence notification (expected for past times)', {
          reminderId,
          notificationId,
          type: typeof notificationId,
          date: nextOccurrence.date.toISOString(),
          message: 'Notification service returned null - likely past time'
        });
      }
    } catch (error) {
      logger.error('Error scheduling next occurrence', { error, reminderId });
    }
  }

  /**
   * Get reminder dates for a specific month
   * Returns array of day numbers (1-31) that have reminders
   */
  async getReminderDatesForMonth(reminder: UserReminder, year: number, month: number): Promise<number[]> {
    try {
      const fromDate = new Date(year, month - 1, 1); // First day of month
      const toDate = new Date(year, month, 0); // Last day of month

      let occurrences: OccurrenceInfo[] = [];

      switch (reminder.reminderType) {
        case 'monthly_tithi':
          occurrences = await findTithiOccurrencesInRange(
            null, // No specific month
            reminder.paksha,
            reminder.tithi,
            fromDate,
            toDate,
            31 // Max days in a month
          );
          break;

        case 'yearly_tithi':
          occurrences = await findTithiOccurrencesInRange(
            reminder.odiaMonth,
            reminder.paksha,
            reminder.tithi,
            fromDate,
            toDate,
            31 // Max days in a month
          );
          break;

        case 'specific_date':
          // For future implementation
          break;

        default:
          logger.error('Unknown reminder type', { type: reminder.reminderType });
          return [];
      }

      // Extract day numbers from occurrences
      const dayNumbers = occurrences
        .filter(occurrence => {
          const occurrenceDate = new Date(occurrence.date);
          return occurrenceDate.getFullYear() === year &&
                 occurrenceDate.getMonth() === month - 1;
        })
        .map(occurrence => occurrence.date.getDate());

      return [...new Set(dayNumbers)]; // Remove duplicates
    } catch (error) {
      logger.error('Error getting reminder dates for month', {
        error,
        reminderId: reminder.id,
        year,
        month
      });
      return [];
    }
  }

  /**
   * Check if a reminder occurs on a specific date
   */
  async doesReminderOccurOnDate(reminder: UserReminder, dateString: string): Promise<boolean> {
    try {
      const targetDate = new Date(dateString);
      const year = targetDate.getFullYear();
      const month = targetDate.getMonth() + 1;

      const reminderDates = await this.getReminderDatesForMonth(reminder, year, month);
      const dayNumber = targetDate.getDate();

      return reminderDates.includes(dayNumber);
    } catch (error) {
      logger.error('Error checking if reminder occurs on date', {
        error,
        reminderId: reminder.id,
        dateString
      });
      return false;
    }
  }
}

export const reminderService = new ReminderService();
