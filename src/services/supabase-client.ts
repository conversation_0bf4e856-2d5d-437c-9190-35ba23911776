// src/services/supabase-client.ts
import 'react-native-url-polyfill/auto'; // Required for Supabase on React Native
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logging-sentry'; // Import the logger
import { remoteConfig } from './RemoteConfigService'; // Import the remote config service
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@/config/secrets'; // Import default secrets

// Default configuration for reference
const DEFAULT_CONFIG = {
  version: 1,
  supabaseUrl: SUPABASE_URL,
  supabaseAnonKey: SUPABASE_ANON_KEY,
};

// Create a function to get the Supabase client
// This allows us to create the client after the remote config is loaded
let supabaseInstance: ReturnType<typeof createClient> | null = null;

/**
 * Initialize the Supabase client with the current configuration
 * This uses the fast initialization approach with cached/default config
 */
export const initializeSupabase = async (): Promise<void> => {
  logger.info('Supabase: Starting initialization');

  try {
    // Initialize with cache or defaults (fast)
    logger.debug('Supabase: Fast initializing with cache or defaults');
    const config = await remoteConfig.initializeWithCache();

    // Get the Supabase URL and anon key from the config
    const supabaseUrl = config.supabaseUrl;
    const supabaseAnonKey = config.supabaseAnonKey;

    logger.debug('Supabase: Got configuration from cache/defaults', {
      urlPrefix: supabaseUrl ? supabaseUrl.substring(0, 15) + '...' : 'undefined',
      hasAnonKey: !!supabaseAnonKey
    });

    // Basic check for credentials
    if (!supabaseUrl || !supabaseAnonKey) {
      logger.error('Supabase: URL or Anon Key is missing. Please check configuration.');
      return;
    }

    // Create the Supabase client
    logger.debug('Supabase: Creating client with configuration');
    supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        storage: AsyncStorage,
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
      },
    });

    logger.info('Supabase: Client initialized successfully with configuration from ' +
      (config === DEFAULT_CONFIG ? 'default' : 'cache'));

    // Trigger background refresh without waiting
    setTimeout(() => {
      logger.debug('Supabase: Triggering background refresh of configuration');
      remoteConfig.refreshInBackground()
        .then(result => {
          if (result.updated) {
            logger.info('Supabase: Remote config updated in background, will apply on next app start');
          } else if (result.fetchFailed) {
            logger.debug('Supabase: Remote config fetch failed, using current configuration');
          } else {
            logger.debug('Supabase: Remote config checked, no updates needed');
          }
        })
        .catch(error => {
          logger.error('Supabase: Background config refresh failed', { error });
        });
    }, 100); // Small delay to prioritize UI rendering
  } catch (error) {
    logger.error('Supabase: Failed to initialize client', { error });
    throw error;
  }
};

/**
 * Get the Supabase client
 * If the client hasn't been initialized yet, it will initialize it first
 */
export const getSupabase = async () => {
  if (!supabaseInstance) {
    logger.debug('Supabase: Client not initialized, initializing now');
    await initializeSupabase();
  }

  if (!supabaseInstance) {
    logger.error('Supabase: Client still null after initialization attempt');
    throw new Error('Failed to initialize Supabase client');
  }

  logger.debug('Supabase: Returning initialized client');
  return supabaseInstance;
};

// Track if initialization has started to avoid multiple parallel initializations
let initializationStarted = false;
let initializationPromise: Promise<void> | null = null;

// For backward compatibility, export a supabase object that will initialize on first use
export const supabase = new Proxy({} as ReturnType<typeof createClient>, {
  get: (_target, prop) => {
    // If supabaseInstance is not initialized, initialize it first
    if (!supabaseInstance) {
      // Only log a debug message and start initialization once
      if (!initializationStarted) {
        // Using debug level instead of warn to prevent cluttering Sentry logs
        // This is an expected behavior with the proxy pattern
        logger.debug('Supabase: Accessing client before initialization. Initializing now...', { property: String(prop) });
        initializationStarted = true;

        // Start initialization in the background and store the promise
        initializationPromise = initializeSupabase().catch(error => {
          logger.error('Supabase: Background initialization failed', { error });
        });
      }

      // Return a promise that resolves to the property
      return async (...args: any[]) => {
        try {
          // Wait for initialization to complete if it's in progress
          if (initializationPromise) {
            await initializationPromise;
          }

          const client = await getSupabase();
          // Only log at debug level to reduce noise
          if (prop !== 'from' && prop !== 'rest') { // These are called very frequently
            logger.debug(`Supabase: Accessing property ${String(prop)} after lazy initialization`);
          }
          // @ts-ignore
          return client[prop](...args);
        } catch (error) {
          logger.error(`Supabase: Error accessing ${String(prop)} after lazy initialization`, { error });
          throw error;
        }
      };
    }

    // If supabaseInstance is initialized, return the property
    // Only log at debug level for less common properties to reduce noise
    if (prop !== 'from' && prop !== 'rest') { // These are called very frequently
      logger.debug(`Supabase: Accessing property ${String(prop)} on initialized client`);
    }
    // @ts-ignore
    return supabaseInstance[prop];
  }
});
