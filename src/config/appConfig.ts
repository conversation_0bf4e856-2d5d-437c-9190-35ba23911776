// Centralized application configuration
import { ThemeMode, SupportedLanguage, CalendarViewMode } from '@/types/calendar';

export const appConfig = {
  review: {
    // Settings for the automatic in-app review prompt
    minOpens: 2, // Minimum app opens before prompting
    minDaysBetweenRequests: 30, // Minimum days before prompting again
  },
  store: {
    // App Store / Play Store information
    androidPackageName: 'com.kalingatech.odia.simplecalendar', // Your Google Play package name
    androidDeveloperName: 'Gyanaranjan Nayak', // Google Play Developer Name
    androidDeveloperUrl: 'https://play.google.com/store/apps/developer?id=<PERSON><PERSON><PERSON>jan+Nayak', // Developer page URL
    iosDeveloperUrl: 'https://apps.apple.com/developer/id1234567890', // iOS Developer URL (placeholder)
  },
  share: {
    // URL to use when sharing the app
    // Consider using a universal link or a landing page URL if available
    appUrl: 'https://play.google.com/store/apps/details?id=com.kalingatech.odia.simplecalendar',
  },
  settingsScreen: {
    // Timeouts and delays specific to the settings screen
    updateStatusTimeoutMs: 5000, // How long (ms) to show the update status message
  },
  ads: {
    // AdMob configuration
    // androidAppId is configured via app.json plugin settings
    iosAppId: 'ca-app-pub-7418777810665263~5096646482', // iOS App ID
    // Banner ad units for different placements
    bannerAdUnits: {
      stickyAd: 'ca-app-pub-7418777810665263/3515971501',      // Sticky ad at bottom of screens
      calendarScreen: 'ca-app-pub-7418777810665263/9367914045', // Calendar screen banner
      settingsScreen: 'ca-app-pub-7418777810665263/1972849181', // Settings screen banner
      dateDetailsModal: 'ca-app-pub-7418777810665263/3285930857', // Date details modal banner
      remindersScreen: 'ca-app-pub-7418777810665263/9367914045'  // Reminders screen banner (reusing calendar screen ad unit)
    },
    // Interstitial ad units for different placements
    // TODO: Replace these placeholder ad unit IDs with actual ones from your AdMob console
    interstitialAdUnits: {
      dateDetails: 'ca-app-pub-7418777810665263/9230254618', 
      reminderCreation: 'ca-app-pub-7418777810665263/7088975303', 
      yearChange: 'ca-app-pub-7418777810665263/3297662857', 
      appSession: 'ca-app-pub-7418777810665263/9671499517', 
    },
    // Frequency capping settings (relaxed for testing)
    interstitialFrequency: {
      minTimeBetweenAds: 90000, // 30 seconds between ads (testing value)
      sessionTimeout: 1800000, // 30 minutes (in milliseconds)
      maxAdsPerSession: 4, // Maximum ads per session (increased for testing)
      initialDelay: 95000, // 10 seconds initial delay (testing value)
      // New user protection settings (relaxed for testing)
      newUserProtection: true, // Enable special handling for new users
      newUserInitialDelay: 120000, // 30 seconds initial delay for new users (testing value)
      newUserSessionThreshold: 1, // 1 session before treating as regular user (testing value)
    },
    // Placement-specific settings
    interstitialPlacement: {
      dateDetails: {
        enabled: true,
        probability: 1.0, // 100% chance of showing when conditions are met (testing)
        frequency: 3, // Show ad after every 2 modal interactions (testing)
      },
      reminderCreation: {
        enabled: true,
        probability: 1.0, // 100% chance of showing when conditions are met
      },
      yearChange: {
        enabled: true,
        probability: 0.5, // 50% chance of showing when conditions are met
        minYearDifference: 1, // Only show when year changes, not just month
      },
      appSession: {
        enabled: true,
        probability: 0.6, // 60% chance of showing when conditions are met
        minSessionTime: 180000, // 3 minutes minimum session time
      }
    }
  },
  remoteConfig: {
    // Remote configuration settings
    url: 'https://dsgyana.github.io/kalingatech/odia-calendar-config.json', // URL for remote configuration
    refreshInterval: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    cacheKeys: {
      config: 'odia_calendar_remote_config',
      lastFetched: 'odia_calendar_config_last_fetched'
    }
  },
  cache: {
    // Cache configuration
    defaultTTL: 3600000, // Default cache TTL: 1 hour in milliseconds
  },
  defaultSettings: {
    // Default application settings
    themeMode: 'system' as ThemeMode,
    language: 'or' as SupportedLanguage, // Set default language to Odia
    showAds: true,
    use24HourFormat: false,
    festivalNotifications: true,
    marriageNotifications: true,
    defaultCalendarView: 'month' as CalendarViewMode,
    showWeekNumbers: false,
    firstDayOfWeek: 0,
    autoUpdateEnabled: true, // Default to enabled
    autoUpdateWifiOnly: false, // Default to allow on any connection
    syncCheckInterval: 60, // Check every 1 hour (renamed from syncInterval)
    errorReportingEnabled: true // Enable error reporting by default
  },
};

// It's recommended to replace the placeholder values above (YOUR_..._HERE)
// with your actual IDs before building for production.
