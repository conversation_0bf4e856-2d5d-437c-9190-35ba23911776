import { useState, useEffect, useCallback } from 'react';
import { dataCoordinator } from '@/services/data-coordinator';
import { PanchangData } from '@/types/database';
import { useCalendarStore } from '@/store/calendar-store'; // Import calendar store
import { useComponentLogger } from '@/utils/logging-sentry'; // Import the logger hook

/**
 * Hook for accessing calendar month data
 * @param year The year
 * @param month The month (1-12)
 */
export function useCalendarMonth(year: number, month: number) {
  const logger = useComponentLogger('useCalendarMonth'); // Get logger instance
  const [data, setData] = useState<PanchangData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  // Subscribe to data update notifications
  const lastDataUpdateTime = useCalendarStore((state) => state.lastDataUpdateTime);

  // Use callback to ensure the fetch function is stable
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      // Get month data - dataCoordinator handles its own initialization
      const monthData = await dataCoordinator.getMonthData(year, month);

      setData(monthData);
      setError(null);
    } catch (err) {
      setError(err as Error);
      logger.error('Error fetching calendar month data', { error: err });
    } finally {
      setLoading(false);
    }
  }, [year, month, logger]); // lastDataUpdateTime intentionally left out to avoid double fetch

  useEffect(() => {
    let isMounted = true;

    // Only fetch if the component is still mounted
    if (isMounted) {
      fetchData();
    }

    return () => {
      isMounted = false;
    };
  }, [year, month, lastDataUpdateTime, fetchData]);

  return { data, loading, error };
}

/**
 * Hook for accessing date details
 * @param dateString The date string in YYYY-MM-DD format
 */
export function useDateDetails(dateString: string | null) {
  const logger = useComponentLogger('useDateDetails'); // Get logger instance
  const [data, setData] = useState<PanchangData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    async function fetchData() {
      if (!dateString) {
        setData(null);
        return;
      }

      try {
        setLoading(true);

        // Get date data - dataCoordinator handles its own initialization
        const dateData = await dataCoordinator.getDateData(dateString);

        if (isMounted) {
          setData(dateData);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          setError(err as Error);
          logger.error('Error fetching date details', { dateString, error: err });
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [dateString]);

  return { data, loading, error };
}

/**
 * Hook for accessing festivals for a month
 * @param year The year
 * @param month The month (1-12)
 */
export function useFestivals(year: number, month: number) {
  const logger = useComponentLogger('useFestivals'); // Get logger instance
  const [festivals, setFestivals] = useState<PanchangData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  // Subscribe to data update notifications
  const lastDataUpdateTime = useCalendarStore((state) => state.lastDataUpdateTime);

  useEffect(() => {
    let isMounted = true;

    async function fetchData() {
      try {
        setLoading(true);

        // Get festivals - dataCoordinator handles its own initialization
        const festivalData = await dataCoordinator.getFestivalsForMonth(year, month);

        if (isMounted) {
          setFestivals(festivalData);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          setError(err as Error);
          logger.error('Error fetching festivals', { year, month, error: err });
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchData();

    return () => {
      isMounted = false;
    };
    // Add lastDataUpdateTime to dependency array
  }, [year, month, lastDataUpdateTime]);

  return { festivals, loading, error };
}

/**
 * Hook for accessing marriage dates for a month
 * @param year The year
 * @param month The month (1-12)
 */
export function useMarriageDates(year: number, month: number) {
  const logger = useComponentLogger('useMarriageDates'); // Get logger instance
  const [marriageDates, setMarriageDates] = useState<PanchangData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  // Subscribe to data update notifications
  const lastDataUpdateTime = useCalendarStore((state) => state.lastDataUpdateTime);

  useEffect(() => {
    let isMounted = true;

    async function fetchData() {
      try {
        setLoading(true);

        // Get marriage dates - dataCoordinator handles its own initialization
        const marriageData = await dataCoordinator.getMarriageDatesForMonth(year, month);

        if (isMounted) {
          setMarriageDates(marriageData);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          setError(err as Error);
          logger.error('Error fetching marriage dates', { year, month, error: err });
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchData();

    return () => {
      isMounted = false;
    };
    // Add lastDataUpdateTime to dependency array
  }, [year, month, lastDataUpdateTime]);

  return { marriageDates, loading, error };
}

/**
 * Hook for accessing brata ghara dates for a month
 * @param year The year
 * @param month The month (1-12)
 */
export function useBrataGharaDates(year: number, month: number) {
  const logger = useComponentLogger('useBrataGharaDates'); // Get logger instance
  const [brataGharaDates, setBrataGharaDates] = useState<PanchangData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  // Subscribe to data update notifications
  const lastDataUpdateTime = useCalendarStore((state) => state.lastDataUpdateTime);

  useEffect(() => {
    let isMounted = true;

    async function fetchData() {
      try {
        setLoading(true);

        // Get brata ghara dates - dataCoordinator handles its own initialization
        const brataGharaData = await dataCoordinator.getBrataGharaDatesForMonth(year, month);

        if (isMounted) {
          setBrataGharaDates(brataGharaData);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          setError(err as Error);
          logger.error('Error fetching brata ghara dates', { year, month, error: err });
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchData();

    return () => {
      isMounted = false;
    };
    // Add lastDataUpdateTime to dependency array
  }, [year, month, lastDataUpdateTime]);

  return { brataGharaDates, loading, error };
}

/**
 * Hook for accessing Odia months for a Gregorian month
 * @param year The year
 * @param month The month (1-12)
 */
export function useOdiaMonths(year: number, month: number) {
  const logger = useComponentLogger('useOdiaMonths');
  const [odiaMonths, setOdiaMonths] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  // Subscribe to data update notifications
  const lastDataUpdateTime = useCalendarStore((state) => state.lastDataUpdateTime);

  useEffect(() => {
    let isMounted = true;

    async function fetchData() {
      try {
        setLoading(true);

        // Get Odia months - dataCoordinator handles its own initialization and caching
        const odiaMonthsData = await dataCoordinator.getOdiaMonthsForMonth(year, month);

        if (isMounted) {
          setOdiaMonths(odiaMonthsData);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          setError(err as Error);
          logger.error('Error fetching Odia months', { year, month, error: err });
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchData();

    return () => {
      isMounted = false;
    };
    // Add lastDataUpdateTime to dependency array
  }, [year, month, lastDataUpdateTime, logger]);

  return { odiaMonths, loading, error };
}
