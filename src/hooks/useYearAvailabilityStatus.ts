import { useState, useEffect, useCallback } from 'react';
import { dataCoordinator } from '@/services/data-coordinator';
import { logger } from '@/utils/logging-sentry'; // Import the logger

export type YearAvailabilityStatus =
  | 'CHECKING_AVAILABILITY'
  | 'AVAILABLE_LOCALLY'
  | 'AVAILABLE_REMOTELY'
  | 'NOT_AVAILABLE_ANYWHERE'
  | 'ERROR_CHECKING_AVAILABILITY';

export interface UseYearAvailabilityStatusResult {
  yearStatus: YearAvailabilityStatus;
  isLoadingYearStatus: boolean;
  serverVersionIfRemote?: number | null;
  triggerYearDownload: (yearToDownload: number) => Promise<boolean>;
  checkYearStatus: () => void; // Function to manually re-trigger the check
}

/**
 * Hook to check the availability status of calendar data for a specific year.
 * Provides status updates and a function to trigger manual download if needed.
 * @param year The year number to check.
 */
export function useYearAvailabilityStatus(year: number): UseYearAvailabilityStatusResult {
  const [yearStatus, setYearStatus] = useState<YearAvailabilityStatus>('CHECKING_AVAILABILITY');
  const [isLoadingYearStatus, setIsLoadingYearStatus] = useState<boolean>(true);
  const [serverVersionIfRemote, setServerVersionIfRemote] = useState<number | null | undefined>(undefined);
  const [triggerCheck, setTriggerCheck] = useState<number>(0); // State to manually trigger effect

  const checkYearStatus = useCallback(() => {
    setTriggerCheck(prev => prev + 1); // Increment to trigger useEffect
  }, []);

  useEffect(() => {
    let isMounted = true;
    const yearStr = year.toString();

    const fetchStatus = async () => {
      logger.debug(`[useYearAvailabilityStatus] Checking status for year ${yearStr}`);
      setIsLoadingYearStatus(true);
      setYearStatus('CHECKING_AVAILABILITY'); // Set status while checking
      setServerVersionIfRemote(undefined); // Reset server version

      try {
        // Get year display info - dataCoordinator handles its own initialization
        const result = await dataCoordinator.getYearDisplayInfo(year);

        if (isMounted) {
          setYearStatus(result.status);
          setServerVersionIfRemote(result.serverVersion);
          logger.debug(`[useYearAvailabilityStatus] Status for year ${yearStr}: ${result.status}`);
        }
      } catch (error) {
        logger.error(`[useYearAvailabilityStatus] Error fetching year status for ${yearStr}`, { error });
        if (isMounted) {
          setYearStatus('ERROR_CHECKING_AVAILABILITY');
        }
      } finally {
        if (isMounted) {
          setIsLoadingYearStatus(false);
        }
      }
    };

    fetchStatus();

    return () => {
      isMounted = false;
    };
  }, [year, triggerCheck]); // Re-run effect when year or triggerCheck changes

  const triggerYearDownload = useCallback(async (yearToDownload: number): Promise<boolean> => {
    logger.info(`[useYearAvailabilityStatus] Attempting manual download for year ${yearToDownload}`);
    setIsLoadingYearStatus(true); // Indicate loading during download attempt
    try {
      const success = await dataCoordinator.manuallyDownloadYearData(yearToDownload);
      if (success) {
        logger.info(`[useYearAvailabilityStatus] Manual download successful for year ${yearToDownload}. Re-checking status...`);
        // Manually trigger a re-check of the status after successful download
        checkYearStatus();
        return true;
      } else {
        logger.warn(`[useYearAvailabilityStatus] Manual download failed or reported no changes for year ${yearToDownload}.`);
        // Optionally set an error state or revert loading state here
        setIsLoadingYearStatus(false); // Revert loading state on failure
        return false;
      }
    } catch (error) {
      logger.error(`[useYearAvailabilityStatus] Error triggering manual download for year ${yearToDownload}`, { error });
      setIsLoadingYearStatus(false); // Revert loading state on error
      return false;
    }
    // Note: setIsLoadingYearStatus(false) will be handled by the useEffect re-running after checkYearStatus()
  }, [checkYearStatus]); // Include checkYearStatus in dependencies

  return {
    yearStatus,
    isLoadingYearStatus,
    serverVersionIfRemote,
    triggerYearDownload,
    checkYearStatus,
  };
}
