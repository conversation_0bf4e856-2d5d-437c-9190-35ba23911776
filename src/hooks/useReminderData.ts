import { useState, useEffect, useCallback } from 'react';
import { useComponentLogger } from '@/utils/logging-sentry';
import { dataCoordinator } from '@/services/data-coordinator';
import { UserReminder } from '@/types/reminders';

/**
 * Hook to get reminders for a specific month
 * Returns dates that have reminders set
 * Follows the same pattern as calendar data hooks
 */
export function useMonthReminders(year: number, month: number) {
  const logger = useComponentLogger('useMonthReminders');
  const [reminderDates, setReminderDates] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Use callback to ensure the fetch function is stable
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      // Get reminder dates - dataCoordinator will handle caching and deduplication
      const dates = await dataCoordinator.getReminderDatesForMonth(year, month);

      setReminderDates(dates);
      setError(null);
    } catch (err) {
      setError(err as Error);
      logger.error('Error fetching reminder dates', { year, month, error: err });
    } finally {
      setLoading(false);
    }
  }, [year, month]); // Remove logger dependency

  useEffect(() => {
    let isMounted = true;

    // Only fetch if the component is still mounted
    if (isMounted) {
      fetchData();
    }

    return () => {
      isMounted = false;
    };
  }, [year, month]); // Remove fetchData dependency to prevent infinite loop

  return {
    reminderDates,
    loading,
    error,
    refetch: fetchData
  };
}

/**
 * Hook to get reminders for a specific date
 * Returns reminders that occur on the given date
 * Uses fast query from scheduled_notifications + user_reminders join
 */
export function useDateReminders(dateString: string | null) {
  const logger = useComponentLogger('useDateReminders');
  const [reminders, setReminders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Use callback to ensure the fetch function is stable
  const fetchData = useCallback(async () => {
    if (!dateString) {
      setReminders([]);
      return;
    }

    try {
      setLoading(true);

      // Get reminder details - dataCoordinator will handle the fast query
      const reminderDetails = await dataCoordinator.getReminderDetailsForDate(dateString);

      setReminders(reminderDetails);
      setError(null);
    } catch (err) {
      setError(err as Error);
      logger.error('Error fetching date reminders', { dateString, error: err });
    } finally {
      setLoading(false);
    }
  }, [dateString]); // Remove logger dependency

  useEffect(() => {
    let isMounted = true;

    // Only fetch if the component is still mounted
    if (isMounted) {
      fetchData();
    }

    return () => {
      isMounted = false;
    };
  }, [dateString]); // Remove fetchData dependency to prevent infinite loop

  return { reminders, loading, error };
}

/**
 * Hook to get scheduled notifications for a specific reminder
 * Returns actual scheduled notifications from the database
 */
export function useScheduledNotifications(reminderId: number | null) {
  const logger = useComponentLogger('useScheduledNotifications');
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Use callback to ensure the fetch function is stable
  const fetchData = useCallback(async () => {
    if (!reminderId) {
      setNotifications([]);
      return;
    }

    try {
      setLoading(true);

      // Get scheduled notifications - dataCoordinator will handle timezone conversion
      const scheduledNotifications = await dataCoordinator.getScheduledNotificationsForReminder(reminderId);

      setNotifications(scheduledNotifications);
      setError(null);
    } catch (err) {
      setError(err as Error);
      logger.error('Error fetching scheduled notifications', { reminderId, error: err });
    } finally {
      setLoading(false);
    }
  }, [reminderId]);

  useEffect(() => {
    let isMounted = true;

    // Only fetch if the component is still mounted
    if (isMounted) {
      fetchData();
    }

    return () => {
      isMounted = false;
    };
  }, [reminderId]);

  return { notifications, loading, error, refetch: fetchData };
}
