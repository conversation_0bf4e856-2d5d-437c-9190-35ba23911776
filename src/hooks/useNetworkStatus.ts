import { useState, useEffect, useCallback } from 'react';
import NetInfo, { NetInfoState, NetInfoSubscription } from '@react-native-community/netinfo';
import { logger } from '@/utils/logging-sentry';

/**
 * Hook to monitor and check network connectivity status
 * 
 * @returns Object with network status information and utility functions
 */
export function useNetworkStatus() {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isInternetReachable, setIsInternetReachable] = useState<boolean | null>(null);
  const [connectionType, setConnectionType] = useState<string | null>(null);
  
  // Check network status on demand
  const checkNetworkStatus = useCallback(async (): Promise<{
    isConnected: boolean | null;
    isInternetReachable: boolean | null;
  }> => {
    try {
      const state = await NetInfo.fetch();
      return {
        isConnected: state.isConnected,
        isInternetReachable: state.isInternetReachable,
      };
    } catch (error) {
      logger.error('Failed to check network status', { error });
      return {
        isConnected: null,
        isInternetReachable: null,
      };
    }
  }, []);

  // Utility function to check if network is available before performing an operation
  const ensureNetworkConnection = useCallback(async (): Promise<boolean> => {
    try {
      const state = await NetInfo.fetch();
      return !!(state.isConnected && state.isInternetReachable);
    } catch (error) {
      logger.error('Failed to check network connection', { error });
      return false;
    }
  }, []);

  useEffect(() => {
    // Set up NetInfo listener
    const unsubscribe: NetInfoSubscription = NetInfo.addEventListener((state: NetInfoState) => {
      setIsConnected(state.isConnected);
      setIsInternetReachable(state.isInternetReachable);
      setConnectionType(state.type);
      
      // Log significant connectivity changes
      if (state.isConnected === false) {
        logger.info('Device is offline');
      } else if (state.isConnected === true && state.isInternetReachable === false) {
        logger.info('Device is connected to network but internet is not reachable');
      } else if (state.isConnected === true && state.isInternetReachable === true) {
        logger.info('Device is online with internet access');
      }
    });

    // Initial check
    NetInfo.fetch().then((state) => {
      setIsConnected(state.isConnected);
      setIsInternetReachable(state.isInternetReachable);
      setConnectionType(state.type);
    });

    // Clean up listener on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  return {
    isConnected,
    isInternetReachable,
    connectionType,
    isOnline: isConnected === true && isInternetReachable === true,
    checkNetworkStatus,
    ensureNetworkConnection,
  };
}
