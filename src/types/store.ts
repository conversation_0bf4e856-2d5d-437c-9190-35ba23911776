/**
 * Store type definitions
 * These types define the structure of our Zustand stores
 */

import { AppSettings } from './settings';
import { CalendarSyncStatus, SupportedLanguage, ThemeMode } from './calendar';
// Removed import from './performance'

/**
 * Settings store state interface
 */
export interface SettingsState {
  /** The current theme mode */
  themeMode: ThemeMode;

  /** The current language */
  language: SupportedLanguage;

  /** Whether to show ads */
  showAds: boolean;

  /** Whether to enable error reporting */
  errorReportingEnabled: boolean;

  /** Whether to automatically download calendar data updates */
  autoUpdateEnabled: boolean;

  /** Whether to only download updates on Wi-Fi */
  autoUpdateWifiOnly: boolean;

  /** Set the theme mode */
  setThemeMode: (themeMode: ThemeMode) => void;

  /** Set the language */
  setLanguage: (language: SupportedLanguage) => void;

  /** Toggle ads visibility */
  toggleAds: () => void;

  /** Toggle error reporting */
  toggleErrorReporting: () => void;

  /** Toggle automatic updates */
  toggleAutoUpdate: () => void;

  /** Toggle Wi-Fi only updates */
  toggleWifiOnlyUpdate: () => void;

  /** Reset settings to defaults */
  resetSettings: () => void;
}

/**
 * Calendar store state interface
 */
export interface CalendarState {
  /** The currently selected date in YYYY-MM-DD format */
  selectedDate: string | null;

  /** The currently selected year */
  selectedYear: number;

  /** The currently selected month (1-12) */
  selectedMonth: number;

  /** Whether the date details modal is visible */
  isDateDetailsModalVisible: boolean;

  /** The sync status of the calendar data */
  syncStatus: CalendarSyncStatus;

  /** Set the selected date */
  setSelectedDate: (date: string | null) => void;

  /** Set the selected year */
  setSelectedYear: (year: number) => void;

  /** Set the selected month */
  setSelectedMonth: (month: number) => void;

  /** Set the date details modal visibility */
  setDateDetailsModalVisible: (visible: boolean) => void;

  /** Navigate to the previous month */
  goToPreviousMonth: () => void;

  /** Navigate to the next month */
  goToNextMonth: () => void;

  /** Navigate to today */
  goToToday: () => void;

  /** Set the sync status */
  setSyncStatus: (status: Partial<CalendarSyncStatus>) => void;

  /** Timestamp of the last data update notification */
  lastDataUpdateTime: number | null;

  /** Action to notify that data has been updated */
  notifyDataUpdated: () => void;

  /** Whether an update is pending manual confirmation */
  isUpdatePending: boolean;

  /** Whether to show the update prompt modal */
  showUpdatePrompt: boolean;

  /** Set the update pending status */
  setIsUpdatePending: (pending: boolean) => void;

  /** Set the update prompt visibility */
  setShowUpdatePrompt: (visible: boolean) => void;
}

// Removed PerformanceState interface definition
