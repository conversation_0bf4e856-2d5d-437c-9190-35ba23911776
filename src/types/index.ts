/**
 * Type definitions index
 * This file exports all types from the types directory
 */

export * from './calendar';
export * from './settings';
export * from './store';
// Removed export * from './performance';

/**
 * Common utility types
 */

/**
 * Makes specific properties of T optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Makes specific properties of T required
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

/**
 * Extracts the type of a promise
 */
export type Awaited<T> = T extends Promise<infer U> ? U : T;

/**
 * Makes all properties of T deeply readonly
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: DeepReadonly<T[P]>;
};

/**
 * Makes all properties of T deeply partial
 */
export type DeepPartial<T> = {
  [P in keyof T]?: DeepPartial<T[P]>;
};
