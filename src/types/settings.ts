/**
 * Settings type definitions
 * These types define the structure of application settings
 */

import { SupportedLanguage, ThemeMode, CalendarViewMode } from './calendar';

/**
 * Application settings interface
 */
export interface AppSettings {
  /** The user's preferred theme mode */
  themeMode: ThemeMode;

  /** The user's preferred language */
  language: SupportedLanguage;

  /** Whether to show ads in the application */
  showAds: boolean;

  /** Whether to use 24-hour time format */
  use24HourFormat: boolean;

  /** Whether to show notifications for festivals */
  festivalNotifications: boolean;

  /** Whether to show notifications for marriage dates */
  marriageNotifications: boolean;

  /** The default calendar view mode */
  defaultCalendarView: CalendarViewMode;

  /** Whether to show week numbers in the calendar */
  showWeekNumbers: boolean;

  /** The first day of the week (0 = Sunday, 1 = Monday, etc.) */
  firstDayOfWeek: number;

  /** Whether to automatically download calendar data updates */
  autoUpdateEnabled: boolean;

  /** Whether to only download updates on Wi-Fi */
  autoUpdateWifiOnly: boolean;

  /** How often to check for calendar data updates (in minutes) - Note: Actual sync might be deferred based on settings */
  syncCheckInterval: number; // Renamed from syncInterval for clarity

  /** Whether to enable error reporting */
  errorReportingEnabled: boolean;
}

/**
 * Default application settings
 * Imported from appConfig.ts for centralized configuration
 */
import { appConfig } from '@/config/appConfig';

export const DEFAULT_SETTINGS: AppSettings = appConfig.defaultSettings;
