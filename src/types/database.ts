/**
 * Represents a row in the panchang_data table
 */
export interface PanchangData {
  /** The Gregorian date in YYYY-MM-DD format */
  eng_date: string;
  
  /** The Odia date */
  odia_date: string;
  
  /** The Odia month name */
  odia_month: string;
  
  /** The Odia year */
  odia_year: string;
  
  /** The day name */
  day_name: string;
  
  /** The paksha (lunar phase) */
  paksha: string;
  
  /** The tithi name */
  tithi_name: string;
  
  /** The tithi end time */
  tithi_end_time: string;
  
  /** The nakshatra name */
  nakshatra_name: string;
  
  /** The nakshatra end time */
  nakshatra_end_time: string;
  
  /** The yoga name */
  yoga_name: string;
  
  /** The karana name */
  karana_name: string;
  
  /** The chandra rasi */
  chandra_rasi: string;
  
  /** The sunrise time */
  sunrise: string;
  
  /** The sunset time */
  sunset: string;
  
  /** The festivals for this date */
  festivals: string;
  
  /** Whether this date is a holiday */
  is_holiday: number;
  
  /** The created timestamp (optional, likely handled by DB default) */
  created_at?: string;
  
  /** The updated timestamp (optional, likely handled by DB default) */
  updated_at?: string;
  
  /** Whether this date is a marriage date */
  is_marriage_date: number;
  
  /** Whether this date is a brata ghara date */
  is_brata_ghara_date: number;
  
  /** Additional information */
  additional: string;
}
