/**
 * Types for the reminder feature
 */

/**
 * Reminder types
 */
export type ReminderType = 'monthly_tithi' | 'yearly_tithi' | 'specific_date';

/**
 * Notification status
 */
export type NotificationStatus = 'scheduled' | 'delivered' | 'cancelled';

/**
 * User reminder
 */
export interface UserReminder {
  /** The reminder ID */
  id?: number;
  
  /** The reminder title */
  title: string;
  
  /** The reminder description (optional) */
  description?: string;
  
  /** The type of reminder */
  reminderType: ReminderType;
  
  /** The Odia month (for yearly reminders) */
  odiaMonth?: string;
  
  /** The paksha (lunar phase) */
  paksha?: string;
  
  /** The tithi (lunar day) */
  tithi?: string;
  
  /** Whether this reminder is recurring */
  isRecurring: boolean;
  
  /** The recurrence interval (e.g., every 1 month) */
  recurrenceInterval?: number;
  
  /** The time of day for the notification (24h format) */
  notificationTime: string;
  
  /** The name of the notification sound */
  soundName: string;
  
  /** Whether this reminder is enabled */
  isEnabled: boolean;
  
  /** The creation timestamp */
  createdAt?: string;
  
  /** The last update timestamp */
  updatedAt?: string;
}

/**
 * Scheduled notification
 */
export interface ScheduledNotification {
  /** The notification ID */
  id?: number;
  
  /** The associated reminder ID */
  reminderId: number;
  
  /** The notification ID returned by the system */
  notificationId: string;
  
  /** The scheduled date (ISO string) */
  scheduledDate: string;
  
  /** The notification status */
  status: NotificationStatus;
  
  /** The creation timestamp */
  createdAt?: string;
}

/**
 * Next occurrence information
 */
export interface OccurrenceInfo {
  /** The Gregorian date of the occurrence */
  date: Date;
  
  /** The Odia date information */
  odiaDate?: string;
  
  /** The Odia month */
  odiaMonth?: string;
  
  /** The paksha */
  paksha?: string;
  
  /** The tithi */
  tithi?: string;
}

/**
 * Sound option for reminders
 */
export interface SoundOption {
  /** The sound ID */
  id: string;
  
  /** The display name */
  name: string;
  
  /** The sound file path (if custom) */
  filePath?: string;
  
  /** Whether this is the default sound */
  isDefault?: boolean;
}

/**
 * Default sound options
 */
export const DEFAULT_SOUND_OPTIONS: SoundOption[] = [
  { id: 'default', name: 'Default', isDefault: true },
  { id: 'bell', name: 'Bell' },
  { id: 'chime', name: 'Chime' },
  { id: 'temple', name: 'Temple Bell' },
  { id: 'gentle', name: 'Gentle' },
];
