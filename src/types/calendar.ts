/**
 * Calendar data type definitions
 * These types define the structure of calendar-related data throughout the application
 */

/**
 * Represents a day in the Odia calendar
 */
export interface OdiaDate {
  /** The Gregorian date string in YYYY-MM-DD format */
  gregorianDate: string;
  
  /** The Odia date in string format */
  odiaDate: string;
  
  /** The Odia month name */
  month: string;
  
  /** The paksha (lunar phase) - <PERSON><PERSON> or Krishna */
  paksha: string;
  
  /** The tithi (lunar day) */
  tithi: string;
  
  /** Whether this date is a full moon (Purnima) */
  isPurnima: boolean;
  
  /** Whether this date is a new moon (Amavasya) */
  isAmavasya: boolean;
  
  /** List of holidays or festivals on this date */
  holidays: string[];
  
  /** Additional notes for this date (optional) */
  notes?: string;
  
  /** Whether this date is a Brata Ghara date (optional) */
  isBrataGhara?: boolean;
}

/**
 * Represents a festival in the Odia calendar
 */
export interface FestivalInfo {
  /** The date of the festival in YYYY-MM-DD format */
  date: string;
  
  /** The name of the festival */
  name: string;
  
  /** Description of the festival */
  description: string;
  
  /** Whether this is a major festival (optional) */
  isMajor?: boolean;
  
  /** Category of the festival (optional) */
  category?: 'religious' | 'cultural' | 'national' | 'other';
}

/**
 * Represents a marriage date in the Odia calendar
 */
export interface MarriageDate {
  /** The date in YYYY-MM-DD format */
  date: string;
  
  /** The muhurta (auspicious time) for marriage */
  muhurta: string;
  
  /** Whether this date is particularly auspicious */
  isAuspicious: boolean;
  
  /** Additional notes about this marriage date (optional) */
  notes?: string;
}

/**
 * Represents a calendar grid cell
 */
export interface CalendarDay {
  /** The day number (1-31) or null for empty cells */
  day: number | null;
  
  /** The full date string in YYYY-MM-DD format (if day is not null) */
  dateString?: string;
  
  /** Whether this day is today */
  isToday?: boolean;
  
  /** Whether this day has any festivals */
  hasFestival?: boolean;
  
  /** Whether this day is a marriage date */
  isMarriageDate?: boolean;
  
  /** Whether this day is a full moon (Purnima) */
  isPurnima?: boolean;
  
  /** Whether this day is a new moon (Amavasya) */
  isAmavasya?: boolean;
  
  /** Whether this day is a Brata Ghara date */
  isBrataGhara?: boolean;
}

/**
 * Represents a week in the calendar grid
 */
export type CalendarWeek = (number | null)[];

/**
 * Represents the entire calendar grid for a month
 */
export type CalendarGrid = CalendarWeek[];

/**
 * Supported languages in the application
 */
export type SupportedLanguage = 'en' | 'or';

/**
 * Theme mode options
 */
export type ThemeMode = 'light' | 'dark' | 'system';

/**
 * Calendar view mode options
 */
export type CalendarViewMode = 'month' | 'week' | 'day';

/**
 * Calendar data source options
 * This will be used when implementing the database
 */
export type CalendarDataSource = 'local' | 'remote' | 'cache';

/**
 * Calendar sync status
 * This will be used when implementing data synchronization
 */
export interface CalendarSyncStatus {
  /** Whether the calendar data is currently syncing */
  isSyncing: boolean;
  
  /** The last time the calendar was successfully synced */
  lastSyncTime: string | null;
  
  /** Whether there was an error during the last sync attempt */
  hasError: boolean;
  
  /** Error message if there was an error during sync */
  errorMessage?: string;
  
  /** The data source that was last used */
  dataSource: CalendarDataSource;
}
