// Color palette for the app
export const colors = {
  light: {
    primary: '#FF6B6B',
    secondary: '#4ECDC4',
    background: '#FFFFFF',
    card: '#F9F9F9',
    cardBackground: '#F9F9F9', // Added for reminders
    text: '#333333',
    textSecondary: '#666666', // Added for reminders
    subtext: '#666666',
    border: '#EEEEEE',
    highlight: '#FFC8DD',
    today: '#FF6B6B',
    festival: '#FFD166',
    marriage: '#06D6A0',
    brataGhara: '#9B59B6',
    reminder: '#9C27B0',        // Purple for reminders
    reminderBg: '#9C27B010',    // Very light purple background (10% opacity)
    reminderBorder: '#9C27B040', // Light purple border (40% opacity)
    success: '#28a745', // Green for success messages
    error: '#dc3545',   // Red for error messages
    warning: '#ffc107', // Yellow for warnings
    calendarPicker: '#74B9FF', // Added from reference
    // New colors for enhanced UI from reference
    themeDark: '#6C5CE7',
    themeLight: '#FDCB6E',
    language: '#00B894',
    inputBackground: '#F5F5F5', // Added for reminders
    buttonText: '#FFFFFF', // Added for reminders
    primaryLight: '#FFE8E8', // Added for reminders
  },
  dark: {
    primary: '#FF6B6B',
    secondary: '#4ECDC4',
    background: '#121212',
    card: '#1E1E1E',
    cardBackground: '#1E1E1E', // Added for reminders
    text: '#FFFFFF',
    textSecondary: '#AAAAAA', // Added for reminders
    subtext: '#AAAAAA',
    border: '#333333',
    highlight: '#5E4C5A',
    today: '#FF6B6B',
    festival: '#FFD166',
    marriage: '#06D6A0',
    brataGhara: '#9B59B6',
    reminder: '#CE93D8',        // Light purple for dark mode
    reminderBg: '#CE93D815',    // Very light purple background (15% opacity)
    reminderBorder: '#CE93D850', // Light purple border (50% opacity)
    success: '#28a745', // Green for success messages
    error: '#dc3545',   // Red for error messages
    warning: '#ffc107', // Yellow for warnings
    calendarPicker: '#74B9FF', // Added from reference
    // New colors for enhanced UI from reference
    themeDark: '#6C5CE7',
    themeLight: '#FDCB6E',
    language: '#00B894',
    inputBackground: '#2A2A2A', // Added for reminders
    buttonText: '#FFFFFF', // Added for reminders
    primaryLight: '#3A2A2A', // Added for reminders
  }
};

export default colors;
