import { create } from 'zustand';
import { UserReminder, OccurrenceInfo } from '@/types/reminders';
import { reminderService } from '@/services/reminder-service';
import { logger } from '@/utils/logging-sentry';

interface ReminderState {
  // State
  reminders: UserReminder[];
  isLoading: boolean;
  error: string | null;
  selectedReminder: UserReminder | null;
  
  // Actions
  loadReminders: () => Promise<void>;
  createReminder: (reminder: UserReminder) => Promise<UserReminder | null>;
  updateReminder: (reminder: UserReminder) => Promise<boolean>;
  deleteReminder: (id: number) => Promise<boolean>;
  toggleReminder: (id: number, enabled: boolean) => Promise<boolean>;
  selectReminder: (reminder: UserReminder | null) => void;
  getNextOccurrence: (reminder: UserReminder) => Promise<OccurrenceInfo | null>;
}

export const useReminderStore = create<ReminderState>((set, get) => ({
  // Initial state
  reminders: [],
  isLoading: false,
  error: null,
  selectedReminder: null,
  
  // Load all reminders
  loadReminders: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // Initialize the reminder service if needed
      await reminderService.initialize();
      
      // Get all reminders
      const reminders = await reminderService.getAllReminders();
      set({ reminders, isLoading: false });
    } catch (error) {
      logger.error('Error loading reminders', { error });
      set({ 
        error: error instanceof Error ? error.message : 'Failed to load reminders', 
        isLoading: false 
      });
    }
  },
  
  // Create a new reminder
  createReminder: async (reminder: UserReminder) => {
    set({ isLoading: true, error: null });
    
    try {
      const newReminder = await reminderService.createReminder(reminder);
      
      if (newReminder) {
        // Add the new reminder to the state
        set(state => ({ 
          reminders: [newReminder, ...state.reminders],
          isLoading: false 
        }));
        
        return newReminder;
      } else {
        set({ 
          error: 'Failed to create reminder', 
          isLoading: false 
        });
        return null;
      }
    } catch (error) {
      logger.error('Error creating reminder', { error });
      set({ 
        error: error instanceof Error ? error.message : 'Failed to create reminder', 
        isLoading: false 
      });
      return null;
    }
  },
  
  // Update an existing reminder
  updateReminder: async (reminder: UserReminder) => {
    set({ isLoading: true, error: null });
    
    try {
      const success = await reminderService.updateReminder(reminder);
      
      if (success) {
        // Update the reminder in the state
        set(state => ({ 
          reminders: state.reminders.map(r => 
            r.id === reminder.id ? reminder : r
          ),
          isLoading: false 
        }));
        
        return true;
      } else {
        set({ 
          error: 'Failed to update reminder', 
          isLoading: false 
        });
        return false;
      }
    } catch (error) {
      logger.error('Error updating reminder', { error });
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update reminder', 
        isLoading: false 
      });
      return false;
    }
  },
  
  // Delete a reminder
  deleteReminder: async (id: number) => {
    set({ isLoading: true, error: null });
    
    try {
      const success = await reminderService.deleteReminder(id);
      
      if (success) {
        // Remove the reminder from the state
        set(state => ({ 
          reminders: state.reminders.filter(r => r.id !== id),
          isLoading: false 
        }));
        
        return true;
      } else {
        set({ 
          error: 'Failed to delete reminder', 
          isLoading: false 
        });
        return false;
      }
    } catch (error) {
      logger.error('Error deleting reminder', { error });
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete reminder', 
        isLoading: false 
      });
      return false;
    }
  },
  
  // Toggle a reminder's enabled state
  toggleReminder: async (id: number, enabled: boolean) => {
    try {
      const success = await reminderService.toggleReminder(id, enabled);
      
      if (success) {
        // Update the reminder in the state
        set(state => ({ 
          reminders: state.reminders.map(r => 
            r.id === id ? { ...r, isEnabled: enabled } : r
          )
        }));
        
        return true;
      } else {
        return false;
      }
    } catch (error) {
      logger.error('Error toggling reminder', { error });
      return false;
    }
  },
  
  // Select a reminder for viewing/editing
  selectReminder: (reminder: UserReminder | null) => {
    set({ selectedReminder: reminder });
  },
  
  // Get the next occurrence of a reminder
  getNextOccurrence: async (reminder: UserReminder) => {
    try {
      return await reminderService.getNextOccurrence(reminder);
    } catch (error) {
      logger.error('Error getting next occurrence', { error });
      return null;
    }
  }
}));
