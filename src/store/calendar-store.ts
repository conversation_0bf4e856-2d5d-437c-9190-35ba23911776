import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { formatDateString } from '@/utils/date-utils';
import { CalendarState, CalendarSyncStatus } from '@/types'; // Keep existing imports

export const useCalendarStore = create<CalendarState>()(
  persist(
    (set, get) => {
      const today = new Date();

      return {
        // Basic calendar state
        selectedYear: today.getFullYear(),
        selectedMonth: today.getMonth() + 1, // 1-12 instead of 0-11
        selectedDate: null,
        isDateDetailsModalVisible: false,

        // Sync status
        syncStatus: {
          isSyncing: false,
          lastSyncTime: null,
          hasError: false,
          dataSource: 'local'
        },

        // State to track data updates
        lastDataUpdateTime: null,

        // Update prompt state
        isUpdatePending: false,
        showUpdatePrompt: false,

        // Calendar navigation methods
        setSelectedYear: (year) => set({ selectedYear: year }),

        setSelectedMonth: (month) => {
          let newMonth = month;
          let newYear = get().selectedYear;

          if (newMonth > 12) {
            newMonth = 1;
            newYear += 1;
          } else if (newMonth < 1) {
            newMonth = 12;
            newYear -= 1;
          }

          set({ selectedMonth: newMonth, selectedYear: newYear });
        },

        setSelectedDate: (date) => set({ selectedDate: date }),

        setDateDetailsModalVisible: (visible) => set({
          isDateDetailsModalVisible: visible
        }),

        goToToday: () => {
          const today = new Date();
          set({
            selectedYear: today.getFullYear(),
            selectedMonth: today.getMonth() + 1,
            selectedDate: formatDateString(today),
          });
        },

        goToNextMonth: () => {
          const { selectedMonth, selectedYear } = get();
          if (selectedMonth === 12) {
            set({ selectedMonth: 1, selectedYear: selectedYear + 1 });
          } else {
            set({ selectedMonth: selectedMonth + 1 });
          }
        },

        goToPreviousMonth: () => {
          const { selectedMonth, selectedYear } = get();
          if (selectedMonth === 1) {
            set({ selectedMonth: 12, selectedYear: selectedYear - 1 });
          } else {
            set({ selectedMonth: selectedMonth - 1 });
          }
        },

        // Sync status methods
        setSyncStatus: (status) => set({
          syncStatus: { ...get().syncStatus, ...status }
        }),

        // Action to notify UI about data updates
        notifyDataUpdated: () => set({ lastDataUpdateTime: Date.now() }),

        // Update prompt actions
        setIsUpdatePending: (pending) => set({ isUpdatePending: pending }),
        setShowUpdatePrompt: (visible) => set({ showUpdatePrompt: visible }),
      };
    },
    {
      name: 'odia-calendar-state',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist specific parts of the state, or none if desired
      partialize: (state) => {
        // Return an empty object to prevent persisting any part of this store's state
        // This ensures the app always opens with the default state (current month/year)
        return {};
        // Example if you wanted to persist only selectedDate:
        // return { selectedDate: state.selectedDate };
      },
    }
  )
);
