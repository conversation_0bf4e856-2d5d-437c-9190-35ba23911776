import { create } from 'zustand';
import { persist, createJSONStorage, StateStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SettingsState, ThemeMode, SupportedLanguage, DEFAULT_SETTINGS } from '@/types'; // Import DEFAULT_SETTINGS

// Track system dark mode state separately from the store
let isSystemDarkTheme = false;

// Define the shape of the persisted state - ONLY the state values, not actions
interface PersistedSettingsState {
  themeMode: ThemeMode;
  language: SupportedLanguage;
  showAds: boolean;
  errorReportingEnabled: boolean;
  autoUpdateEnabled: boolean;
  autoUpdateWifiOnly: boolean;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      // Initialize state using DEFAULT_SETTINGS
      themeMode: DEFAULT_SETTINGS.themeMode,
      language: DEFAULT_SETTINGS.language,
      showAds: DEFAULT_SETTINGS.showAds,
      errorReportingEnabled: DEFAULT_SETTINGS.errorReportingEnabled,
      autoUpdateEnabled: DEFAULT_SETTINGS.autoUpdateEnabled,
      autoUpdateWifiOnly: DEFAULT_SETTINGS.autoUpdateWifiOnly,

      setThemeMode: (themeMode: ThemeMode) => set({ themeMode }),

      setLanguage: (language: SupportedLanguage) => set({ language }),

      toggleAds: () => set((state) => ({ showAds: !state.showAds })),

      toggleErrorReporting: () => set((state) => ({ errorReportingEnabled: !state.errorReportingEnabled })),

      // Add actions for the new settings
      toggleAutoUpdate: () => set((state) => ({ autoUpdateEnabled: !state.autoUpdateEnabled })),
      toggleWifiOnlyUpdate: () => set((state) => ({ autoUpdateWifiOnly: !state.autoUpdateWifiOnly })),

      resetSettings: () => set({
        themeMode: DEFAULT_SETTINGS.themeMode,
        language: DEFAULT_SETTINGS.language,
        showAds: DEFAULT_SETTINGS.showAds,
        errorReportingEnabled: DEFAULT_SETTINGS.errorReportingEnabled,
        autoUpdateEnabled: DEFAULT_SETTINGS.autoUpdateEnabled,
        autoUpdateWifiOnly: DEFAULT_SETTINGS.autoUpdateWifiOnly,
      }),
    }),
    {
      name: 'odia-calendar-settings',
      storage: createJSONStorage(() => AsyncStorage as StateStorage), // Cast AsyncStorage to StateStorage
      // Only persist the relevant fields
      partialize: (state): PersistedSettingsState => ({
        themeMode: state.themeMode,
        language: state.language,
        showAds: state.showAds,
        errorReportingEnabled: state.errorReportingEnabled,
        autoUpdateEnabled: state.autoUpdateEnabled,
        autoUpdateWifiOnly: state.autoUpdateWifiOnly,
      }),
    }
  )
);

// Helper function to get auto update enabled status
export const useAutoUpdateEnabled = (): boolean => {
  return useSettingsStore((state) => state.autoUpdateEnabled);
};

// Helper function to get wifi only update status
export const useAutoUpdateWifiOnly = (): boolean => {
  return useSettingsStore((state) => state.autoUpdateWifiOnly);
};

// Set system dark mode state - this is used by the root layout
export const setIsSystemDarkTheme = (isDark: boolean): void => {
  isSystemDarkTheme = isDark;
};

// Helper function to determine if dark mode should be used
export const useDarkMode = (): boolean => {
  const { themeMode } = useSettingsStore();

  if (themeMode === 'system') {
    return isSystemDarkTheme;
  }

  return themeMode === 'dark';
};

// Helper function to get the current language
export const useLanguage = (): SupportedLanguage => {
  return useSettingsStore((state) => state.language);
};

/**
 * Helper function to get translated text
 * @returns A function that translates keys to the current language
 */
export const useTranslation = () => {
  const language = useLanguage();

  /**
   * Translates a key to the current language
   * @param key The translation key (can be dot-separated for nested values)
   * @param defaultText The default text to return if the key is not found
   * @returns The translated text
   */
  return (key: string, defaultText: string = ''): string => {
    const parts = key.split('.');
    let result = { ...translations[language] };

    for (const part of parts) {
      if (result && result[part as keyof typeof result] !== undefined) {
        result = result[part as keyof typeof result] as any;
      } else {
        return defaultText || key;
      }
    }

    return (typeof result === 'string' ? result : defaultText || key);
  };
};

// Import translations here to avoid circular dependencies
import { translations } from '@/constants/translations';
