import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet, FlatList } from 'react-native';
import { X, Calendar } from 'lucide-react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { useCalendarStore } from '@/store/calendar-store';
import { getMonthNames } from '@/utils/date-utils';
import { convertToLocalDigits } from '@/utils/language-utils';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { logger } from '@/utils/logging-sentry';

interface MonthYearPickerProps {
  visible: boolean;
  onClose: () => void;
}

const MonthYearPicker: React.FC<MonthYearPickerProps> = ({ visible, onClose }) => {
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const { selectedYear, selectedMonth, setSelectedYear, setSelectedMonth } = useCalendarStore();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Local state for selection
  const [tempYear, setTempYear] = useState(selectedYear);
  const [tempMonth, setTempMonth] = useState(selectedMonth);

  // Create refs for the FlatLists
  const yearListRef = useRef<FlatList>(null);
  const monthListRef = useRef<FlatList>(null);

  // Create a unique key for the FlatList that changes when visibility changes
  // This forces the FlatList to re-render when the modal is opened
  const [yearListKey, setYearListKey] = useState(`year-list-${Date.now()}`);

  // Effect to handle modal visibility changes
  useEffect(() => {
    if (visible) {
      // Reset the year list key to force a re-render
      setYearListKey(`year-list-${Date.now()}`);

      // Reset local state to match the selected values
      setTempYear(selectedYear);
      setTempMonth(selectedMonth);
    }
  }, [visible, selectedYear, selectedMonth]);

  // Generate years (10 years back and 10 years forward) - memoized to prevent recreation on each render
  const years = useMemo(() => {
    const currentYear = new Date().getFullYear();
    return Array.from({ length: 21 }, (_, i) => currentYear - 10 + i);
  }, []);

  // Separate effect to handle scrolling after state is updated
  useEffect(() => {
    if (visible && yearListRef.current && monthListRef.current) {
      // Use a small timeout to ensure the FlatList has rendered
      const timer = setTimeout(() => {
        try {
          // Find the index of the selected year
          const yearIndex = years.findIndex(y => y === selectedYear);
          if (yearIndex !== -1 && yearListRef.current) {
            // Scroll to the selected year
            yearListRef.current.scrollToIndex({
              index: yearIndex,
              animated: false,
              viewPosition: 0.5 // Center the item
            });
            logger.debug('Scrolled year list to index', { yearIndex });
          }

          // Find the index of the selected month
          const monthIndex = selectedMonth - 1;
          if (monthIndex >= 0 && monthListRef.current) {
            // Scroll to the selected month
            monthListRef.current.scrollToIndex({
              index: monthIndex,
              animated: false,
              viewPosition: 0.5 // Center the item
            });
          }
        } catch (error) {
          logger.error('Error scrolling to index in month/year picker', { error });
        }
      }, 100); // Increased timeout for more reliable rendering

      return () => clearTimeout(timer);
    }
  }, [visible, selectedYear, selectedMonth, years]);

  // Get month names based on language - memoized to prevent recalculation on each render
  const monthNames = useMemo(() => {
    return language === 'or'
      ? Object.keys(translations.or)
          .filter(key => ['january', 'february', 'march', 'april', 'may', 'june', 'july',
                          'august', 'september', 'october', 'november', 'december'].includes(key))
          .map(key => translations.or[key as keyof typeof translations.or])
      : getMonthNames(language);
  }, [language]);

  // Apply selection and close
  const handleApply = () => {
    setSelectedYear(tempYear);
    setSelectedMonth(tempMonth);
    onClose();
  };

  // Reset and close
  const handleCancel = () => {
    setTempYear(selectedYear);
    setTempMonth(selectedMonth);
    onClose();
  };

  // Render month item - memoized to prevent recreation on each render
  const renderMonthItem = useCallback(({ item, index }: { item: string; index: number }) => (
    <TouchableOpacity
      style={[
        styles.itemButton,
        tempMonth === index + 1 && { backgroundColor: theme.primary + '30' },
      ]}
      onPress={() => setTempMonth(index + 1)}
    >
      <Text
        style={[
          styles.itemText,
          { color: theme.text },
          tempMonth === index + 1 && { color: theme.primary, fontWeight: 'bold' },
        ]}
      >
        {item}
      </Text>
    </TouchableOpacity>
  ), [tempMonth, theme.primary, theme.text]);

  // Render year item - memoized to prevent recreation on each render
  const renderYearItem = useCallback(({ item }: { item: number }) => {
    // Convert year to Odia digits if language is Odia
    const displayYear = convertToLocalDigits(item, language);

    return (
      <TouchableOpacity
        style={[
          styles.itemButton,
          tempYear === item && { backgroundColor: theme.primary + '30' },
        ]}
        onPress={() => setTempYear(item)}
      >
        <Text
          style={[
            styles.itemText,
            { color: theme.text },
            tempYear === item && { color: theme.primary, fontWeight: 'bold' },
          ]}
        >
          {displayYear}
        </Text>
      </TouchableOpacity>
    );
  }, [tempYear, theme.primary, theme.text, language]);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleCancel}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
          <View style={styles.modalHeader}>
            <View style={styles.headerTitleContainer}>
              <Calendar size={20} color={theme.primary} />
              <Text style={[styles.modalTitle, { color: theme.text }]}>
                {language === 'or' ? 'ମାସ ଏବଂ ବର୍ଷ ଚୟନ କରନ୍ତୁ' : 'Select Month & Year'}
              </Text>
            </View>
            <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
              <X size={24} color={theme.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.pickerContainer}>
            {/* Month Picker */}
            <View style={styles.pickerSection}>
              <Text style={[styles.pickerTitle, { color: theme.subtext }]}>
                {translations[language].month}
              </Text>
              <FlatList
                ref={monthListRef}
                key={`month-list-${visible}`}
                data={monthNames}
                renderItem={renderMonthItem}
                keyExtractor={(_, index) => `month-${index}`}
                showsVerticalScrollIndicator={false}
                style={[styles.pickerList, { backgroundColor: theme.background + '50' }]}
                contentContainerStyle={styles.pickerListContent}
                initialNumToRender={12}
                maxToRenderPerBatch={12}
                windowSize={5}
                removeClippedSubviews={true}
                onScrollToIndexFailed={(info) => {
                  logger.warn('Failed to scroll to month index', { info });
                }}
              />
            </View>

            {/* Year Picker */}
            <View style={styles.pickerSection}>
              <Text style={[styles.pickerTitle, { color: theme.subtext }]}>
                {language === 'or' ? 'ବର୍ଷ' : 'Year'}
              </Text>
              <FlatList
                ref={yearListRef}
                key={yearListKey}
                data={years}
                renderItem={renderYearItem}
                keyExtractor={(item) => `year-${item}`}
                showsVerticalScrollIndicator={false}
                style={[styles.pickerList, { backgroundColor: theme.background + '50' }]}
                contentContainerStyle={styles.pickerListContent}
                getItemLayout={(_, index) => ({
                  length: 44,
                  offset: 44 * index,
                  index,
                })}
                initialNumToRender={21} // Render all years at once
                maxToRenderPerBatch={21}
                windowSize={21}
                removeClippedSubviews={false} // Disable this to ensure all items render
                onScrollToIndexFailed={(info) => {
                  logger.warn('Failed to scroll to year index', { info });
                  // Alternative approach if scrollToIndex fails
                  setTimeout(() => {
                    if (yearListRef.current) {
                      yearListRef.current.scrollToOffset({
                        offset: info.index * 44,
                        animated: false
                      });
                    }
                  }, 100);
                }}
              />
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton, { borderColor: theme.border }]}
              onPress={handleCancel}
            >
              <Text style={[styles.buttonText, { color: theme.text }]}>
                {language === 'or' ? 'ବାତିଲ' : 'Cancel'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.applyButton, { backgroundColor: theme.primary }]}
              onPress={handleApply}
            >
              <Text style={styles.applyButtonText}>
                {language === 'or' ? 'ପ୍ରୟୋଗ କରନ୍ତୁ' : 'Apply'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    overflow: 'hidden',
    paddingTop: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  closeButton: {
    padding: 4,
  },
  pickerContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  pickerSection: {
    flex: 1,
    marginHorizontal: 4,
  },
  pickerTitle: {
    fontSize: 14,
    marginBottom: 8,
    paddingHorizontal: 4,
  },
  pickerList: {
    height: 220,
    borderRadius: 12,
  },
  pickerListContent: {
    paddingVertical: 8,
  },
  itemButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 2,
    marginHorizontal: 4,
  },
  itemText: {
    fontSize: 16,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  cancelButton: {
    borderWidth: 1,
  },
  applyButton: {
    elevation: 2,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  applyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MonthYearPicker;