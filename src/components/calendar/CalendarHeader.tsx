import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ChevronLeft, ChevronRight, Calendar, CalendarRange } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient'; // Import LinearGradient
import { useCalendarStore } from '@/store/calendar-store';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { getMonthName } from '@/utils/date-utils';
import { convertToLocalDigits } from '@/utils/language-utils';
import { translations } from '@/constants/translations';
import MonthYearPicker from './MonthYearPicker';
import colors from '@/constants/colors';

const CalendarHeader: React.FC = () => {
  const { selectedYear, selectedMonth, goToNextMonth, goToPreviousMonth, goToToday } = useCalendarStore();
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const [monthYearPickerVisible, setMonthYearPickerVisible] = useState(false);

  const theme = isDarkMode ? colors.dark : colors.light;

  // Get month name in the current language
  const monthName = language === 'or'
    ? translations.or[getMonthName(selectedMonth).toLowerCase() as keyof typeof translations.or]
    : getMonthName(selectedMonth, language);

  // Convert year to Odia digits if language is Odia
  const displayYear = convertToLocalDigits(selectedYear, language);

  // Open month/year picker
  const openMonthYearPicker = () => {
    setMonthYearPickerVisible(true);
  };

  // Close month/year picker
  const closeMonthYearPicker = () => {
    setMonthYearPickerVisible(false);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.monthYearContainer}
        onPress={openMonthYearPicker}
      >
        <View style={styles.monthYearContent}>
          <Text style={[styles.monthText, { color: theme.text }]}>
            {monthName}
          </Text>
          <Text style={[styles.yearText, { color: theme.subtext }]}>
            {displayYear}
          </Text>
        </View>
        {/* Apply gradient background to icon container */}
        <LinearGradient
          colors={[theme.calendarPicker, theme.calendarPicker + '80']}
          style={styles.calendarIconContainer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <CalendarRange size={16} color="#FFFFFF" />
        </LinearGradient>
      </TouchableOpacity>

      <View style={styles.controlsContainer}>
        {/* Apply gradient background to Today button */}
        <TouchableOpacity
          style={styles.todayButton} // Remove background color style
          onPress={goToToday}
        >
          <LinearGradient
            colors={[theme.primary, theme.primary + '80']}
            style={styles.todayGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Calendar size={16} color="#FFFFFF" />
            <Text style={styles.todayText}> {/* Color set in styles */}
              {translations[language].today}
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        <View style={styles.navigationButtons}>
          {/* Apply gradient background to Nav buttons */}
          <TouchableOpacity
            style={styles.navButton} // Remove background color style
            onPress={goToPreviousMonth}
          >
            <LinearGradient
              colors={[theme.secondary + '90', theme.secondary + '60']}
              style={styles.navGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <ChevronLeft size={20} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton} // Remove background color style
            onPress={goToNextMonth}
          >
            <LinearGradient
              colors={[theme.secondary + '90', theme.secondary + '60']}
              style={styles.navGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <ChevronRight size={20} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>

      <MonthYearPicker
        visible={monthYearPickerVisible}
        onClose={closeMonthYearPicker}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  monthYearContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 4,
    borderRadius: 8,
  },
  // Add style for calendar icon container from reference
  calendarIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  monthYearContent: {
    flexDirection: 'column',
    marginRight: 8,
  },
  monthText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  yearText: {
    fontSize: 16,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  todayButton: {
    // Removed paddingHorizontal from here, moved to gradient
    borderRadius: 20, // Keep the original border radius
    marginRight: 12,
    overflow: 'hidden', // Ensure gradient is clipped
  },
  // Add style for today button gradient from reference
  todayGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  todayText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF', // Set text color to white
  },
  navigationButtons: {
    flexDirection: 'row',
  },
  navButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
    overflow: 'hidden', // Add overflow hidden for gradient rounding
  },
  // Add style for nav button gradient from reference
  navGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CalendarHeader;
