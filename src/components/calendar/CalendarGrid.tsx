import React, { useMemo, useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { generateCalendarGrid, formatDateString, isSameDay } from '@/utils/date-utils';
import { convertToLocalDigits } from '@/utils/language-utils';
import { useCalendarStore } from '@/store/calendar-store';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { useCalendarMonth, useFestivals, useMarriageDates } from '@/hooks/useCalendarData';
import { useMonthReminders } from '@/hooks/useReminderData';

interface CalendarGridProps {
  onDatePress: (date: string) => void;
}

interface DayCellProps {
  day: number | null;
  isToday: boolean;
  // isSelected prop removed
  // isPurnima: boolean; // Property removed as it's not in PanchangData
  // isAmavasya: boolean; // Property removed as it's not in PanchangData
  hasFestival: boolean;
  isMarriageDate: boolean;
  hasReminder: boolean;
  onPress: (day: number | null) => void;
  textColor: string;
  todayColor: string;
  primaryColor: string;
  festivalColor: string;
  marriageColor: string;
  reminderColor: string;
  reminderBg: string;
  reminderBorder: string;
}

// Memoized DayCell component to prevent unnecessary re-renders
const DayCell = React.memo(({
  day,
  isToday,
  // isSelected prop removed
  // isPurnima, // Property removed
  // isAmavasya, // Property removed
  hasFestival,
  isMarriageDate,
  hasReminder,
  onPress,
  textColor,
  todayColor,
  primaryColor,
  festivalColor,
  marriageColor,
  reminderColor,
  reminderBg,
  reminderBorder
}: DayCellProps) => {
  // Get the current language
  const language = useLanguage();

  // Convert day number to Odia digits if language is Odia
  const displayDay = day !== null ? convertToLocalDigits(day, language) : '';

  return (
    <TouchableOpacity
      style={[
        styles.dayCell,
        isToday && { backgroundColor: todayColor + '20' }, // Keep subtle background for today
        // isSelected && { backgroundColor: primaryColor + '30' }, // Remove selected background
        // Hybrid reminder styling: background tint + border
        hasReminder && {
          backgroundColor: reminderBg, // Very subtle purple background
          borderWidth: 1,
          borderColor: reminderBorder // Subtle purple border
        },
      ]}
      onPress={() => onPress(day)}
      disabled={day === null}
    >
      {day !== null && (
        <>
          <Text
            style={[
              styles.dayText,
              // Base text color
              { color: textColor },
              // Style for Today (red color and bold)
              isToday && { color: todayColor, fontWeight: 'bold' },
              // Style for reminder dates (slightly bolder)
              hasReminder && { fontWeight: '600' },
              // Remove selected style
            ]}
          >
            {displayDay}
          </Text>

          {/* Moon phase indicators removed as properties don't exist */}

          {/* Indicators for festivals and marriage dates */}
          <View style={styles.indicators}>
            {hasFestival && (
              <View style={[styles.indicator, { backgroundColor: festivalColor }]} />
            )}
            {isMarriageDate && (
              <View style={[styles.indicator, { backgroundColor: marriageColor }]} />
            )}
          </View>
        </>
      )}
    </TouchableOpacity>
  );
});

const CalendarGrid: React.FC<CalendarGridProps> = ({ onDatePress }) => {
  const { selectedYear, selectedMonth, selectedDate } = useCalendarStore();
  const isDarkMode = useDarkMode();
  const language = useLanguage();

  const today = new Date();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Get calendar data, festivals and marriage dates for the selected month
  const { data: monthData, loading: monthLoading, error: monthError } = useCalendarMonth(selectedYear, selectedMonth);
  const { festivals, loading: festivalsLoading } = useFestivals(selectedYear, selectedMonth);
  const { marriageDates, loading: marriageDatesLoading } = useMarriageDates(selectedYear, selectedMonth);

  // Get reminder data for the selected month
  const { reminderDates, loading: remindersLoading } = useMonthReminders(selectedYear, selectedMonth);

  // Check if core calendar data is still loading
  // Reminder data loading should not block calendar rendering
  const isLoading = monthLoading || festivalsLoading || marriageDatesLoading;

  // Generate calendar grid for the selected month
  const calendarGrid = useMemo(() => {
    return generateCalendarGrid(selectedYear, selectedMonth);
  }, [selectedYear, selectedMonth]);

  // Get day names based on current language
  const dayNames = useMemo(() => {
    const days = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
    return days.map(day => {
      // Type-safe access to translations
      const trans = translations[language];
      return trans[day as keyof typeof trans] || day;
    });
  }, [language]);

  // Check if a date is today - memoized to prevent recalculation on each render
  const isToday = useCallback((day: number | null): boolean => {
    if (day === null) return false;

    const date = new Date(selectedYear, selectedMonth - 1, day);
    return isSameDay(date, today);
  }, [selectedYear, selectedMonth, today]);

  // Removed isSelected callback as it's no longer needed for highlighting

  // Check if a date has a festival - memoized to prevent recalculation on each render
  const hasFestival = useCallback((day: number | null): boolean => {
    if (day === null || !monthData) return false; // Check monthData exists

    const dateStr = formatDateString(new Date(selectedYear, selectedMonth - 1, day));
    // Use monthData directly and check 'festivals' property
    const dateInfo = monthData.find(item => item.eng_date === dateStr);
    return !!(dateInfo && dateInfo.festivals && dateInfo.festivals.trim() !== '');
  }, [selectedYear, selectedMonth, monthData]); // Depend on monthData

  // Check if a date is a marriage date - memoized to prevent recalculation on each render
  const isMarriageDate = useCallback((day: number | null): boolean => {
    if (day === null || !monthData) return false; // Check monthData exists

    const dateStr = formatDateString(new Date(selectedYear, selectedMonth - 1, day));
    // Use monthData directly and check 'is_marriage_date' property
    const dateInfo = monthData.find(item => item.eng_date === dateStr);
    return !!(dateInfo && dateInfo.is_marriage_date === 1);
  }, [selectedYear, selectedMonth, monthData]); // Depend on monthData

  // Check if a date has a reminder - memoized to prevent recalculation on each render
  const hasReminder = useCallback((day: number | null): boolean => {
    if (day === null) return false;
    return reminderDates.has(day);
  }, [reminderDates]);

  // Removed isPurnima and isAmavasya callbacks as properties don't exist in PanchangData

  // Handle date press - memoized with useCallback to prevent unnecessary re-renders
  const handleDatePress = useCallback((day: number | null) => {
    if (day === null) return;

    const dateStr = formatDateString(new Date(selectedYear, selectedMonth - 1, day));
    // useCalendarStore.getState().setSelectedDate(dateStr); // Removed: Don't set persistent selection state
    onDatePress(dateStr); // Only trigger the action to open the modal
  }, [selectedYear, selectedMonth, onDatePress]);

  return (
    <View style={styles.container}>
      {/* Day names row */}
      <View style={styles.dayNamesRow}>
        {dayNames.map((dayName, index) => (
          <View key={index} style={styles.dayNameCell}>
            <Text style={[styles.dayNameText, { color: theme.subtext }]}>
              {dayName}
            </Text>
          </View>
        ))}
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
        </View>
      ) : monthError ? (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.error }]}>
            Failed to load calendar data
          </Text>
        </View>
      ) : (
        /* Calendar grid */
        calendarGrid.map((week, weekIndex) => (
          <View key={weekIndex} style={styles.weekRow}>
            {week.map((day, dayIndex) => (
              <DayCell
                key={dayIndex}
                day={day}
                isToday={isToday(day)}
                // isSelected={isSelected(day)} // Removed
                // isPurnima={isPurnima(day)} // Removed
                // isAmavasya={isAmavasya(day)} // Removed
                hasFestival={hasFestival(day)}
                isMarriageDate={isMarriageDate(day)}
                hasReminder={hasReminder(day)}
                onPress={handleDatePress}
                textColor={theme.text}
                todayColor={theme.today}
                primaryColor={theme.primary}
                festivalColor={theme.festival}
                marriageColor={theme.marriage}
                reminderColor={theme.reminder}
                reminderBg={theme.reminderBg}
                reminderBorder={theme.reminderBorder}
              />
            ))}
          </View>
        ))
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  dayNamesRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  dayNameCell: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  dayNameText: {
    fontSize: 12,
    fontWeight: '600',
  },
  weekRow: {
    flexDirection: 'row',
    marginBottom: 2,
  },
  dayCell: {
    flex: 1,
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    margin: 2,
  },
  dayText: {
    fontSize: 16,
  },
  moonContainer: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  fullMoon: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  newMoon: {
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 1,
  },
  indicators: {
    flexDirection: 'row',
    marginTop: 2,
    height: 4,
  },
  indicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 1,
  },
  loadingContainer: {
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default CalendarGrid;
