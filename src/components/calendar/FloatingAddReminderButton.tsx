import React, { useRef, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Platform,
} from 'react-native';
import { Plus, Bell } from 'lucide-react-native';
import { useDarkMode } from '@/store/settings-store';
import colors from '@/constants/colors';

interface FloatingAddReminderButtonProps {
  onPress: () => void;
}

const FloatingAddReminderButton: React.FC<FloatingAddReminderButtonProps> = ({ onPress }) => {
  const isDarkMode = useDarkMode();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0.7)).current;
  const iconRotateAnim = useRef(new Animated.Value(0)).current;

  // Continuous pulse animation - more subtle for smaller button
  useEffect(() => {
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05, // Smaller pulse for compact design
          duration: 2500, // Slower, more elegant
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2500,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, [pulseAnim]);

  // Continuous glow animation - more subtle and elegant
  useEffect(() => {
    const glowAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnim, {
          toValue: 0.9, // Less intense glow
          duration: 2200, // Slower, more elegant
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0.6, // Softer minimum
          duration: 2200,
          useNativeDriver: true,
        }),
      ])
    );
    glowAnimation.start();

    return () => glowAnimation.stop();
  }, [glowAnim]);

  // Icon rotation animation
  useEffect(() => {
    const iconAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(iconRotateAnim, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(iconRotateAnim, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    );
    iconAnimation.start();

    return () => iconAnimation.stop();
  }, [iconRotateAnim]);

  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 0.85,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 200,
        friction: 4,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '15deg'],
  });

  const iconRotateInterpolate = iconRotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      {/* Outer glow ring */}
      <Animated.View
        style={[
          styles.glowRing,
          {
            opacity: glowAnim,
            transform: [{ scale: pulseAnim }],
            borderColor: theme.primary,
          },
        ]}
      />

      {/* Inner glow ring */}
      <Animated.View
        style={[
          styles.innerGlowRing,
          {
            opacity: Animated.multiply(glowAnim, 0.6),
            transform: [{ scale: Animated.multiply(pulseAnim, 0.95) }],
            backgroundColor: `${theme.primary}30`,
          },
        ]}
      />

      {/* Main button */}
      <Animated.View
        style={[
          styles.buttonContainer,
          {
            transform: [
              { scale: scaleAnim },
              { rotate: rotateInterpolate },
            ],
          },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.button,
            {
              backgroundColor: theme.primary,
              shadowColor: theme.primary,
            },
          ]}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
        >
          {/* Gradient overlay effect */}
          <View
            style={[
              styles.gradientOverlay,
              {
                backgroundColor: isDarkMode
                  ? 'rgba(255,255,255,0.15)'
                  : 'rgba(255,255,255,0.25)',
              },
            ]}
          />

          {/* Bell icon (background) */}
          <Animated.View
            style={[
              styles.bellIcon,
              {
                transform: [{ rotate: iconRotateInterpolate }],
              },
            ]}
          >
            <Bell
              size={16} // Smaller bell icon
              color={`${theme.buttonText}40`}
              strokeWidth={2}
            />
          </Animated.View>

          {/* Plus icon (foreground) */}
          <View style={styles.plusIcon}>
            <Plus
              size={20} // Smaller plus icon
              color={theme.buttonText}
              strokeWidth={2.5} // Slightly thinner stroke
            />
          </View>
        </TouchableOpacity>
      </Animated.View>

      {/* Ripple effect */}
      <Animated.View
        style={[
          styles.ripple,
          {
            backgroundColor: `${theme.primary}15`,
            transform: [{ scale: pulseAnim }],
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 48, // Smaller size
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  glowRing: {
    position: 'absolute',
    width: 54, // Smaller glow ring
    height: 54,
    borderRadius: 27,
    borderWidth: 1.5, // Thinner border
  },
  innerGlowRing: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  buttonContainer: {
    zIndex: 3,
  },
  button: {
    width: 42, // Smaller button
    height: 42,
    borderRadius: 21,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  gradientOverlay: {
    position: 'absolute',
    top: 1,
    left: 1,
    right: 1,
    bottom: 1,
    borderRadius: 20,
  },
  bellIcon: {
    position: 'absolute',
    top: 6,
    right: 6,
  },
  plusIcon: {
    zIndex: 2,
  },
  ripple: {
    position: 'absolute',
    width: 60, // Smaller ripple
    height: 60,
    borderRadius: 30,
    zIndex: 0,
  },
});

export default FloatingAddReminderButton;
