import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { Download, AlertCircle, WifiOff } from 'lucide-react-native';
import { useLanguage, useDarkMode } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { YearAvailabilityStatus } from '@/hooks/useYearAvailabilityStatus'; // Import the type
import { useNetworkStatusContext } from '@/components/providers/NetworkStatusProvider';

interface YearAvailabilityBannerProps {
  status: YearAvailabilityStatus;
  year: number;
  isLoading: boolean; // Loading state specifically for the banner/download action
  onDownloadPress: (yearToDownload: number) => Promise<boolean>; // Accept Promise<boolean>
}

const YearAvailabilityBanner: React.FC<YearAvailabilityBannerProps> = ({
  status,
  year,
  isLoading,
  onDownloadPress,
}) => {
  const language = useLanguage();
  const isDarkMode = useDarkMode();
  const theme = isDarkMode ? colors.dark : colors.light;
  const t = translations[language];
  const { ensureNetworkConnection, showNetworkError } = useNetworkStatusContext();

  const [isDownloading, setIsDownloading] = useState(false); // Internal loading state for button press

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      // First check if network is available
      const isNetworkAvailable = await ensureNetworkConnection();

      if (!isNetworkAvailable) {
        // Show network error toast if offline
        showNetworkError({
          message: t.internetRequiredForYearData.replace('{year}', year.toString()),
          action: {
            label: t.retry,
            onPress: handleDownload
          }
        });

        // Reset internal loading state since we're not proceeding
        setIsDownloading(false);
        return;
      }

      // If network is available, proceed with download
      await onDownloadPress(year);
      // Loading state will be managed by the parent via the isLoading prop changing
      // based on the hook's state after download attempt + status recheck.
    } catch (error) {
      // Error logging should happen within onDownloadPress or the hook
      setIsDownloading(false); // Reset on error
    } finally {
      // We don't reset isDownloading here because the parent's isLoading prop
      // will handle the visual state after the download attempt completes
    }
  };

  const getMessage = () => {
    switch (status) {
      case 'AVAILABLE_REMOTELY':
        return t.yearDataNotDownloaded.replace('{year}', year.toString());
      case 'NOT_AVAILABLE_ANYWHERE':
        return t.yearDataNotAvailable.replace('{year}', year.toString());
      case 'ERROR_CHECKING_AVAILABILITY':
         return t.error; // Generic error message
      default:
        return null; // Don't show banner for other statuses
    }
  };

  const message = getMessage();

  if (!message || status === 'AVAILABLE_LOCALLY' || status === 'CHECKING_AVAILABILITY') {
    return null; // Don't render the banner if status doesn't require it
  }

  const isError = status === 'ERROR_CHECKING_AVAILABILITY';
  const showDownloadButton = status === 'AVAILABLE_REMOTELY';
  const bannerBackgroundColor = isError ? theme.card : theme.card; // Use card for background
  const bannerTextColor = isError ? theme.error : theme.subtext; // Use subtext for normal, error for error
  const bannerIconColor = isError ? theme.error : theme.subtext; // Match text color
  const buttonTextColor = '#FFFFFF'; // Use white for text on primary button

  return (
    <View style={[styles.banner, { backgroundColor: bannerBackgroundColor }]}>
      <AlertCircle size={20} color={bannerIconColor} style={styles.icon} />
      <Text style={[styles.message, { color: bannerTextColor }]}>
        {isLoading ? t.downloadingYearData.replace('{year}', year.toString()) : message}
      </Text>
      {showDownloadButton && !isLoading && (
        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.primary }]}
          onPress={handleDownload}
          disabled={isDownloading} // Disable button during internal download state
        >
          {isDownloading ? (
            <ActivityIndicator size="small" color={buttonTextColor} />
          ) : (
            <>
              <Download size={16} color={buttonTextColor} style={styles.buttonIcon} />
              <Text style={[styles.buttonText, { color: buttonTextColor }]}>
                {t.downloadYearData.replace('{year}', year.toString())}
              </Text>
            </>
          )}
        </TouchableOpacity>
      )}
       {/* Show loading indicator based on the main isLoading prop */}
       {isLoading && !showDownloadButton && (
         <ActivityIndicator size="small" color={bannerTextColor} style={styles.loadingIndicator} />
       )}
    </View>
  );
};

const styles = StyleSheet.create({
  banner: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginTop: 10,
    borderRadius: 8,
    elevation: 1, // for Android shadow
    shadowColor: '#000', // for iOS shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  icon: {
    marginRight: 10,
  },
  message: {
    flex: 1,
    fontSize: 14,
    marginRight: 10, // Add margin between text and button/indicator
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  buttonIcon: {
    marginRight: 6,
  },
  buttonText: {
    fontSize: 13,
    fontWeight: '600',
  },
  loadingIndicator: {
    marginLeft: 'auto', // Push indicator to the right if no button
  },
});

export default YearAvailabilityBanner;
