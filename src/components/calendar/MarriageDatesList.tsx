import React, { useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
import { Heart } from 'lucide-react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { useCalendarStore } from '@/store/calendar-store';
import { formatDateWithLocalDigits } from '@/utils/language-utils';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { useMarriageDates } from '@/hooks/useCalendarData';
import { PanchangData } from '@/types/database';

const MarriageDatesList: React.FC = () => {
  const { selectedYear, selectedMonth } = useCalendarStore();
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Get marriage dates for the selected month using the database hook
  const { marriageDates, loading, error } = useMarriageDates(selectedYear, selectedMonth);

  // Format date for display - memoized to prevent recreation on each render
  // NOTE: This is only needed for mock data. When using real database data,
  // the formatted date should come directly from the database in the correct language
  const formatDate = useCallback((dateStr: string) => {
    // For mock data, we need to format the date
    const date = new Date(dateStr);
    return formatDateWithLocalDigits(date, language, {
      day: 'numeric',
      month: 'short',
    });

    // When using real database data, we would use something like:
    // return language === 'or' ? item.odiaFormattedDate : item.englishFormattedDate;
  }, [language]);

  // Define the MarriageDateItemProps interface for better type safety
  interface MarriageDateItemProps {
    item: PanchangData;
    isDarkMode: boolean;
    theme: typeof colors.light | typeof colors.dark;
    formatDate: (dateStr: string) => string;
    language: 'en' | 'or';
  }

  // Memoized Marriage Date Item component with theme props
  const MarriageDateItem = React.memo(({ item, isDarkMode, theme, formatDate, language }: MarriageDateItemProps) => {
    const formattedDate = formatDate(item.eng_date);
    const muhurtaLabel = language === 'or' ? 'ମୁହୂର୍ତ୍ତ' : 'Muhurta';
    const auspiciousText = language === 'or' ? 'ଶୁଭ' : 'Auspicious';

    // Extract muhurta from additional field or use a default
    const muhurta = item.additional || (language === 'or' ? 'ଶୁଭ ମୁହୂର୍ତ୍ତ' : 'Auspicious Time');

    // Check if it's an auspicious date (for now, assume all marriage dates are auspicious)
    const isAuspicious = true;
    // Use theme.card for background in dark mode for better contrast
    const itemBackgroundColor = isDarkMode ? theme.card : theme.marriage + '20';

    return (
      <View
        style={[
          styles.marriageItem,
          {
            backgroundColor: itemBackgroundColor,
            // Add border in dark mode for better visibility
            borderWidth: isDarkMode ? 1 : 0,
            borderColor: isDarkMode ? theme.marriage : 'transparent'
          }
        ]}
      >
        <View style={[styles.marriageDateContainer, { backgroundColor: theme.marriage }]}>
          <Text style={styles.marriageDateText}>{formattedDate}</Text>
        </View>
        <View style={styles.marriageContent}>
          <Text style={[styles.marriageMuhurtaLabel, { color: theme.subtext }]}>
            {muhurtaLabel}
          </Text>
          <Text
            style={[
              styles.marriageMuhurtaText,
              {
                color: theme.text,
                // Enhance text visibility in dark mode
                fontWeight: isDarkMode ? '700' : '600',
                // Add slight shadow in dark mode for better contrast
                textShadowColor: isDarkMode ? 'rgba(0, 0, 0, 0.75)' : 'transparent',
                textShadowOffset: { width: 0, height: 1 },
                textShadowRadius: isDarkMode ? 2 : 0
              }
            ]}
          >
            {muhurta}
          </Text>
          {isAuspicious && (
            <View style={[styles.auspiciousTag, { backgroundColor: theme.marriage }]}>
              <Text style={styles.auspiciousTagText}>
                {auspiciousText}
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  });

  // Render marriage date item - memoized to prevent recreation on each render
  // Include isDarkMode, theme, formatDate, and language in dependencies to ensure it updates when theme changes
  const renderMarriageDateItem = useCallback(({ item }: { item: PanchangData }) => {
    return <MarriageDateItem
      item={item}
      isDarkMode={isDarkMode}
      theme={theme}
      formatDate={formatDate}
      language={language}
    />;
  }, [isDarkMode, theme, formatDate, language]);

  // Header component for reuse
  const Header = () => (
    <View style={styles.headerContainer}>
      <Heart size={20} color={theme.primary} />
      <Text style={[styles.headerText, { color: theme.text }]}>
        {translations[language].marriageDates}
      </Text>
    </View>
  );

  // Show loading state
  if (loading) {
    return (
      <View style={styles.container}>
        <Header />
        <View style={[styles.emptyContainer, { borderColor: theme.border }]}>
          <ActivityIndicator size="small" color={theme.primary} />
          <Text style={[styles.emptyText, { color: theme.subtext }]}>
            {language === 'or' ? 'ବିବାହ ତାରିଖ ଲୋଡ୍ ହେଉଛି...' : 'Loading marriage dates...'}
          </Text>
        </View>
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={styles.container}>
        <Header />
        <View style={[styles.emptyContainer, { borderColor: 'red' }]}>
          <Text style={[styles.emptyText, { color: 'red' }]}>
            {language === 'or' ? 'ବିବାହ ତାରିଖ ଲୋଡ୍ କରିବାରେ ତ୍ରୁଟି' : 'Error loading marriage dates'}
          </Text>
        </View>
      </View>
    );
  }

  // If no marriage dates, show empty state
  if (marriageDates.length === 0) {
    return (
      <View style={styles.container}>
        <Header />
        <View style={[styles.emptyContainer, { borderColor: theme.border }]}>
          <Text style={[styles.emptyText, { color: theme.subtext }]}>
            {language === 'or' ? 'ଏହି ମାସରେ କୌଣସି ବିବାହ ତାରିଖ ନାହିଁ' : 'No marriage dates this month'}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header />
      <FlatList
        data={marriageDates}
        renderItem={renderMarriageDateItem}
        keyExtractor={(item) => item.eng_date}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.marriagesList}
        initialNumToRender={3}
        maxToRenderPerBatch={3}
        windowSize={3}
        removeClippedSubviews={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    marginBottom: 24,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  marriagesList: {
    paddingHorizontal: 8,
  },
  marriageItem: {
    width: 200,
    borderRadius: 12,
    marginHorizontal: 8,
    overflow: 'hidden',
  },
  marriageDateContainer: {
    paddingVertical: 8,
    alignItems: 'center',
  },
  marriageDateText: {
    color: 'white',
    fontWeight: '600',
  },
  marriageContent: {
    padding: 12,
  },
  marriageMuhurtaLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  marriageMuhurtaText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  auspiciousTag: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  auspiciousTagText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  emptyContainer: {
    marginHorizontal: 16,
    paddingVertical: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 14,
  },
});

export default MarriageDatesList;
