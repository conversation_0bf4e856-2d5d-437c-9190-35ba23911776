import React, { useMemo, useCallback, useEffect } from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet, ScrollView, ActivityIndicator } from 'react-native';
import { X, Sunrise, Sunset, Star, Moon, CalendarDays, Sparkles, Heart, Home, Calendar as CalendarIcon, Bell } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { formatDateWithLocalDigits, convertToLocalDigits } from '@/utils/language-utils';
import { translations } from '@/constants/translations';
import BannerAd from '@/components/common/BannerAd';
import { useAdMob } from '@/components/common/AdMobProvider';
import colors from '@/constants/colors';
import { useDateDetails } from '@/hooks/useCalendarData';
import { useDateReminders } from '@/hooks/useReminderData';

interface DateDetailsModalProps {
  visible: boolean;
  dateString: string | null;
  onClose: () => void;
}

interface HolidayItemProps {
  holiday: string;
  festivalColor: string;
  textColor: string;
}

// Memoized HolidayItem component to prevent unnecessary re-renders
const HolidayItem = React.memo(({ holiday, festivalColor, textColor }: HolidayItemProps) => {
  // Split potential multi-line festivals
  const festivalLines = holiday.split('\n').map(line => line.trim()).filter(line => line);
  return (
    <>
      {festivalLines.map((line, index) => (
        <View key={index} style={styles.holidayItem}>
          <View style={[styles.holidayDot, { backgroundColor: festivalColor }]} />
          <Text style={[styles.holidayText, { color: textColor }]}>
            {line}
          </Text>
        </View>
      ))}
    </>
  );
});

// Enhanced Detail Row Component
interface DetailRowProps {
  label: string;
  value: string | number | null | undefined;
  icon?: React.ReactNode; // Optional icon
  theme: typeof colors.light | typeof colors.dark;
  language: 'en' | 'or';
  formatOdia?: boolean; // Flag to format numbers/times as Odia digits
  highlight?: boolean; // Flag to highlight important information
  iconColor?: string; // Optional custom icon color
}

const DetailRow: React.FC<DetailRowProps> = React.memo(({
  label,
  value,
  icon,
  theme,
  language,
  formatOdia = false,
  highlight = false,
  iconColor
}) => {
  if (value === null || value === undefined || String(value).trim() === '') {
    return null; // Don't render if value is empty
  }

  const displayValue = formatOdia && language === 'or' ? convertToLocalDigits(String(value), language) : String(value);
  const iconColorToUse = iconColor || theme.subtext;

  return (
    <View style={[
      styles.detailRow,
      highlight && styles.highlightedRow
    ]}>
      {icon && (
        <View style={styles.detailIcon}>
          {React.cloneElement(icon as React.ReactElement, { color: iconColorToUse })}
        </View>
      )}
      <Text style={[
        styles.detailLabel,
        { color: theme.subtext },
        !icon && styles.detailLabelNoIcon,
        highlight && { fontWeight: '600', color: theme.text }
      ]}>
        {label}:
      </Text>
      <Text
        style={[
          styles.detailValue,
          { color: theme.text },
          highlight && { fontWeight: '600' }
        ]}
        numberOfLines={2}
        ellipsizeMode="tail"
      >
        {displayValue}
      </Text>
    </View>
  );
});


const DateDetailsModal: React.FC<DateDetailsModalProps> = ({ visible, dateString, onClose }) => {
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const theme = isDarkMode ? colors.dark : colors.light;
  const t = translations[language]; // Translation helper
  const { showInterstitial, trackPlacementInteraction } = useAdMob();

  // Get date details from the database
  const { data: dateInfo, loading, error } = useDateDetails(dateString);

  // Get reminder details for this date
  const { reminders: dateReminders, loading: remindersLoading } = useDateReminders(dateString);

  // Track modal interactions for interstitial ad frequency
  useEffect(() => {
    if (visible) {
      // Track that the modal was opened, but don't show an ad yet
      trackPlacementInteraction('dateDetails');
    }
  }, [visible, trackPlacementInteraction]);

  // Enhanced close handler with interstitial ad
  const handleClose = useCallback(async () => {
    // Try to show an interstitial ad when closing the modal
    // The frequency service will determine if it's time to show an ad
    // based on the counter (every 3 interactions)
    await showInterstitial('dateDetails');

    // Then close the modal
    onClose();
  }, [onClose, showInterstitial]);

  // Format the date for display in the header - memoized to prevent recreation on each render
  const formattedHeaderDate = useMemo(() => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return formatDateWithLocalDigits(date, language, {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  }, [dateString, language]);



  // Create a loading modal with enhanced UI
  const renderLoadingModal = () => (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
          {/* Enhanced header with gradient background */}
          <LinearGradient
            colors={[theme.primary, theme.primary + '80']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.modalHeaderGradient}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: 'white' }]}>
                {translations[language].dateDetails}
              </Text>
              <TouchableOpacity onPress={handleClose} style={styles.closeButtonEnhanced}>
                <X size={20} color="white" />
              </TouchableOpacity>
            </View>
          </LinearGradient>

          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={[styles.loadingText, { color: theme.subtext }]}>
              {language === 'or' ? 'ତାରିଖ ବିବରଣୀ ଲୋଡ୍ ହେଉଛି...' : 'Loading date details...'}
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.closeModalButton, { backgroundColor: theme.primary }]}
            onPress={handleClose}
          >
            <Text style={styles.closeModalButtonText}>
              {translations[language].close}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  // Create an error modal with enhanced UI
  const renderErrorModal = () => (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
          {/* Enhanced header with gradient background */}
          <LinearGradient
            colors={[theme.primary, theme.primary + '80']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.modalHeaderGradient}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: 'white' }]}>
                {translations[language].dateDetails}
              </Text>
              <TouchableOpacity onPress={handleClose} style={styles.closeButtonEnhanced}>
                <X size={20} color="white" />
              </TouchableOpacity>
            </View>
          </LinearGradient>

          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: theme.error }]}>
              {language === 'or' ? 'ତାରିଖ ବିବରଣୀ ଲୋଡ୍ କରିବାରେ ତ୍ରୁଟି' : 'Error loading date details'}
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.closeModalButton, { backgroundColor: theme.primary }]}
            onPress={handleClose}
          >
            <Text style={styles.closeModalButtonText}>
              {translations[language].close}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  if (loading) {
    return renderLoadingModal();
  }

  if (error || !dateInfo) {
    return renderErrorModal();
  }



  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
          {/* Enhanced header with gradient background and date display */}
          <LinearGradient
            colors={[theme.primary, theme.primary + '80']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.modalHeaderGradient}
          >
            <View style={styles.modalHeader}>
              <View style={styles.headerContent}>
                <Text style={styles.modalTitle}>
                  {translations[language].dateDetails}
                </Text>
                <Text style={styles.headerDate}>{formattedHeaderDate}</Text>
              </View>
              <TouchableOpacity onPress={handleClose} style={styles.closeButtonEnhanced}>
                <X size={20} color="white" />
              </TouchableOpacity>
            </View>
          </LinearGradient>

          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            {/* --- Combined Odia Date and Panchang Information Card --- */}
            <View style={[styles.sectionCard, { backgroundColor: theme.card }]}>
              <View style={styles.sectionCardHeader}>
                <CalendarIcon size={18} color={theme.primary} />
                <Text style={[styles.sectionCardTitle, { color: theme.primary }]}>
                  {language === 'or' ? 'ଓଡ଼ିଆ ତାରିଖ ଓ ପଞ୍ଚାଙ୍ଗ' : 'Odia Date & Panchang'}
                </Text>
              </View>
              <View style={styles.sectionCardContent}>
                {/* Day name */}
                <DetailRow
                  label={t.day}
                  value={dateInfo.day_name}
                  theme={theme}
                  language={language}
                  highlight={true}
                  icon={<CalendarIcon size={16} />}
                  iconColor={theme.primary}
                />

                {/* Odia date and month */}
                <DetailRow
                  label={t.odiaDate}
                  value={dateInfo.odia_date}
                  theme={theme}
                  language={language}
                  formatOdia={true}
                  highlight={true}
                  icon={<CalendarIcon size={16} />}
                  iconColor={theme.primary}
                />
                <DetailRow
                  label={t.odiaMonth}
                  value={dateInfo.odia_month}
                  theme={theme}
                  language={language}
                  highlight={true}
                  icon={<CalendarIcon size={16} />}
                  iconColor={theme.primary}
                />
                <DetailRow
                  label={t.odiaYear}
                  value={dateInfo.odia_year}
                  theme={theme}
                  language={language}
                  formatOdia={true}
                  highlight={true}
                  icon={<CalendarIcon size={16} />}
                  iconColor={theme.primary}
                />

                {/* Panchang details */}
                <DetailRow
                  label={t.paksha}
                  value={dateInfo.paksha}
                  theme={theme}
                  language={language}
                  icon={<Moon size={16} />}
                  iconColor={theme.primary}
                  highlight={true}
                />
                <DetailRow
                  label={t.tithi}
                  value={dateInfo.tithi_end_time ? `${dateInfo.tithi_name} (${t.tithiEnds} ${dateInfo.tithi_end_time})` : dateInfo.tithi_name}
                  theme={theme}
                  language={language}
                  formatOdia={true}
                  icon={<Star size={16} />}
                  iconColor={theme.primary}
                  highlight={true}
                />
                <DetailRow
                  label={t.nakshatra}
                  value={dateInfo.nakshatra_end_time ? `${dateInfo.nakshatra_name} (${t.nakshatraEnds} ${dateInfo.nakshatra_end_time})` : dateInfo.nakshatra_name}
                  theme={theme}
                  language={language}
                  formatOdia={true}
                  icon={<Star size={16} />}
                  iconColor={theme.primary}
                  highlight={true}
                />
                <DetailRow
                  label={t.yoga}
                  value={dateInfo.yoga_name}
                  theme={theme}
                  language={language}
                  icon={<Sparkles size={16} />}
                  iconColor={theme.primary}
                  highlight={true}
                />
                <DetailRow
                  label={t.karana}
                  value={dateInfo.karana_name}
                  theme={theme}
                  language={language}
                  icon={<Sparkles size={16} />}
                  iconColor={theme.primary}
                  highlight={true}
                />
                <DetailRow
                  label={t.chandraRasi}
                  value={dateInfo.chandra_rasi}
                  theme={theme}
                  language={language}
                  icon={<Moon size={16} />}
                  iconColor={theme.primary}
                  highlight={true}
                />

                {/* Marriage and Brata Ghara eligibility */}
                {dateInfo.is_marriage_date !== undefined && (
                  <DetailRow
                    label={t.suitableForMarriage}
                    value={dateInfo.is_marriage_date === 1 ? (language === 'or' ? 'ହଁ' : 'Yes') : (language === 'or' ? 'ନା' : 'No')}
                    theme={theme}
                    language={language}
                    icon={<Heart size={16} />}
                    iconColor={dateInfo.is_marriage_date === 1 ? theme.marriage : theme.error}
                    highlight={dateInfo.is_marriage_date === 1}
                  />
                )}

                {dateInfo.is_brata_ghara_date !== undefined && (
                  <DetailRow
                    label={t.suitableForBrataGhara}
                    value={dateInfo.is_brata_ghara_date === 1 ? (language === 'or' ? 'ହଁ' : 'Yes') : (language === 'or' ? 'ନା' : 'No')}
                    theme={theme}
                    language={language}
                    icon={<Home size={16} />}
                    iconColor={dateInfo.is_brata_ghara_date === 1 ? theme.brataGhara : theme.error}
                    highlight={dateInfo.is_brata_ghara_date === 1}
                  />
                )}
              </View>
            </View>

            {/* Banner Ad - Moved here */}
            <View style={styles.adSeparator}>
              <BannerAd placement="dateDetailsModal" />
            </View>

            {/* --- Sun Timings Card --- */}
            <View style={[styles.sectionCard, { backgroundColor: theme.card }]}>
              <View style={styles.sectionCardHeader}>
                <Sunrise size={18} color={theme.primary} />
                <Text style={[styles.sectionCardTitle, { color: theme.primary }]}>
                  {t.sunTimings}
                </Text>
              </View>
              <View style={styles.sectionCardContent}>
                <DetailRow
                  label={t.sunrise}
                  value={dateInfo.sunrise}
                  icon={<Sunrise size={16} />}
                  theme={theme}
                  language={language}
                  formatOdia={true}
                  iconColor={theme.primary}
                  highlight={true}
                />
                <DetailRow
                  label={t.sunset}
                  value={dateInfo.sunset}
                  icon={<Sunset size={16} />}
                  theme={theme}
                  language={language}
                  formatOdia={true}
                  iconColor={theme.primary}
                  highlight={true}
                />
              </View>
            </View>

            {/* --- Festivals Card --- */}
            {dateInfo.festivals && dateInfo.festivals.trim() !== '' ? (
              <View style={[styles.sectionCard, { backgroundColor: theme.card }]}>
                <View style={styles.sectionCardHeader}>
                  <CalendarDays size={18} color={theme.primary} />
                  <Text style={[styles.sectionCardTitle, { color: theme.primary }]}>
                    {t.festivalsAndEvents}
                  </Text>
                </View>
                <View style={styles.sectionCardContent}>
                  {dateInfo.festivals.split(',').map((festival, index) => (
                    <HolidayItem
                      key={`fest-${index}`}
                      holiday={festival.trim()}
                      festivalColor={theme.festival}
                      textColor={theme.text}
                    />
                  ))}
                </View>
              </View>
            ) : null}

            {/* --- Reminders Card --- */}
            {dateReminders && dateReminders.length > 0 ? (
              <View style={[styles.sectionCard, { backgroundColor: theme.card }]}>
                <View style={styles.sectionCardHeader}>
                  <Bell size={18} color={theme.reminder} />
                  <Text style={[styles.sectionCardTitle, { color: theme.reminder }]}>
                    {language === 'or' ? 'ସ୍ମରଣ' : 'Reminders'}
                  </Text>
                  {remindersLoading && (
                    <ActivityIndicator size="small" color={theme.reminder} style={{ marginLeft: 8 }} />
                  )}
                </View>
                <View style={styles.sectionCardContent}>
                  {dateReminders.map((reminder, index) => (
                    <View key={`reminder-${reminder.id || index}`} style={styles.reminderItem}>
                      <View style={styles.reminderIconContainer}>
                        <Bell size={16} color={theme.reminder} />
                      </View>
                      <View style={styles.reminderContent}>
                        <Text style={[styles.reminderTitle, { color: theme.text }]}>
                          {reminder.title}
                        </Text>
                        {reminder.description && (
                          <Text style={[styles.reminderDescription, { color: theme.subtext }]}>
                            {reminder.description}
                          </Text>
                        )}
                        <View style={styles.reminderDetails}>
                          <Text style={[styles.reminderTime, { color: theme.subtext }]}>
                            {language === 'or' ? 'ସମୟ: ' : 'Time: '}
                            {convertToLocalDigits(reminder.notificationTime || '00:00', language)}
                          </Text>
                          {reminder.soundName && (
                            <Text style={[styles.reminderSound, { color: theme.subtext }]}>
                              {language === 'or' ? 'ଧ୍ୱନି: ' : 'Sound: '}
                              {reminder.soundName}
                            </Text>
                          )}
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              </View>
            ) : null}
          </ScrollView>

          <TouchableOpacity
            style={[styles.closeModalButton, { backgroundColor: theme.primary }]}
            onPress={handleClose}
          >
            <Text style={styles.closeModalButtonText}>
              {translations[language].close}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 30,
    maxHeight: '90%', // Increased from 80% to show more content
  },
  // Enhanced header styles
  modalHeaderGradient: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  headerDate: {
    fontSize: 14,
    color: 'white',
    opacity: 0.9,
    marginTop: 4,
  },
  closeButton: {
    padding: 4,
  },
  closeButtonEnhanced: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBody: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  // Card-based section styles
  sectionCard: {
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionCardContent: {
    paddingLeft: 4,
  },
  // Special indicator styles
  specialIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  specialIndicatorIcon: {
    marginRight: 12,
  },
  specialIndicatorText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  // Legacy styles kept for compatibility
  dateSection: {
    marginBottom: 16,
  },
  dateSectionTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  dateSectionValue: {
    fontSize: 18,
    fontWeight: '500',
  },
  tithiHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  moonPhaseContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fullMoon: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  newMoon: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 1.5,
    marginRight: 4,
  },
  moonPhaseText: {
    fontSize: 12,
  },
  holidayItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  holidayDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  holidayText: {
    fontSize: 16,
  },
  closeModalButton: {
    marginHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
  },
  closeModalButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  // Legacy section styles
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionContent: {
    paddingBottom: 10,
    marginBottom: 10,
    borderBottomWidth: 1,
  },

  // Enhanced DetailRow styles
  detailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  highlightedRow: {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 6,
    paddingHorizontal: 8,
  },
  detailIcon: {
    marginRight: 12,
    marginTop: 2,
    width: 20,
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    flexBasis: '40%',
    marginRight: 8,
  },
  detailLabelNoIcon: {
    marginLeft: 0,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  eventItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    paddingVertical: 4,
  },
  eventIcon: {
    marginRight: 8,
  },
  eventText: {
    fontSize: 14,
    flex: 1,
  },
  adSeparator: {
    marginVertical: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 8,
  },
  // Reminder styles
  reminderItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginVertical: 4,
    backgroundColor: 'rgba(156, 39, 176, 0.05)',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#9C27B0',
  },
  reminderIconContainer: {
    marginRight: 12,
    marginTop: 2,
    width: 20,
    alignItems: 'center',
  },
  reminderContent: {
    flex: 1,
  },
  reminderTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 4,
  },
  reminderDescription: {
    fontSize: 13,
    marginBottom: 6,
    lineHeight: 18,
  },
  reminderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reminderTime: {
    fontSize: 12,
    fontWeight: '500',
  },
  reminderSound: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});

export default DateDetailsModal;
