import React, { useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
import { Calendar } from 'lucide-react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { useCalendarStore } from '@/store/calendar-store';
import { formatDateWithLocalDigits } from '@/utils/language-utils';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { useFestivals } from '@/hooks/useCalendarData';
import { PanchangData } from '@/types/database';

const FestivalsList: React.FC = () => {
  const { selectedYear, selectedMonth } = useCalendarStore();
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Get festivals for the selected month using the database hook
  const { festivals, loading, error } = useFestivals(selectedYear, selectedMonth);

  // Format date for display - memoized to prevent recreation on each render
  // NOTE: This is only needed for mock data. When using real database data,
  // the formatted date should come directly from the database in the correct language
  const formatDate = useCallback((dateStr: string) => {
    // For mock data, we need to format the date
    const date = new Date(dateStr);
    return formatDateWithLocalDigits(date, language, {
      day: 'numeric',
      month: 'short',
    });

    // When using real database data, we would use something like:
    // return language === 'or' ? item.odiaFormattedDate : item.englishFormattedDate;
  }, [language]);

  // Define the FestivalItemProps interface for better type safety
  interface FestivalItemProps {
    item: PanchangData;
    isDarkMode: boolean;
    theme: typeof colors.light | typeof colors.dark;
    formatDate: (dateStr: string) => string;
  }

  // Memoized Festival Item component with theme props
  const FestivalItem = React.memo(({ item, isDarkMode, theme, formatDate }: FestivalItemProps) => {
    const formattedDate = formatDate(item.eng_date);
    const festivalNames = item.festivals.split(',').map(f => f.trim());
    // Use a more visible background color in dark mode for better contrast
    const itemBackgroundColor = isDarkMode ? theme.card : theme.festival + '20';

    return (
      <View
        style={[
          styles.festivalItem,
          {
            backgroundColor: itemBackgroundColor,
            // Add border in dark mode for better visibility
            borderWidth: isDarkMode ? 1 : 0,
            borderColor: isDarkMode ? theme.festival : 'transparent'
          }
        ]}
      >
        <View style={[styles.festivalDateContainer, { backgroundColor: theme.festival }]}>
          <Text style={styles.festivalDateText}>{formattedDate}</Text>
        </View>
        <View style={styles.festivalContent}>
          {festivalNames.map((festival, index) => (
            <Text
              key={index}
              style={[
                styles.festivalName,
                {
                  color: theme.text,
                  // Enhance text visibility in dark mode
                  fontWeight: isDarkMode ? '700' : '600',
                  // Add slight shadow in dark mode for better contrast
                  textShadowColor: isDarkMode ? 'rgba(0, 0, 0, 0.75)' : 'transparent',
                  textShadowOffset: { width: 0, height: 1 },
                  textShadowRadius: isDarkMode ? 2 : 0
                }
              ]}
            >
              {festival}
            </Text>
          ))}
        </View>
      </View>
    );
  });

  // Render festival item - memoized to prevent recreation on each render
  // Include isDarkMode, theme, and formatDate in dependencies to ensure it updates when theme changes
  const renderFestivalItem = useCallback(({ item }: { item: PanchangData }) => {
    return <FestivalItem
      item={item}
      isDarkMode={isDarkMode}
      theme={theme}
      formatDate={formatDate}
    />;
  }, [isDarkMode, theme, formatDate]);

  // Header component for reuse
  const Header = () => (
    <View style={styles.headerContainer}>
      <Calendar size={20} color={theme.primary} />
      <Text style={[styles.headerText, { color: theme.text }]}>
        {translations[language].festivals}
      </Text>
    </View>
  );

  // Show loading state
  if (loading) {
    return (
      <View style={styles.container}>
        <Header />
        <View style={[styles.emptyContainer, { borderColor: theme.border }]}>
          <ActivityIndicator size="small" color={theme.primary} />
          <Text style={[styles.emptyText, { color: theme.subtext }]}>
            {language === 'or' ? 'ପର୍ବପର୍ବାଣୀ ଲୋଡ୍ ହେଉଛି...' : 'Loading festivals...'}
          </Text>
        </View>
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={styles.container}>
        <Header />
        <View style={[styles.emptyContainer, { borderColor: 'red' }]}>
          <Text style={[styles.emptyText, { color: 'red' }]}>
            {language === 'or' ? 'ପର୍ବପର୍ବାଣୀ ଲୋଡ୍ କରିବାରେ ତ୍ରୁଟି' : 'Error loading festivals'}
          </Text>
        </View>
      </View>
    );
  }

  // If no festivals, show empty state
  if (festivals.length === 0) {
    return (
      <View style={styles.container}>
        <Header />
        <View style={[styles.emptyContainer, { borderColor: theme.border }]}>
          <Text style={[styles.emptyText, { color: theme.subtext }]}>
            {language === 'or' ? 'ଏହି ମାସରେ କୌଣସି ପର୍ବପର୍ବାଣୀ ନାହିଁ' : 'No festivals this month'}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Calendar size={20} color={theme.primary} />
        <Text style={[styles.headerText, { color: theme.text }]}>
          {translations[language].festivals}
        </Text>
      </View>
      <FlatList
        data={festivals}
        renderItem={renderFestivalItem}
        keyExtractor={(item) => item.eng_date + item.festivals}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.festivalsList}
        initialNumToRender={3}
        maxToRenderPerBatch={3}
        windowSize={3}
        removeClippedSubviews={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  festivalsList: {
    paddingHorizontal: 8,
  },
  festivalItem: {
    width: 250,
    borderRadius: 12,
    marginHorizontal: 8,
    overflow: 'hidden',
  },
  festivalDateContainer: {
    paddingVertical: 8,
    alignItems: 'center',
  },
  festivalDateText: {
    color: 'white',
    fontWeight: '600',
  },
  festivalContent: {
    padding: 12,
  },
  festivalName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  festivalDescription: {
    fontSize: 14,
  },
  emptyContainer: {
    marginHorizontal: 16,
    paddingVertical: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 14,
  },
});

export default FestivalsList;
