import React, { useCallback, useRef, useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
  interpolate,
} from 'react-native-reanimated';
import { useCalendarStore } from '@/store/calendar-store';
import { useAdMob } from '@/components/common/AdMobProvider';
import CalendarHeader from './CalendarHeader';
import CalendarGrid from './CalendarGrid';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const SWIPE_THRESHOLD = SCREEN_WIDTH * 0.2;

interface SwipeableCalendarViewProps {
  onDatePress: (date: string) => void;
  dateDetailsVisible?: boolean;
}

const SwipeableCalendarView: React.FC<SwipeableCalendarViewProps> = ({
  onDatePress,
  dateDetailsVisible = false
}) => {
  const { selectedYear, goToNextMonth, goToPreviousMonth } = useCalendarStore();
  const { showInterstitial } = useAdMob();
  const previousYear = useRef(selectedYear);

  // Animated values
  const translateX = useSharedValue(0);
  const isSwiping = useSharedValue(false);
  const swipeDirection = useSharedValue<'left' | 'right' | null>(null);

  // Track year changes for interstitial ads
  useEffect(() => {
    const handleYearChange = async () => {
      // Check if the year has changed
      if (selectedYear !== previousYear.current) {
        // Show interstitial ad when year changes
        await showInterstitial('yearChange');
        previousYear.current = selectedYear;
      }
    };

    handleYearChange();
  }, [selectedYear, showInterstitial]);

  // Function to handle month change - this runs on JS thread
  const handleMonthChange = useCallback((direction: 'next' | 'previous') => {
    if (direction === 'next') {
      goToNextMonth();
    } else {
      goToPreviousMonth();
    }

    // Reset animation state after changing month
    translateX.value = 0;
    isSwiping.value = false;
  }, [goToNextMonth, goToPreviousMonth, translateX, isSwiping]);

  // Pan gesture handler
  const panGesture = Gesture.Pan()
    .enabled(!dateDetailsVisible) // Disable gesture when modal is visible
    .onStart(() => {
      if (isSwiping.value) return;
      isSwiping.value = true;
    })
    .onUpdate((event) => {
      // Only handle horizontal swipes (with some tolerance for diagonal movement)
      if (Math.abs(event.translationX) > Math.abs(event.translationY * 2)) {
        translateX.value = event.translationX;
      }
    })
    .onEnd((event) => {
      // Use velocity to make swipes feel more responsive
      const hasHighVelocity = Math.abs(event.velocityX) > 500;

      if (event.translationX > SWIPE_THRESHOLD || (event.translationX > 0 && hasHighVelocity)) {
        // Swiped right - go to previous month
        swipeDirection.value = 'right';
        translateX.value = withTiming(SCREEN_WIDTH, { duration: 250 }, () => {
          runOnJS(handleMonthChange)('previous');
        });
      } else if (event.translationX < -SWIPE_THRESHOLD || (event.translationX < 0 && hasHighVelocity)) {
        // Swiped left - go to next month
        swipeDirection.value = 'left';
        translateX.value = withTiming(-SCREEN_WIDTH, { duration: 250 }, () => {
          runOnJS(handleMonthChange)('next');
        });
      } else {
        // Not enough to trigger month change, reset position
        translateX.value = withTiming(0, { duration: 150 }, () => {
          isSwiping.value = false;
        });
      }
    });

  // Animated styles for the calendar container
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  // Animated styles for the next month (appearing from right)
  const nextMonthStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      translateX.value,
      [0, -SCREEN_WIDTH],
      [0, 1]
    );

    const translateValue = interpolate(
      translateX.value,
      [0, -SCREEN_WIDTH],
      [SCREEN_WIDTH, 0]
    );

    return {
      opacity,
      transform: [{ translateX: translateValue }],
      position: 'absolute',
      width: '100%',
      height: '100%',
    };
  });

  // Animated styles for the previous month (appearing from left)
  const prevMonthStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      translateX.value,
      [0, SCREEN_WIDTH],
      [0, 1]
    );

    const translateValue = interpolate(
      translateX.value,
      [0, SCREEN_WIDTH],
      [-SCREEN_WIDTH, 0]
    );

    return {
      opacity,
      transform: [{ translateX: translateValue }],
      position: 'absolute',
      width: '100%',
      height: '100%',
    };
  });

  return (
    <View style={styles.container}>
      <CalendarHeader />
      <GestureDetector gesture={panGesture}>
        <View style={styles.calendarContainer}>
          {/* Current month */}
          <Animated.View style={[styles.calendarContent, animatedStyle]}>
            <CalendarGrid onDatePress={onDatePress} />
          </Animated.View>

          {/* Next month placeholder (appears when swiping left) */}
          <Animated.View style={[styles.calendarContent, nextMonthStyle]}>
            <View style={styles.placeholderContainer} />
          </Animated.View>

          {/* Previous month placeholder (appears when swiping right) */}
          <Animated.View style={[styles.calendarContent, prevMonthStyle]}>
            <View style={styles.placeholderContainer} />
          </Animated.View>
        </View>
      </GestureDetector>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  calendarContainer: {
    position: 'relative',
    width: '100%',
    overflow: 'hidden',
    minHeight: 350, // Ensure container has enough height for the calendar grid
  },
  calendarContent: {
    width: '100%',
  },
  placeholderContainer: {
    width: '100%',
    height: 350, // Match the height of the calendar grid
  },
});

export default SwipeableCalendarView;
