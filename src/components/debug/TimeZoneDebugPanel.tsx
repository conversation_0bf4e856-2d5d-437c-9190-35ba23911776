/**
 * Debug panel for time zone testing
 * This component provides a UI for testing time zone handling
 * Only available in development builds
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { testTimeZoneUtils, testReminderDateCalculation } from '@/utils/time-zone-test';
import { getCurrentISTDateString, getISTDateString } from '@/utils/time-zone-utils';
import { logger } from '@/utils/logging-sentry';

interface TimeZoneDebugPanelProps {
  visible?: boolean;
}

const TimeZoneDebugPanel: React.FC<TimeZoneDebugPanelProps> = ({ visible = true }) => {
  const [results, setResults] = useState<string[]>([]);

  // Skip rendering if not visible or not in development
  if (!visible || !__DEV__) {
    return null;
  }

  const runTimeZoneTests = () => {
    setResults(prev => [...prev, '--- Running Time Zone Tests ---']);

    try {
      // Run tests and get results directly
      const testResults = testTimeZoneUtils();

      // Update results
      setResults(prev => [...prev, ...testResults]);
    } catch (error) {
      setResults(prev => [...prev, `Error: ${error instanceof Error ? error.message : String(error)}`]);
    }
  };

  const runReminderTests = () => {
    setResults(prev => [...prev, '--- Running Reminder Date Tests ---']);

    try {
      // Run tests and get results directly
      const testResults = testReminderDateCalculation();

      // Update results
      setResults(prev => [...prev, ...testResults]);
    } catch (error) {
      setResults(prev => [...prev, `Error: ${error instanceof Error ? error.message : String(error)}`]);
    }
  };

  const showCurrentTime = () => {
    const now = new Date();
    const istDateString = getCurrentISTDateString();

    setResults(prev => [
      ...prev,
      '--- Current Time Info ---',
      `Local Time: ${now.toString()}`,
      `ISO String: ${now.toISOString()}`,
      `IST Date: ${istDateString}`,
      `Time Zone Offset: ${now.getTimezoneOffset()} minutes`,
      `Time Zone: ${Intl.DateTimeFormat().resolvedOptions().timeZone}`
    ]);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>⏰ Time Zone Debug Panel</Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={runTimeZoneTests}>
          <Text style={styles.buttonText}>Run Time Zone Tests</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={runReminderTests}>
          <Text style={styles.buttonText}>Test Reminder Dates</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={showCurrentTime}>
          <Text style={styles.buttonText}>Show Current Time</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <Text key={index} style={styles.resultText}>{result}</Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    margin: 10,
    maxHeight: 400,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 8,
    borderRadius: 4,
    margin: 4,
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
  },
  resultsContainer: {
    maxHeight: 300,
    backgroundColor: '#000',
    padding: 8,
    borderRadius: 4,
  },
  resultText: {
    color: '#00FF00',
    fontSize: 10,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
});

export default TimeZoneDebugPanel;
