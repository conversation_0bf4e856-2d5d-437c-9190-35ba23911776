/**
 * SQLQueryExecutor component for the debug panel
 *
 * This component provides a UI for executing SQL queries against the app's SQLite database.
 * It is ONLY available in development builds and should never be included in production.
 */

import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, Platform } from 'react-native';
import { databaseService } from '@/services/database-service';
import { useComponentLogger } from '@/utils/logging-sentry';

// Props for the SQLQueryExecutor component
interface SQLQueryExecutorProps {
  // No props needed for now
}

/**
 * SQLQueryExecutor component
 *
 * Provides a text input for entering SQL queries and an Execute button
 * Results are logged to the console, not displayed in the UI
 */
const SQLQueryExecutor: React.FC<SQLQueryExecutorProps> = () => {
  const logger = useComponentLogger('SQLQueryExecutor');
  const [query, setQuery] = useState<string>('');
  const [isExecuting, setIsExecuting] = useState<boolean>(false);

  /**
   * Execute the SQL query
   */
  const executeQuery = async () => {
    if (!query.trim()) {
      Alert.alert('Error', 'Please enter a SQL query');
      return;
    }

    setIsExecuting(true);

    try {
      logger.info('Executing SQL query from debug panel', { query });

      // Execute the query
      const results = await databaseService.executeQuery(query);

      // Log the results to the console
      console.log('SQL Query Results:');
      console.log(JSON.stringify(results, null, 2));

      // Show a success message
      Alert.alert(
        'Query Executed',
        `Query executed successfully. ${results.length} rows returned. Results logged to console.`
      );
    } catch (error) {
      // Log the error
      logger.error('Error executing SQL query from debug panel', { error, query });

      // Show an error message
      Alert.alert(
        'Query Error',
        `Error executing query: ${error instanceof Error ? error.message : String(error)}`
      );
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>SQL Query Executor</Text>
      <Text style={styles.warning}>⚠️ DEVELOPMENT ONLY</Text>

      <TextInput
        style={styles.queryInput}
        placeholder="Enter SQL query..."
        placeholderTextColor="#999"
        value={query}
        onChangeText={setQuery}
        multiline
        numberOfLines={3}
        autoCapitalize="none"
        autoCorrect={false}
      />

      <TouchableOpacity
        style={[
          styles.executeButton,
          isExecuting && styles.executingButton
        ]}
        onPress={executeQuery}
        disabled={isExecuting}
      >
        <Text style={styles.executeButtonText}>
          {isExecuting ? 'Executing...' : 'Execute Query'}
        </Text>
      </TouchableOpacity>

      <Text style={styles.note}>
        Results will be logged to the console, not displayed here.
      </Text>
    </View>
  );
};

// Styles for the SQLQueryExecutor component
const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#ccc',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  warning: {
    color: '#FF3B30',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  queryInput: {
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    marginBottom: 8,
    color: '#333',
    fontSize: 12,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  executeButton: {
    backgroundColor: '#007AFF',
    padding: 8,
    borderRadius: 4,
    alignItems: 'center',
  },
  executingButton: {
    backgroundColor: '#999',
  },
  executeButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  note: {
    fontSize: 10,
    color: '#666',
    marginTop: 8,
    fontStyle: 'italic',
  },
});

export default SQLQueryExecutor;
