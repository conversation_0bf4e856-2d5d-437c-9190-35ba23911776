import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Platform } from 'react-native';
import { Bell, Settings } from 'lucide-react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { notificationService } from '@/services/notification-service';
import { logger } from '@/utils/logging-sentry';

interface NotificationPermissionAlertProps {
  onPermissionChange?: (granted: boolean) => void;
  showOnlyIfDenied?: boolean;
}

/**
 * A component that handles notification permission requests and alerts
 *
 * This component will:
 * 1. Check if notification permissions are granted
 * 2. If not granted, show a modal asking the user to enable notifications
 * 3. If the user has previously denied permissions, show a different message with a link to settings
 *
 * @param onPermissionChange Optional callback when permission status changes
 * @param showOnlyIfDenied Only show the alert if permissions are denied (not if they're just not determined yet)
 */
const NotificationPermissionAlert: React.FC<NotificationPermissionAlertProps> = ({
  onPermissionChange,
  showOnlyIfDenied = false,
}) => {
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const t = translations[language];
  const theme = isDarkMode ? colors.dark : colors.light;

  const [showModal, setShowModal] = useState(false);
  const [permissionDenied, setPermissionDenied] = useState(false);

  // Check permissions when component mounts
  useEffect(() => {
    checkPermissions();
  }, []);

  // Check if notification permissions are granted
  const checkPermissions = useCallback(async () => {
    try {
      const permissionStatus = await notificationService.getPermissionStatus();

      if (permissionStatus) {
        const isGranted = permissionStatus.granted;
        setPermissionDenied(!isGranted);

        // Notify parent component if callback provided
        if (onPermissionChange) {
          onPermissionChange(isGranted);
        }

        // Show modal if permissions are not granted and we're not in "only if denied" mode
        // or if permissions are explicitly denied and we are in "only if denied" mode
        if ((!isGranted && !showOnlyIfDenied) ||
            (!isGranted && showOnlyIfDenied && permissionStatus.status?.status === 'denied')) {
          setShowModal(true);
        }
      }
    } catch (error) {
      logger.error('Error checking notification permissions', { error });
    }
  }, [onPermissionChange, showOnlyIfDenied]);

  const handleRequestPermission = async () => {
    try {
      await notificationService.requestPermissions();
      await checkPermissions();
      setShowModal(false);
    } catch (error) {
      logger.error('Error requesting notification permissions', { error });
    }
  };

  const openSettings = async () => {
    try {
      await notificationService.openNotificationSettings();
      setShowModal(false);
    } catch (error) {
      logger.error('Error opening settings', { error });
    }
  };

  // Don't render anything if permissions are granted
  if (!permissionDenied) {
    return null;
  }

  return (
    <Modal
      visible={showModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.cardBackground }]}>
          <View style={styles.iconContainer}>
            <Bell color={theme.primary} size={32} />
          </View>

          <Text style={[styles.title, { color: theme.text }]}>
            {t.notificationPermissionRequired}
          </Text>

          <Text style={[styles.message, { color: theme.textSecondary }]}>
            {t.notificationPermissionMessage}
          </Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton, { borderColor: theme.border }]}
              onPress={() => setShowModal(false)}
            >
              <Text style={[styles.buttonText, { color: theme.textSecondary }]}>
                {t.later}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.primaryButton, { backgroundColor: theme.primary }]}
              onPress={handleRequestPermission}
            >
              <Text style={[styles.buttonText, styles.primaryButtonText]}>
                {t.allowNotifications}
              </Text>
            </TouchableOpacity>
          </View>

          {Platform.OS !== 'web' && (
            <TouchableOpacity style={styles.settingsButton} onPress={openSettings}>
              <Settings size={16} color={theme.primary} />
              <Text style={[styles.settingsText, { color: theme.primary }]}>
                {t.openSettings}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );

};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 6,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  primaryButton: {
    borderWidth: 0,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  primaryButtonText: {
    color: 'white',
  },
  settingsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  settingsText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default NotificationPermissionAlert;
