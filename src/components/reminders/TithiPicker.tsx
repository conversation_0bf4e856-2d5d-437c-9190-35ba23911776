import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, FlatList } from 'react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { ChevronDown, Check } from 'lucide-react-native';

interface PickerOption {
  value: string;
  label: string;
}

interface TithiPickerProps {
  label: string;
  value: string | undefined;
  options: PickerOption[];
  onValueChange: (value: string) => void;
  placeholder?: string;
}

const TithiPicker: React.FC<TithiPickerProps> = ({
  label,
  value,
  options,
  onValueChange,
  placeholder = 'Select...'
}) => {
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const t = translations[language];
  const theme = isDarkMode ? colors.dark : colors.light;

  const [modalVisible, setModalVisible] = useState(false);

  // Get the selected option label
  const selectedLabel = value
    ? options.find(option => option.value === value)?.label || value
    : placeholder;

  // Handle option selection
  const handleSelect = useCallback((option: PickerOption) => {
    onValueChange(option.value);
    setModalVisible(false);
  }, [onValueChange]);

  // Render option item
  const renderItem = useCallback(({ item }: { item: PickerOption }) => {
    const isSelected = item.value === value;

    return (
      <TouchableOpacity
        style={[
          styles.optionItem,
          isSelected && { backgroundColor: theme.primaryLight }
        ]}
        onPress={() => handleSelect(item)}
      >
        <Text style={[styles.optionText, { color: theme.text }]}>
          {item.label}
        </Text>

        {isSelected && (
          <Check size={20} color={theme.primary} />
        )}
      </TouchableOpacity>
    );
  }, [value, theme, handleSelect]);

  return (
    <View style={styles.container}>
      <Text style={[styles.label, { color: theme.text }]}>
        {label}
      </Text>

      <TouchableOpacity
        style={[
          styles.pickerButton,
          {
            backgroundColor: theme.inputBackground,
            borderColor: theme.border
          }
        ]}
        onPress={() => setModalVisible(true)}
      >
        <Text style={[
          styles.pickerText,
          { color: value ? theme.text : theme.textSecondary }
        ]}>
          {selectedLabel}
        </Text>

        <ChevronDown size={20} color={theme.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[
            styles.modalContent,
            { backgroundColor: theme.cardBackground }
          ]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.text }]}>
                {label}
              </Text>

              <TouchableOpacity
                style={[styles.closeButton, { backgroundColor: theme.border }]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={[styles.closeButtonText, { color: theme.text }]}>
                  {t.close}
                </Text>
              </TouchableOpacity>
            </View>

            <FlatList
              data={options}
              keyExtractor={(item) => item.value}
              renderItem={renderItem}
              contentContainerStyle={styles.optionsList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 48,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  pickerText: {
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  closeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionsList: {
    paddingBottom: 24,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  optionText: {
    fontSize: 16,
  },
});

export default React.memo(TithiPicker);
