import React, { useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useReminderStore } from '@/store/reminder-store';
import { UserReminder } from '@/types/reminders';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import ReminderItem from './ReminderItem';
import { Plus } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import BannerAd from '@/components/common/BannerAd';

// Define styles first so they can be used in components
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerAddButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  inlineAdContainer: {
    marginVertical: 8,
  },
});

// Create a separate component for the ad to avoid conditional hook calls
const InlineAdComponent = React.memo(() => (
  <View style={styles.inlineAdContainer}>
    <BannerAd placement="remindersScreen" />
  </View>
));

const RemindersList: React.FC = () => {
  const { reminders, isLoading, error, loadReminders, selectReminder } = useReminderStore();
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const t = translations[language];
  const theme = isDarkMode ? colors.dark : colors.light;
  const router = useRouter();

  // Load reminders when the component mounts
  useEffect(() => {
    loadReminders();
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    loadReminders();
  }, [loadReminders]);

  // Handle reminder press
  const handleReminderPress = useCallback((reminder: UserReminder) => {
    selectReminder(reminder);
    router.push('/reminder-details');
  }, [selectReminder, router]);

  // Handle add button press
  const handleAddPress = useCallback(() => {
    selectReminder(null); // Clear selected reminder
    router.push('/add-reminder');
  }, [selectReminder, router]);

  // Render empty state
  const renderEmptyState = useCallback(() => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
          <Text style={[styles.emptyText, { color: theme.textSecondary }]}>
            {t.loading}
          </Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: theme.error }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.primary }]}
            onPress={handleRefresh}
          >
            <Text style={[styles.retryButtonText, { color: theme.buttonText }]}>
              {t.retry}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyTitle, { color: theme.text }]}>
          {t.noReminders}
        </Text>
        <Text style={[styles.emptyText, { color: theme.textSecondary }]}>
          {t.addYourFirst}
        </Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.primary }]}
          onPress={handleAddPress}
        >
          <Plus size={20} color={theme.buttonText} />
          <Text style={[styles.addButtonText, { color: theme.buttonText }]}>
            {t.addReminder}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }, [isLoading, error, theme, t, handleRefresh, handleAddPress]);

  // Render list header
  const renderListHeader = useCallback(() => {
    return (
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.text }]}>
          {t.yourReminders}
        </Text>
        <TouchableOpacity
          style={[styles.headerAddButton, { backgroundColor: theme.primary }]}
          onPress={handleAddPress}
        >
          <Plus size={20} color={theme.buttonText} />
        </TouchableOpacity>
      </View>
    );
  }, [theme, t, handleAddPress]);

  // Create a function to render items with an ad in the middle - defined before any conditional returns
  const renderItem = useCallback(({ item, index }: { item: UserReminder; index: number }) => {
    // If we're at the middle of the list, render an ad after this item
    const showAdAfter = reminders.length > 2 && index === Math.floor(reminders.length / 2) - 1;

    return (
      <>
        <ReminderItem
          reminder={item}
          onPress={handleReminderPress}
        />
        {showAdAfter ? <InlineAdComponent /> : null}
      </>
    );
  }, [reminders.length, handleReminderPress]);

  // If there are no reminders, show the empty state
  if (reminders.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        {renderEmptyState()}
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <FlatList
        data={reminders}
        keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
        renderItem={renderItem}
        contentContainerStyle={styles.listContent}
        refreshing={isLoading}
        onRefresh={handleRefresh}
        ListHeaderComponent={renderListHeader}
        ListFooterComponent={() => (
          <View style={{ height: 70 }} /> // Add padding at the bottom for the sticky ad
        )}
      />
    </View>
  );
};

export default RemindersList;
