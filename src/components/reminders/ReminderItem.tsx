import React, { useCallback, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch, Alert } from 'react-native';
import { useReminderStore } from '@/store/reminder-store';
import { UserReminder, OccurrenceInfo } from '@/types/reminders';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { Bell, Calendar, Trash2 } from 'lucide-react-native';
// Import the date formatter
import { formatDate } from '../../utils/date-formatter';

interface ReminderItemProps {
  reminder: UserReminder;
  onPress: (reminder: UserReminder) => void;
}

const ReminderItem: React.FC<ReminderItemProps> = ({ reminder, onPress }) => {
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const t = translations[language];
  const theme = isDarkMode ? colors.dark : colors.light;

  const { toggleReminder, deleteReminder, getNextOccurrence } = useReminderStore();
  const [nextOccurrence, setNextOccurrence] = useState<OccurrenceInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load the next occurrence when the component mounts
  React.useEffect(() => {
    loadNextOccurrence();
  }, [reminder]);

  // Load the next occurrence of this reminder
  const loadNextOccurrence = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    const occurrence = await getNextOccurrence(reminder);
    setNextOccurrence(occurrence);
    setIsLoading(false);
  }, [reminder, getNextOccurrence]);

  // Handle toggle switch
  const handleToggle = useCallback(async (value: boolean) => {
    await toggleReminder(reminder.id!, value);
  }, [reminder.id, toggleReminder]);

  // Handle delete button
  const handleDelete = useCallback(() => {
    Alert.alert(
      t.deleteTitle,
      t.deleteConfirm,
      [
        {
          text: language === 'or' ? t.commonCancel : 'Cancel',
          style: 'cancel'
        },
        {
          text: language === 'or' ? t.commonDelete : 'Delete',
          style: 'destructive',
          onPress: async () => {
            await deleteReminder(reminder.id!);
          }
        }
      ]
    );
  }, [reminder.id, deleteReminder, t]);

  // Get the reminder type text
  const getReminderTypeText = useCallback(() => {
    switch (reminder.reminderType) {
      case 'monthly_tithi':
        return t.monthlyTithi;
      case 'yearly_tithi':
        return t.yearlyTithi;
      case 'specific_date':
        return t.specificDate;
      default:
        return '';
    }
  }, [reminder.reminderType, t]);

  // Get the reminder details text
  const getReminderDetailsText = useCallback(() => {
    if (reminder.reminderType === 'monthly_tithi') {
      return `${reminder.paksha} ${reminder.tithi}`;
    } else if (reminder.reminderType === 'yearly_tithi') {
      return `${reminder.odiaMonth}, ${reminder.paksha} ${reminder.tithi}`;
    } else {
      return '';
    }
  }, [reminder]);

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: theme.cardBackground }
      ]}
      onPress={() => onPress(reminder)}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Bell size={24} color={reminder.isEnabled ? theme.primary : theme.textSecondary} />
        </View>

        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: theme.text }]} numberOfLines={1}>
            {reminder.title}
          </Text>

          <Text style={[styles.details, { color: theme.textSecondary }]}>
            {getReminderTypeText()} • {getReminderDetailsText()}
          </Text>

          {nextOccurrence && (
            <View style={styles.nextOccurrence}>
              <Calendar size={14} color={theme.textSecondary} />
              <Text style={[styles.nextOccurrenceText, { color: theme.textSecondary }]}>
                {formatDate(nextOccurrence.date)}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.actions}>
          <Switch
            value={reminder.isEnabled}
            onValueChange={handleToggle}
            trackColor={{ false: theme.border, true: theme.primary }}
            thumbColor={theme.background}
          />

          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDelete}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            <Trash2 size={18} color={theme.error} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  content: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  details: {
    fontSize: 14,
    marginBottom: 4,
  },
  nextOccurrence: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextOccurrenceText: {
    fontSize: 12,
    marginLeft: 4,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    marginLeft: 16,
    padding: 4,
  },
});

export default React.memo(ReminderItem);
