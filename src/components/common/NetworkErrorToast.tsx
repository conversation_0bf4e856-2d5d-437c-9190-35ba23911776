import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import colors from '@/constants/colors';
import { WifiOff, X } from 'lucide-react-native';
import { useComponentLogger } from '@/utils/logging-sentry';

interface NetworkErrorToastProps {
  visible: boolean;
  message?: string;
  onDismiss?: () => void;
  autoHideDuration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
}

/**
 * A lightweight toast component for displaying network error messages
 */
const NetworkErrorToast: React.FC<NetworkErrorToastProps> = ({
  visible,
  message,
  onDismiss,
  autoHideDuration = 5000, // Default 5 seconds
  action,
}) => {
  const logger = useComponentLogger('NetworkErrorToast');
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const theme = isDarkMode ? colors.dark : colors.light;
  
  const [animation] = useState(new Animated.Value(0));
  const [localVisible, setLocalVisible] = useState(false);

  // Default messages based on language
  const defaultMessage = language === 'or' 
    ? 'ଇଣ୍ଟରନେଟ୍ ସଂଯୋଗ ନାହିଁ'
    : 'No internet connection';

  useEffect(() => {
    if (visible && !localVisible) {
      setLocalVisible(true);
      Animated.timing(animation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Auto-hide after duration if onDismiss is provided
      if (onDismiss && autoHideDuration > 0) {
        const timer = setTimeout(() => {
          handleDismiss();
        }, autoHideDuration);
        return () => clearTimeout(timer);
      }
    } else if (!visible && localVisible) {
      handleDismiss();
    }
  }, [visible, localVisible]);

  const handleDismiss = () => {
    Animated.timing(animation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setLocalVisible(false);
      if (onDismiss) onDismiss();
    });
  };

  if (!localVisible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: theme.card,
          transform: [
            {
              translateY: animation.interpolate({
                inputRange: [0, 1],
                outputRange: [-100, 0],
              }),
            },
          ],
          opacity: animation,
        },
      ]}
    >
      <View style={styles.content}>
        <WifiOff size={20} color={theme.error} style={styles.icon} />
        <Text style={[styles.message, { color: theme.text }]}>
          {message || defaultMessage}
        </Text>
      </View>
      
      <View style={styles.actions}>
        {action && (
          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => {
              action.onPress();
              logger.debug('Network toast action pressed');
            }}
          >
            <Text style={[styles.actionText, { color: theme.primary }]}>
              {action.label}
            </Text>
          </TouchableOpacity>
        )}
        
        {onDismiss && (
          <TouchableOpacity style={styles.closeButton} onPress={handleDismiss}>
            <X size={18} color={theme.subtext} />
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1000,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  message: {
    fontSize: 14,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    marginRight: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
});

export default NetworkErrorToast;
