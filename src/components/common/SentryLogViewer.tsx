/**
 * Simple LogViewer component for development
 *
 * This is a lightweight version of the LogViewer that works with our Sentry-integrated logging system.
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { LogLevel } from '@/utils/logging-sentry';
import * as Sentry from '@sentry/react-native';

// Simple in-memory log storage for development
const logStore: Array<{
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
}> = [];

// Export a function to add logs to the store
export function addLogToStore(level: LogLevel, message: string, context?: Record<string, any>): void {
  if (__DEV__) {
    try {
      // Safely stringify context to avoid circular references
      let safeContext = context;
      if (context) {
        try {
          // Test if context can be stringified
          JSON.stringify(context);
        } catch (error) {
          // If not, create a simplified version
          safeContext = Object.keys(context).reduce((acc, key) => {
            try {
              const value = context[key];
              acc[key] = typeof value === 'object' ? '[Complex Object]' : String(value);
            } catch (e) {
              acc[key] = '[Error: Cannot stringify]';
            }
            return acc;
          }, {} as Record<string, any>);
        }
      }

      // Add to log store
      logStore.push({
        timestamp: new Date().toISOString(),
        level,
        message,
        context: safeContext,
      });

      // Keep only the last 100 logs using efficient array method
      if (logStore.length > 100) {
        // This is more efficient than shift() for large arrays
        logStore.splice(0, logStore.length - 100);
      }
    } catch (error) {
      // Silently fail - we don't want logging to cause issues
      console.warn('Error adding log to store:', error);
    }
  }
}

/**
 * Props for the LogViewer component
 */
interface LogViewerProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * LogViewer component
 */
const SentryLogViewer: React.FC<LogViewerProps> = ({
  visible,
  onClose,
}) => {
  const [filter, setFilter] = useState<LogLevel>(LogLevel.DEBUG);

  // Get color for log level
  const getLogLevelColor = useCallback((level: LogLevel): string => {
    switch (level) {
      case LogLevel.DEBUG:
        return '#6c757d'; // Gray
      case LogLevel.INFO:
        return '#0d6efd'; // Blue
      case LogLevel.WARN:
        return '#ffc107'; // Yellow
      case LogLevel.ERROR:
        return '#dc3545'; // Red
      default:
        return '#000000'; // Black
    }
  }, []);

  // Get log level name
  const getLogLevelName = useCallback((level: LogLevel): string => {
    switch (level) {
      case LogLevel.DEBUG:
        return 'DEBUG';
      case LogLevel.INFO:
        return 'INFO';
      case LogLevel.WARN:
        return 'WARN';
      case LogLevel.ERROR:
        return 'ERROR';
      default:
        return 'UNKNOWN';
    }
  }, []);

  // Filter logs by level
  const filteredLogs = logStore.filter(log => log.level >= filter);

  // Clear logs
  const handleClearLogs = useCallback(() => {
    logStore.length = 0;
  }, []);

  // Send test event to Sentry
  const handleSendTestEvent = useCallback(() => {
    try {
      // Send a test message
      Sentry.captureMessage('Test message from LogViewer');

      // Send a test error
      Sentry.captureException(new Error('Test error from LogViewer'));

      // Show confirmation
      alert('Test events sent to Sentry successfully!');
    } catch (error) {
      // Show error
      alert(`Failed to send test events to Sentry: ${error}`);
    }
  }, []);

  // Only show in development
  if (!visible || !__DEV__) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Log Viewer</Text>
          <View style={styles.filterContainer}>
            <Text style={styles.filterLabel}>Min Level:</Text>
            {[LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR].map((level) => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.filterButton,
                  filter === level && { backgroundColor: getLogLevelColor(level) },
                ]}
                onPress={() => setFilter(level)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    filter === level && { color: '#ffffff' },
                  ]}
                >
                  {getLogLevelName(level)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <FlatList
          data={filteredLogs}
          renderItem={({ item }) => (
            <View style={styles.logEntry}>
              <View style={styles.logHeader}>
                <Text style={[styles.logLevel, { color: getLogLevelColor(item.level) }]}>
                  {getLogLevelName(item.level)}
                </Text>
                <Text style={styles.logTimestamp}>{item.timestamp}</Text>
              </View>
              <Text style={styles.logMessage}>{item.message}</Text>
              {item.context && (
                <Text style={styles.logContext}>
                  {JSON.stringify(item.context, null, 2)}
                </Text>
              )}
            </View>
          )}
          keyExtractor={(_, index) => index.toString()}
          style={styles.logContainer}
          contentContainerStyle={filteredLogs.length === 0 ? styles.emptyContainer : undefined}
          ListEmptyComponent={
            <Text style={styles.emptyText}>No logs to display</Text>
          }
        />

        <View style={styles.footer}>
          <TouchableOpacity style={styles.button} onPress={handleClearLogs}>
            <Text style={styles.buttonText}>Clear Logs</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={handleSendTestEvent}>
            <Text style={styles.buttonText}>Test Sentry</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={onClose}>
            <Text style={styles.buttonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  filterLabel: {
    marginRight: 8,
    fontSize: 14,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#e9ecef',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  logContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    textAlign: 'center',
    color: '#6c757d',
  },
  logEntry: {
    marginHorizontal: 8,
    marginVertical: 4,
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f8f9fa',
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  logLevel: {
    fontWeight: 'bold',
    fontSize: 12,
  },
  logTimestamp: {
    fontSize: 12,
    color: '#6c757d',
  },
  logMessage: {
    fontSize: 14,
    marginBottom: 4,
  },
  logContext: {
    fontSize: 12,
    color: '#6c757d',
    fontFamily: 'monospace',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
    backgroundColor: '#0d6efd',
  },
  buttonText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
});

export default SentryLogViewer;
