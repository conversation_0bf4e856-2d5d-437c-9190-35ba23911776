import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { logger } from '@/utils/logging-sentry'; // Import the logger
import { appConfig } from '@/config/appConfig';
import { adFrequencyService, PlacementType } from '@/services/ad-frequency-service';

// Check if we're running in Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

// Define types for our context
interface AdMobContextType {
  isAdMobInitialized: boolean;
  getAdUnitId: (placement: 'stickyAd' | 'calendarScreen' | 'settingsScreen' | 'dateDetailsModal' | 'remindersScreen') => string;
  isExpoGo: boolean;
  // New methods for interstitial ads
  showInterstitial: (placement: PlacementType) => Promise<boolean>;
  trackPlacementInteraction: (placement: PlacementType) => Promise<void>;
}

// Create a default context
export const AdMobContext = React.createContext<AdMobContextType>({
  isAdMobInitialized: false,
  getAdUnitId: () => 'test-banner-ad',
  isExpoGo: true,
  showInterstitial: async () => false,
  trackPlacementInteraction: async () => {},
});

// Conditional imports to avoid errors in Expo Go
let MobileAds: any;
let TestIds: any;
let RNGoogleMobileAdsBannerAd: any;
let BannerAdSize: any;
let InterstitialAd: any;
let AdEventType: any;

// Only import the real AdMob SDK if we're not in Expo Go
if (!isExpoGo) {
  try {
    const GoogleMobileAds = require('react-native-google-mobile-ads');
    MobileAds = GoogleMobileAds.MobileAds;
    TestIds = GoogleMobileAds.TestIds;
    RNGoogleMobileAdsBannerAd = GoogleMobileAds.BannerAd;
    BannerAdSize = GoogleMobileAds.BannerAdSize;
    InterstitialAd = GoogleMobileAds.InterstitialAd;
    AdEventType = GoogleMobileAds.AdEventType;
  } catch (error) {
    logger.warn('Failed to import react-native-google-mobile-ads', { error });
  }
} else {
  // Create mock components for Expo Go
  RNGoogleMobileAdsBannerAd = ({ children }: { children?: React.ReactNode }) => null;
  BannerAdSize = { BANNER: 'BANNER' };
  TestIds = {
    BANNER: 'test-banner-ad',
    INTERSTITIAL: 'test-interstitial-ad'
  };
}

// Initialize the Google Mobile Ads SDK
export const initializeAdMob = async (): Promise<boolean> => {
  // Skip initialization in Expo Go
  if (isExpoGo) {
    logger.info('Skipping AdMob initialization in Expo Go');
    return false;
  }

  try {
    if (MobileAds) {
      await MobileAds().initialize();
      logger.info('AdMob SDK initialized successfully');
      return true;
    }
    return false;
  } catch (error) {
    logger.error('Error initializing AdMob SDK', { error });
    return false;
  }
};

// Cache for preloaded interstitial ads
const interstitialCache: Record<string, any> = {};

// AdMob Provider component
export const AdMobProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAdMobInitialized, setIsAdMobInitialized] = useState(false);

  // Function to get the appropriate banner ad unit ID based on placement
  const getAdUnitId = (placement: 'stickyAd' | 'calendarScreen' | 'settingsScreen' | 'dateDetailsModal' | 'remindersScreen'): string => {
    if (isExpoGo) {
      return 'test-banner-ad';
    }

    if (__DEV__) {
      return TestIds?.BANNER || 'test-banner-ad';
    }

    // Return the production ad unit ID for the specific placement
    return appConfig.ads.bannerAdUnits[placement];
  };

  // Function to get interstitial ad unit ID
  const getInterstitialAdUnitId = useCallback((placement: PlacementType): string => {
    if (isExpoGo) {
      return 'test-interstitial-ad';
    }

    if (__DEV__) {
      return TestIds?.INTERSTITIAL || 'test-interstitial-ad';
    }

    // Return the production interstitial ad unit ID
    return appConfig.ads.interstitialAdUnits[placement];
  }, []);

  // Function to preload an interstitial ad
  const preloadInterstitial = useCallback(async (placement: PlacementType): Promise<any> => {
    if (isExpoGo || !isAdMobInitialized || !InterstitialAd) {
      return null;
    }

    try {
      const adUnitId = getInterstitialAdUnitId(placement);

      // Check if we already have a loaded ad for this placement
      if (interstitialCache[placement] && !interstitialCache[placement]._loaded) {
        // If we have an ad that's still loading, return it
        return interstitialCache[placement];
      }

      // Create a new interstitial ad
      const interstitial = InterstitialAd.createForAdRequest(adUnitId, {
        requestNonPersonalizedAdsOnly: true,
      });

      // Set up event listeners
      const loadedListener = interstitial.addAdEventListener(AdEventType.LOADED, () => {
        logger.info(`Interstitial ad loaded for ${placement}`);
      });

      const errorListener = interstitial.addAdEventListener(AdEventType.ERROR, (error: any) => {
        try {
          logger.error(`Interstitial ad failed to load for ${placement}`, { error });
          // Remove from cache on error
          if (interstitialCache[placement] === interstitial) {
            delete interstitialCache[placement];
          }

          // Schedule graceful retry after network issues
          setTimeout(() => {
            try {
              preloadInterstitial(placement);
            } catch (retryError) {
              logger.debug(`Interstitial retry failed for ${placement}`, { retryError });
            }
          }, 10000); // 10 second delay for interstitial retry
        } catch (handlerError) {
          logger.error(`Error in interstitial error handler for ${placement}`, { handlerError });
        }
      });

      const closedListener = interstitial.addAdEventListener(AdEventType.CLOSED, () => {
        try {
          logger.info(`Interstitial ad closed for ${placement}`);
          // Remove from cache when closed
          if (interstitialCache[placement] === interstitial) {
            delete interstitialCache[placement];
          }
          // Preload the next ad
          setTimeout(() => {
            try {
              preloadInterstitial(placement);
            } catch (reloadError) {
              logger.debug(`Interstitial reload failed for ${placement}`, { reloadError });
            }
          }, 1000);
        } catch (handlerError) {
          logger.error(`Error in interstitial closed handler for ${placement}`, { handlerError });
        }
      });

      // Load the ad
      interstitial.load();

      // Store in cache
      interstitialCache[placement] = interstitial;

      // Store cleanup function
      interstitial._cleanup = () => {
        try {
          // In react-native-google-mobile-ads, addAdEventListener returns a function to remove the listener
          if (typeof loadedListener === 'function') {
            loadedListener();
          }
          if (typeof errorListener === 'function') {
            errorListener();
          }
          if (typeof closedListener === 'function') {
            closedListener();
          }
        } catch (cleanupError) {
          logger.debug(`Error during interstitial cleanup for ${placement}`, { cleanupError });
        }
      };

      return interstitial;
    } catch (error) {
      logger.error(`Error preloading interstitial ad for ${placement}`, { error });
      return null;
    }
  }, [isAdMobInitialized, isExpoGo, getInterstitialAdUnitId]);

  // Function to track placement interaction without showing an ad
  const trackPlacementInteraction = useCallback(async (placement: PlacementType): Promise<void> => {
    if (isExpoGo) {
      return;
    }

    try {
      await adFrequencyService.incrementPlacementCounter(placement);
      logger.debug(`Tracked interaction for ${placement}`);
    } catch (error) {
      logger.error(`Error tracking interaction for ${placement}`, { error });
    }
  }, []);

  // Function to show an interstitial ad
  const showInterstitial = useCallback(async (placement: PlacementType): Promise<boolean> => {
    if (isExpoGo) {
      logger.info(`Skipping interstitial ad in Expo Go for ${placement}`);
      return false;
    }

    if (!isAdMobInitialized) {
      logger.info(`AdMob not initialized, can't show interstitial for ${placement}`);
      return false;
    }

    // Check frequency capping
    const canShowAd = await adFrequencyService.canShowAd(placement);
    if (!canShowAd) {
      // If we can't show an ad, still increment the counter for this placement
      if (placement === 'dateDetails') {
        await adFrequencyService.incrementPlacementCounter(placement);
      }
      return false;
    }

    try {
      // Get or preload the interstitial
      let interstitial = interstitialCache[placement];
      if (!interstitial) {
        interstitial = await preloadInterstitial(placement);
      }

      // If we have a loaded ad, show it
      if (interstitial && interstitial._loaded) {
        logger.info(`Showing interstitial ad for ${placement}`);
        await interstitial.show();

        // Record that we showed an ad
        await adFrequencyService.recordAdShown(placement);

        return true;
      } else {
        logger.info(`No loaded interstitial available for ${placement}`);

        // Try to preload for next time
        preloadInterstitial(placement);

        // Still increment the counter for this placement
        if (placement === 'dateDetails') {
          await adFrequencyService.incrementPlacementCounter(placement);
        }

        return false;
      }
    } catch (error) {
      logger.error(`Error showing interstitial ad for ${placement}`, { error });
      return false;
    }
  }, [isAdMobInitialized, isExpoGo, preloadInterstitial]);

  // Initialize AdMob
  useEffect(() => {
    const initialize = async () => {
      if (!isExpoGo) {
        const initialized = await initializeAdMob();
        setIsAdMobInitialized(initialized);
      }
    };

    initialize();
  }, []);

  // Preload interstitial ads when AdMob is initialized
  useEffect(() => {
    if (isAdMobInitialized && !isExpoGo) {
      // Initialize ad frequency service
      adFrequencyService.initialize();

      // Preload interstitial ads for each placement
      preloadInterstitial('dateDetails');
      preloadInterstitial('reminderCreation');
      preloadInterstitial('yearChange');
      preloadInterstitial('appSession');
    }
  }, [isAdMobInitialized, isExpoGo, preloadInterstitial]);

  // Clean up interstitial ads on unmount
  useEffect(() => {
    return () => {
      // Clean up any loaded interstitials
      Object.values(interstitialCache).forEach((interstitial: any) => {
        if (interstitial && interstitial._cleanup) {
          interstitial._cleanup();
        }
      });
    };
  }, []);

  return (
    <AdMobContext.Provider value={{
      isAdMobInitialized,
      getAdUnitId,
      isExpoGo,
      showInterstitial,
      trackPlacementInteraction
    }}>
      {children}
    </AdMobContext.Provider>
  );
};

// Hook to use AdMob context
export const useAdMob = () => React.useContext(AdMobContext);

// Export the BannerAd component and types
export { RNGoogleMobileAdsBannerAd, BannerAdSize };
