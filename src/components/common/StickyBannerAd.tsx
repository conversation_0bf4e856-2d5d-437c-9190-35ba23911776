import React, { useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDarkMode } from '@/store/settings-store';
import colors from '@/constants/colors';
import { useAdMob, RNGoogleMobileAdsBannerAd, BannerAdSize } from './AdMobProvider';
import { useComponentLogger } from '@/utils/logging-sentry';

/**
 * A banner ad component that sticks to the bottom of the screen
 * This is more effective than ads placed within scrollable content
 */
const StickyBannerAd: React.FC = () => {
  const logger = useComponentLogger('StickyBannerAd');
  const isDarkMode = useDarkMode();
  const theme = isDarkMode ? colors.dark : colors.light;
  const { isAdMobInitialized, getAdUnitId, isExpoGo } = useAdMob();
  const insets = useSafeAreaInsets();
  const [adFailed, setAdFailed] = React.useState(false);
  const [retryCount, setRetryCount] = React.useState(0);
  const [isRetrying, setIsRetrying] = React.useState(false);
  const [forceRefresh, setForceRefresh] = React.useState(0);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const maxRetries = 3; // Maximum retry attempts
  const retryDelayMs = 2000; // 2 seconds delay between retries

  // Calculate bottom padding to account for safe area
  const bottomPadding = Math.max(insets.bottom, 8);

  // Get network status with safe fallback
  let isOnline = false;
  try {
    // Try to import and use network status context
    const { useNetworkStatusContext } = require('@/components/providers/NetworkStatusProvider');
    const networkContext = useNetworkStatusContext();
    isOnline = networkContext?.isOnline === true;
  } catch (error) {
    // Gracefully handle case where NetworkStatusProvider is not available
    logger.debug('NetworkStatusProvider not available, assuming online', { error });
    isOnline = true; // Assume online if network status is unavailable
  }

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    };
  }, []);

  // Retry mechanism when network becomes available
  const scheduleRetry = useCallback(() => {
    if (retryCount >= maxRetries || isRetrying) {
      logger.debug(`Max retries reached or already retrying for sticky ad`);
      return;
    }

    setIsRetrying(true);
    logger.info(`Scheduling sticky ad retry ${retryCount + 1}/${maxRetries}`);

    retryTimeoutRef.current = setTimeout(() => {
      try {
        setRetryCount(prev => prev + 1);
        setAdFailed(false);
        setForceRefresh(prev => prev + 1); // Force component re-render
        setIsRetrying(false);
        logger.debug(`Retrying sticky ad load`);
      } catch (error) {
        logger.error(`Error during sticky ad retry`, { error });
        setIsRetrying(false);
      }
    }, retryDelayMs);
  }, [retryCount, maxRetries, isRetrying, logger]);

  // Network status change handler
  useEffect(() => {
    if (isOnline && adFailed && !isRetrying && retryCount < maxRetries) {
      logger.info(`Network available, attempting sticky ad retry`);
      scheduleRetry();
    }
  }, [isOnline, adFailed, isRetrying, retryCount, maxRetries, scheduleRetry, logger]);

  // If we're in Expo Go, show a placeholder
  if (isExpoGo) {
    return (
      <View
        style={[
          styles.container,
          {
            backgroundColor: theme.border,
            paddingBottom: bottomPadding
          }
        ]}
      >
        <Text style={[styles.adText, { color: theme.subtext }]}>
          Ad Placeholder (Expo Go)
        </Text>
      </View>
    );
  }

  // If AdMob is not initialized, show a loading placeholder
  if (!isAdMobInitialized) {
    return (
      <View
        style={[
          styles.container,
          {
            backgroundColor: theme.border,
            paddingBottom: bottomPadding
          }
        ]}
      >
        <Text style={[styles.adText, { color: theme.subtext }]}>
          Loading Ad...
        </Text>
      </View>
    );
  }

  // If ad failed to load, hide the sticky ad completely
  if (adFailed) {
    return null;
  }

  // If AdMob is initialized, show a real ad
  return (
    <View
      style={[
        styles.adContainer,
        {
          paddingBottom: bottomPadding,
          backgroundColor: theme.background
        }
      ]}
    >
      <RNGoogleMobileAdsBannerAd
        key={`stickyAd-${forceRefresh}`} // Force re-render on retry
        unitId={getAdUnitId('stickyAd')}
        size={BannerAdSize.BANNER}
        requestOptions={{
          // Personalized ads enabled for better revenue
        }}
        onAdLoaded={() => {
          try {
            logger.info(`Sticky ad loaded successfully (attempt ${retryCount + 1})`);
            setAdFailed(false);
            setRetryCount(0); // Reset retry count on success
            setIsRetrying(false);

            // Clear any pending retry timeout
            if (retryTimeoutRef.current) {
              clearTimeout(retryTimeoutRef.current);
              retryTimeoutRef.current = null;
            }
          } catch (error) {
            logger.error(`Error in sticky ad onAdLoaded handler`, { error });
          }
        }}
        onAdFailedToLoad={(error: Error) => {
          try {
            logger.error(`Sticky ad failed to load (attempt ${retryCount + 1})`, {
              error: error?.message || 'Unknown error',
              retryCount,
              isOnline
            });
            setAdFailed(true);
            setIsRetrying(false);

            // Don't immediately retry here - let the network status effect handle it
            // This prevents rapid retry loops
          } catch (handlerError) {
            logger.error(`Error in sticky ad onAdFailedToLoad handler`, { handlerError });
            setAdFailed(true);
            setIsRetrying(false);
          }
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    // Add shadow for iOS
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  adContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    // Add shadow for iOS
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  adText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default StickyBannerAd;
