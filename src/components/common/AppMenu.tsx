import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet, Share, Linking, Platform, Alert } from 'react-native';
import { MoreVertical, Share2, Star, Package, FileText, Users } from 'lucide-react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { useComponentLogger } from '@/utils/logging-sentry'; // Import logger hook
import { appConfig } from '@/config/appConfig';
import { reviewService } from '@/services/review-service';

const AppMenu: React.FC = () => {
  const logger = useComponentLogger('AppMenu'); // Get logger instance
  const [menuVisible, setMenuVisible] = useState(false);
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Toggle menu visibility
  const toggleMenu = () => {
    setMenuVisible(!menuVisible);
  };

  // Close menu
  const closeMenu = () => {
    setMenuVisible(false);
  };

  // Share app
  const handleShareApp = async () => {
    closeMenu();

    try {
      await Share.share({
        message: language === 'or'
          ? `ଓଡ଼ିଆ କ୍ୟାଲେଣ୍ଡର ଆପ୍ ଡାଉନଲୋଡ କରନ୍ତୁ: ${appConfig.share.appUrl}`
          : `Download the Odia Calendar App: ${appConfig.share.appUrl}`,
      });
    } catch (error) {
      logger.error('Error sharing app', { error });
    }
  };

  // Rate app (Triple dot menu - always goes to store directly)
  const handleRateApp = async () => {
    closeMenu();

    try {
      const success = await reviewService.openStoreForReview();
      if (!success) {
        Alert.alert(translations[language].error, translations[language].cannotOpenStore);
      }
    } catch (error) {
      logger.error('Error opening store for review', { error });
      Alert.alert(translations[language].error, translations[language].cannotOpenStore);
    }
  };

  // More apps
  const handleMoreApps = () => {
    closeMenu();

    const url = Platform.select({
      ios: appConfig.store.iosDeveloperUrl,
      android: appConfig.store.androidDeveloperUrl,
      default: appConfig.store.androidDeveloperUrl,
    });

    Linking.canOpenURL(url).then((supported) => {
      if (supported) {
        Linking.openURL(url);
      } else {
        Alert.alert(translations[language].error, translations[language].cannotOpenStore);
        if (__DEV__) {
          console.warn(`Could not open developer URL: ${url}`);
        }
      }
    });
  };

  // Open About Us page
  const handleOpenAboutUs = () => {
    closeMenu();

    const aboutUsUrl = 'https://sites.google.com/view/odia-calendar-1/odia-calendar-lite-about-us';

    Linking.canOpenURL(aboutUsUrl).then((supported) => {
      if (supported) {
        Linking.openURL(aboutUsUrl);
      } else {
        Alert.alert(translations[language].error, translations[language].cannotOpenStore);
        if (__DEV__) {
          console.warn(`Could not open About Us URL: ${aboutUsUrl}`);
        }
      }
    });
  };

  // Open Privacy Policy page
  const handleOpenPrivacyPolicy = () => {
    closeMenu();

    const privacyPolicyUrl = 'https://sites.google.com/view/odia-calendar-1/privacy-policy-odia-calendar-lite';

    Linking.canOpenURL(privacyPolicyUrl).then((supported) => {
      if (supported) {
        Linking.openURL(privacyPolicyUrl);
      } else {
        Alert.alert(translations[language].error, translations[language].cannotOpenStore);
        if (__DEV__) {
          console.warn(`Could not open Privacy Policy URL: ${privacyPolicyUrl}`);
        }
      }
    });
  };

  return (
    <View>
      <TouchableOpacity onPress={toggleMenu} style={styles.menuButton}>
        <MoreVertical size={24} color={theme.text} />
      </TouchableOpacity>

      <Modal
        visible={menuVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeMenu}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeMenu}
        >
          <View
            style={[
              styles.menuContainer,
              { backgroundColor: theme.card, top: 50, right: 16 }
            ]}
          >
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleMoreApps}
            >
              <Package size={20} color={theme.text} />
              <Text style={[styles.menuItemText, { color: theme.text }]}>
                {translations[language].moreApps}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleShareApp}
            >
              <Share2 size={20} color={theme.text} />
              <Text style={[styles.menuItemText, { color: theme.text }]}>
                {translations[language].shareApp}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleRateApp}
            >
              <Star size={20} color={theme.text} />
              <Text style={[styles.menuItemText, { color: theme.text }]}>
                {translations[language].rateReview}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleOpenAboutUs}
            >
              <Users size={20} color={theme.text} />
              <Text style={[styles.menuItemText, { color: theme.text }]}>
                {translations[language].aboutUs}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleOpenPrivacyPolicy}
            >
              <FileText size={20} color={theme.text} />
              <Text style={[styles.menuItemText, { color: theme.text }]}>
                {translations[language].privacyPolicy}
              </Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  menuButton: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  menuContainer: {
    position: 'absolute',
    width: 200,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  menuItemText: {
    marginLeft: 12,
    fontSize: 16,
  },
});

export default AppMenu;
