import React, { useState } from 'react';
import { Modal, View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useCalendarStore } from '@/store/calendar-store';
import { useSettingsStore, useDarkMode, useTranslation } from '@/store/settings-store';
import { syncService } from '@/services/sync-service';
import colors from '@/constants/colors';
import { DownloadCloud, X } from 'lucide-react-native';
import { useComponentLogger } from '@/utils/logging-sentry'; // Import logger hook

export default function UpdatePromptModal() {
  const logger = useComponentLogger('UpdatePromptModal'); // Get logger instance
  const { showUpdatePrompt, setShowUpdatePrompt, setIsUpdatePending } = useCalendarStore();
  const isDarkMode = useDarkMode();
  const t = useTranslation();
  const activeTheme = isDarkMode ? colors.dark : colors.light;
  const [isLoading, setIsLoading] = useState(false);

  const handleCancel = () => {
    setShowUpdatePrompt(false);
    // Keep isUpdatePending true, so it might prompt again later
  };

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      const success = await syncService.triggerManualUpdate();
      if (success) {
        // Update was successful, clear pending state
        setIsUpdatePending(false);
      } else {
        // Handle update failure? Maybe show a toast?
        logger.warn('Manual update trigger reported no success.');
      }
    } catch (error) {
      logger.error('Error triggering manual update', { error });
      // Handle error, maybe show a toast
    } finally {
      setIsLoading(false);
      setShowUpdatePrompt(false); // Close modal regardless of success/failure for now
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={showUpdatePrompt}
      onRequestClose={handleCancel} // Allow closing with back button on Android
    >
      <View style={styles.centeredView}>
        <View style={[styles.modalView, { backgroundColor: activeTheme.card }]}>
          <View style={styles.header}>
            <DownloadCloud size={24} color={activeTheme.primary} />
            <Text style={[styles.modalTitle, { color: activeTheme.text }]}>
              {t('updateAvailableTitle', 'Update Available')}
            </Text>
            <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
              <X size={20} color={activeTheme.subtext} />
            </TouchableOpacity>
          </View>

          <Text style={[styles.modalText, { color: activeTheme.text }]}>
            {t('updateAvailableMessage', 'New calendar data is available. Download now?')}
          </Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.buttonCancel, { borderColor: activeTheme.border }]}
              onPress={handleCancel}
              disabled={isLoading}
            >
              <Text style={[styles.textStyle, styles.textCancel, { color: activeTheme.subtext }]}>
                {t('cancel', 'Cancel')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.buttonDownload, { backgroundColor: activeTheme.primary }]}
              onPress={handleDownload}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#ffffff" />
              ) : (
                <Text style={[styles.textStyle, styles.textDownload]}>
                  {t('download', 'Download')}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

// Add new translation keys
// updateAvailableTitle: 'Update Available' / 'ଅପଡେଟ୍ ଉପଲବ୍ଧ'
// updateAvailableMessage: 'New calendar data is available. Download now?' / 'ନୂଆ କ୍ୟାଲେଣ୍ଡର ଡାଟା ଉପଲବ୍ଧ ଅଛି। ବର୍ତ୍ତମାନ ଡାଉନଲୋଡ୍ କରନ୍ତୁ କି?'
// cancel: 'Cancel' / 'ବାତିଲ କରନ୍ତୁ'
// download: 'Download' / 'ଡାଉନଲୋଡ୍ କରନ୍ତୁ'

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Dim background
  },
  modalView: {
    margin: 20,
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '85%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 10,
    flex: 1, // Take remaining space
  },
  closeButton: {
    padding: 4, // Easier tap target
  },
  modalText: {
    marginBottom: 24,
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    elevation: 2,
    minWidth: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonCancel: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  buttonDownload: {
    // backgroundColor is set dynamically
  },
  textStyle: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  textCancel: {
    // color is set dynamically
  },
  textDownload: {
    color: 'white',
  },
});
