import React, { createContext, useContext, useState, useCallback } from 'react';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import NetworkErrorToast from '@/components/common/NetworkErrorToast';
import { useTranslation } from '@/store/settings-store';

interface NetworkStatusContextType {
  isOnline: boolean | null;
  isConnected: boolean | null;
  isInternetReachable: boolean | null;
  connectionType: string | null;
  checkNetworkStatus: () => Promise<{
    isConnected: boolean | null;
    isInternetReachable: boolean | null;
  }>;
  ensureNetworkConnection: () => Promise<boolean>;
  showNetworkError: (options?: {
    message?: string;
    action?: {
      label: string;
      onPress: () => void;
    };
    duration?: number;
  }) => void;
  hideNetworkError: () => void;
}

const NetworkStatusContext = createContext<NetworkStatusContextType | null>(null);

/**
 * Provider component for network status monitoring and error handling
 */
export const NetworkStatusProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {
    isConnected,
    isInternetReachable,
    connectionType,
    isOnline,
    checkNetworkStatus,
    ensureNetworkConnection,
  } = useNetworkStatus();

  const t = useTranslation();
  
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState<string | undefined>(undefined);
  const [toastAction, setToastAction] = useState<{
    label: string;
    onPress: () => void;
  } | undefined>(undefined);
  const [toastDuration, setToastDuration] = useState(5000);

  const showNetworkError = useCallback((options?: {
    message?: string;
    action?: {
      label: string;
      onPress: () => void;
    };
    duration?: number;
  }) => {
    setToastMessage(options?.message);
    setToastAction(options?.action);
    if (options?.duration !== undefined) {
      setToastDuration(options.duration);
    }
    setToastVisible(true);
  }, []);

  const hideNetworkError = useCallback(() => {
    setToastVisible(false);
  }, []);

  const handleDismiss = useCallback(() => {
    setToastVisible(false);
  }, []);

  return (
    <NetworkStatusContext.Provider
      value={{
        isOnline,
        isConnected,
        isInternetReachable,
        connectionType,
        checkNetworkStatus,
        ensureNetworkConnection,
        showNetworkError,
        hideNetworkError,
      }}
    >
      {children}
      <NetworkErrorToast
        visible={toastVisible}
        message={toastMessage}
        onDismiss={handleDismiss}
        autoHideDuration={toastDuration}
        action={toastAction}
      />
    </NetworkStatusContext.Provider>
  );
};

/**
 * Hook to use network status context
 */
export const useNetworkStatusContext = (): NetworkStatusContextType => {
  const context = useContext(NetworkStatusContext);
  if (!context) {
    throw new Error('useNetworkStatusContext must be used within a NetworkStatusProvider');
  }
  return context;
};
