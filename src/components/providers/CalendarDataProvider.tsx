import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { dataCoordinator } from '@/services/data-coordinator';
import { databaseService } from '@/services/database-service';
import { reminderService } from '@/services/reminder-service';
import { notificationService } from '@/services/notification-service';
import Constants from 'expo-constants';
import { useComponentLogger } from '@/utils/logging-sentry'; // Import logger hook

// Import debug components only in development builds
const SQLQueryExecutor = __DEV__
  ? require('@/components/debug/SQLQueryExecutor').default
  : null;

const TimeZoneDebugPanel = __DEV__
  ? require('@/components/debug/TimeZoneDebugPanel').default
  : null;

// Create a context for the calendar data provider
const CalendarDataContext = createContext<{
  isInitialized: boolean;
  error: Error | null;
  retryInitialization: () => Promise<void>;
}>({
  isInitialized: false,
  error: null,
  retryInitialization: async () => {},
});

// Props for the CalendarDataProvider component
interface CalendarDataProviderProps {
  children: ReactNode;
}

// Check if we're in development mode
const isDevelopment = __DEV__;

/**
 * Provider component for calendar data
 * Initializes the database and provides the data coordinator
 */
export function CalendarDataProvider({ children }: CalendarDataProviderProps) {
  const logger = useComponentLogger('CalendarDataProvider'); // Get logger instance
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  const initializeDatabase = async () => {
    if (isInitializing) {
      logger.debug('Database initialization already in progress, skipping');
      return;
    }

    logger.info('Starting database initialization');
    setIsInitializing(true);
    setError(null);

    try {
      // Step 1: Initialize the data coordinator (which initializes the database)
      logger.debug('Calling dataCoordinator.initialize()');
      await dataCoordinator.initialize();
      logger.info('Database initialization completed successfully');

      // Step 2: Initialize the reminder service
      // This is done after dataCoordinator to ensure proper initialization order
      logger.debug('Initializing reminder service');
      await reminderService.initialize();
      logger.info('Reminder service initialized successfully');

      // Step 3: Handle any pending notification navigation
      // This should be done after all services are initialized
      logger.debug('Checking for pending notification navigation');
      await notificationService.handlePendingNavigation();
      logger.info('Pending navigation check completed');

      setIsInitialized(true);
    } catch (err) {
      const error = err as Error;
      setError(error);
      logger.error('Failed to initialize calendar data', {
        error: error.message,
        stack: error.stack,
        name: error.name
      });
    } finally {
      logger.debug('Database initialization process finished (success or failure)');
      setIsInitializing(false);
    }
  };

  useEffect(() => {
    initializeDatabase();
  }, []);

  // Debug functions
  const debugGetDatabaseFileInfo = async () => {
    await databaseService.debugGetDatabaseFileInfo();
  };

  const debugLogDatabaseInfo = async () => {
    await databaseService.debugLogDatabaseInfo();
  };

  const clearCache = () => {
    dataCoordinator.clearCache();
    logger.info('Cache cleared via debug panel');
  };

  // Render debug panel in development mode
  const renderDebugPanel = () => {
    if (!isDevelopment) return null;

    // Log that we're rendering the debug panel (for troubleshooting)
    logger.debug('Rendering debug panel button, showDebugPanel:', showDebugPanel);

    return (
      <View style={styles.debugContainer}>
        <TouchableOpacity
          style={styles.debugButton}
          onPress={() => setShowDebugPanel(!showDebugPanel)}
        >
          <Text style={styles.debugButtonText}>
            {showDebugPanel ? 'Hide Debug' : 'Show Debug'}
          </Text>
        </TouchableOpacity>

        {showDebugPanel && (
          <View style={styles.debugPanel}>
            {/* Add a close button at the top-right corner */}
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowDebugPanel(false)}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.debugTitle}>Database Debug</Text>

            <View style={styles.debugInfo}>
              <Text>Status: {isInitialized ? 'Initialized' : 'Not Initialized'}</Text>
              <Text>Error: {error ? error.message : 'None'}</Text>
              <Text>Platform: {Platform.OS}</Text>
              <Text>Expo SDK: {Constants.expoConfig?.sdkVersion}</Text>
            </View>

            <View style={styles.debugButtons}>
              <TouchableOpacity
                style={styles.debugActionButton}
                onPress={initializeDatabase}
                disabled={isInitializing}
              >
                <Text style={styles.debugActionButtonText}>
                  {isInitializing ? 'Initializing...' : 'Retry Init'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.debugActionButton}
                onPress={debugLogDatabaseInfo}
              >
                <Text style={styles.debugActionButtonText}>Log DB Info</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.debugActionButton}
                onPress={debugGetDatabaseFileInfo}
              >
                <Text style={styles.debugActionButtonText}>DB Info</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.debugActionButton}
                onPress={clearCache}
              >
                <Text style={styles.debugActionButtonText}>Clear Cache</Text>
              </TouchableOpacity>
            </View>

            {/* SQL Query Executor - Only included in development builds */}
            {__DEV__ && SQLQueryExecutor && <SQLQueryExecutor />}

            {/* Time Zone Debug Panel - Only included in development builds */}
            {__DEV__ && TimeZoneDebugPanel && <TimeZoneDebugPanel />}
          </View>
        )}
      </View>
    );
  };

  // If there's an error initializing the database, show an error message
  if (error && !isInitialized) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Database Error</Text>
        <Text style={styles.errorMessage}>{error.message}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={initializeDatabase}
          disabled={isInitializing}
        >
          <Text style={styles.retryButtonText}>
            {isInitializing ? 'Retrying...' : 'Retry'}
          </Text>
        </TouchableOpacity>
        {renderDebugPanel()}
      </View>
    );
  }

  return (
    <CalendarDataContext.Provider
      value={{
        isInitialized,
        error,
        retryInitialization: initializeDatabase
      }}
    >
      {children}
      {renderDebugPanel()}
    </CalendarDataContext.Provider>
  );
}

// Styles for the debug panel and error container
const styles = StyleSheet.create({
  debugContainer: {
    position: 'absolute',
    bottom: 100, // Moved up to avoid overlap with settings button
    right: 10,
    zIndex: 9999,
  },
  debugButton: {
    backgroundColor: '#007AFF',
    padding: 8,
    borderTopLeftRadius: 8,
    margin: 8,
    elevation: 5, // Add elevation for Android
    shadowColor: '#000', // Shadow for iOS
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  debugButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  debugPanel: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 16,
    margin: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    maxWidth: 300,
    maxHeight: '80%', // Limit height to prevent going off screen
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  debugInfo: {
    marginBottom: 16,
  },
  debugButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  debugActionButton: {
    backgroundColor: '#007AFF',
    padding: 8,
    borderRadius: 4,
    margin: 4,
  },
  debugActionButtonText: {
    color: 'white',
    fontSize: 12,
  },
  closeButton: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: '#FF3B30',
    padding: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    zIndex: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: 14,
  },
  // Error container styles
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f8f8',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#FF3B30',
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

/**
 * Hook to use the calendar data context
 * @returns The calendar data context with initialization status, error, and retry function
 */
export function useCalendarDataContext() {
  return useContext(CalendarDataContext);
}
