# Odia Calendar Lite

A beautiful, culturally rich Odia calendar app built with React Native and Expo.

## Project Structure

```
/src
  /components
    /calendar        # Calendar-related components
    /common          # Reusable UI components
    /settings        # Settings-related components
  /screens
    /tabs            # Tab screen components
  /store             # State management with Zustand
  /utils             # Utility functions
  /constants         # App constants and translations
  /mocks             # Mock data (to be replaced with database)
```

## Features

- Beautiful calendar UI with month navigation
- Date details with Odia calendar information
- Festival and marriage date listings
- Theme switching (light/dark)
- Language switching (English/Odia)
- Moon phase indicators (Purnima/Amavasya)

## Getting Started

### Prerequisites

- Node.js
- npm or yarn
- Expo CLI

### Installation

```bash
# Install dependencies
npm install

# Start the development server
npm start
```

## Tech Stack

- React Native
- Expo
- TypeScript
- Zustand for state management
- AsyncStorage for persistence
