import React, { useEffect } from 'react';
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { Tabs } from "expo-router";
import { useDarkMode, useLanguage } from "@/store/settings-store";
import { translations } from "@/constants/translations";
import colors from "@/constants/colors";
import { reviewService } from '@/services/review-service';
import { Bell } from 'lucide-react-native';

export default function TabLayout() {
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Get translated tab names
  const homeTabName = translations[language].calendar;
  const remindersTabName = translations[language].reminders; // Use the correct key from the Tabs section
  const settingsTabName = translations[language].settings;

  useEffect(() => {
    // Check for automatic in-app reviews when tab layout mounts
    reviewService.incrementOpenCount().then(() => {
      reviewService.requestInAppReview();
    });
  }, []); // Empty dependency array ensures this runs only once when the layout mounts

  return (
    <Tabs
      screenOptions={{
        headerShown: false, // Hide the header completely
        tabBarActiveTintColor: theme.primary,
        tabBarInactiveTintColor: theme.subtext,
        tabBarStyle: {
          backgroundColor: theme.background,
          borderTopColor: theme.border,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: homeTabName,
          tabBarIcon: ({ color }) => <FontAwesome name="home" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="reminders"
        options={{
          title: remindersTabName,
          tabBarIcon: ({ color }) => <Bell size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: settingsTabName,
          tabBarIcon: ({ color }) => <FontAwesome name="cog" size={24} color={color} />,
        }}
      />
    </Tabs>
  );
}
