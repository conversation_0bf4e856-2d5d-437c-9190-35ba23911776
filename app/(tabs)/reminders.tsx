import React, { useState, useEffect } from 'react';
import { Stack } from 'expo-router';
import { View, StyleSheet, Text } from 'react-native';
import { useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import RemindersList from '@/components/reminders/RemindersList';
import NotificationPermissionAlert from '@/components/reminders/NotificationPermissionAlert';
import { notificationService } from '@/services/notification-service';
import StickyBannerAd from '@/components/common/StickyBannerAd';
import { useCalendarDataContext } from '@/components/providers/CalendarDataProvider';

export default function RemindersScreen() {
  const language = useLanguage();
  const t = translations[language];
  const [permissionGranted, setPermissionGranted] = useState<boolean | null>(null);
  const { isInitialized } = useCalendarDataContext();

  // Check notification permissions when the screen loads
  useEffect(() => {
    const checkPermissions = async () => {
      const status = await notificationService.getPermissionStatus();
      setPermissionGranted(status?.granted || false);
    };

    // Only check permissions if the database is initialized
    if (isInitialized) {
      checkPermissions();
    }
  }, [isInitialized]);

  // Handle permission changes
  const handlePermissionChange = (granted: boolean) => {
    setPermissionGranted(granted);
  };

  // Show a loading state if the database isn't initialized yet
  if (!isInitialized) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <Stack.Screen
          options={{
            title: t.reminders,
            headerShadowVisible: false,
          }}
        />
        <Text style={styles.loadingText}>{t.loading}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          title: t.reminders,
          headerShadowVisible: false,
        }}
      />

      {/* Show permission alert if permissions are not granted */}
      {permissionGranted === false && (
        <NotificationPermissionAlert
          onPermissionChange={handlePermissionChange}
          showOnlyIfDenied={false}
        />
      )}

      <RemindersList />

      {/* Sticky Banner Ad - always visible at the bottom */}
      <StickyBannerAd />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
});
