import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch, ScrollView, StatusBar, Linking, Share, Platform, ActivityIndicator, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Sun, Globe, Info, Package, Share2, Star, Bug, Wifi, DownloadCloud, RefreshCw, Trash2, FileText, Users } from 'lucide-react-native';
import { useSettingsStore, useDarkMode, useLanguage } from '@/store/settings-store';
import { syncService } from '@/services/sync-service';
import { dataCoordinator } from '@/services/data-coordinator';
import { formatLastCheckedTime } from '@/utils/date-utils';
import { translations } from '@/constants/translations';
import StickyBannerAd from '@/components/common/StickyBannerAd';
import BannerAd from '@/components/common/BannerAd';
import colors from '@/constants/colors';
import { appConfig } from '@/config/appConfig';
import * as Sentry from '@sentry/react-native';
import { reviewService } from '@/services/review-service';
import { databaseService } from '@/services/database-service';
import { useNetworkStatusContext } from '@/components/providers/NetworkStatusProvider';

export default function SettingsScreen() {
  const {
    themeMode,
    language,
    errorReportingEnabled,
    autoUpdateEnabled, // Added
    autoUpdateWifiOnly, // Added
    setThemeMode,
    setLanguage,
    toggleErrorReporting,
    toggleAutoUpdate, // Added
    toggleWifiOnlyUpdate, // Added
  } = useSettingsStore();
  const isDarkMode = useDarkMode();
  const currentLanguage = useLanguage();
  const activeTheme = isDarkMode ? colors.dark : colors.light;
  const t = translations[currentLanguage]; // Helper for translations

  // Network status context for handling offline scenarios
  const { ensureNetworkConnection, showNetworkError } = useNetworkStatusContext();

  // State for manual update check
  const [isCheckingForUpdates, setIsCheckingForUpdates] = useState(false);
  const [updateStatusMessage, setUpdateStatusMessage] = useState<string | null>(null);
  const [updateStatusType, setUpdateStatusType] = useState<'success' | 'info' | 'error' | null>(null);
  const [lastCheckedTimestamp, setLastCheckedTimestamp] = useState<string | null>(null);

  // State for delete year data
  const [availableYears, setAvailableYears] = useState<string[]>([]);
  const [deleteYearStatusMessage, setDeleteYearStatusMessage] = useState<string | null>(null);
  const [deleteYearStatusType, setDeleteYearStatusType] = useState<'success' | 'error' | null>(null);

  // Function to fetch and format the last checked time
  const fetchLastCheckedTime = useCallback(async () => {
    try {
      // Note: Using the key directly as importing STORAGE_KEYS from syncService might cause circular dependency issues
      const timestampStr = await AsyncStorage.getItem('last_sync_check_timestamp');
      const timestamp = timestampStr ? parseInt(timestampStr, 10) : null;
      const formattedTime = formatLastCheckedTime(timestamp);
      setLastCheckedTimestamp(formattedTime);
    } catch (error) {
      if (__DEV__) {
        console.error("Failed to fetch last checked time:", error);
      }
      setLastCheckedTimestamp(null); // Reset on error
    }
  }, []);

  // Function to fetch available years
  const fetchAvailableYears = useCallback(async () => {
    try {
      // Ensure database is initialized
      if (!databaseService.isDbInitialized) {
        await dataCoordinator.initialize();
      }

      const years = await databaseService.getAvailableYearsFromVersions();

      // Filter out the current year
      const currentYear = new Date().getFullYear().toString();
      const filteredYears = years.filter(year => year !== currentYear);

      setAvailableYears(filteredYears);
    } catch (error) {
      console.error('Failed to fetch available years:', error);
      setAvailableYears([]);
    }
  }, []);

  // Function to handle year deletion
  const handleDeleteYear = useCallback(async (year: string) => {
    if (!year) return;

    setDeleteYearStatusMessage(null);
    setDeleteYearStatusType(null);

    try {
      const result = await dataCoordinator.deleteYearData(parseInt(year));

      if (result.success) {
        setDeleteYearStatusMessage(t.deleteYearSuccess.replace('{year}', year));
        setDeleteYearStatusType('success');
        // Refresh the available years list
        fetchAvailableYears();
      } else {
        // Handle specific error codes
        if (result.errorCode === 'CURRENT_YEAR') {
          setDeleteYearStatusMessage(t.deleteYearCurrentYear);
          setDeleteYearStatusType('error');
        } else {
          setDeleteYearStatusMessage(t.deleteYearFailed.replace('{year}', year));
          setDeleteYearStatusType('error');
        }
      }
    } catch (error) {
      console.error(`Error deleting year ${year}:`, error);
      setDeleteYearStatusMessage(t.deleteYearFailed.replace('{year}', year));
      setDeleteYearStatusType('error');
    } finally {
      // Clear the status message after a delay
      setTimeout(() => {
        setDeleteYearStatusMessage(null);
        setDeleteYearStatusType(null);
      }, appConfig.settingsScreen.updateStatusTimeoutMs);
    }
  }, [t, fetchAvailableYears]);

  // Fetch last checked time and available years when the screen focuses
  useFocusEffect(
    useCallback(() => {
      // Define the async function inside the callback
      const loadData = async () => {
        await fetchLastCheckedTime();
        await fetchAvailableYears();
      };
      // Call the async function
      loadData();
      // No cleanup needed, so return undefined
      return () => {};
    }, [fetchLastCheckedTime, fetchAvailableYears]) // Dependency array includes the memoized fetch functions
  );

  // Toggle language
  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'or' : 'en');
  };

  // Set theme
  const handleSetTheme = (newTheme: 'light' | 'dark' | 'system') => {
    setThemeMode(newTheme);
  };

  // Share app
  const handleShareApp = async () => {
    try {
      // Use translation keys if they exist, otherwise fallback to hardcoded strings with config URL
      const shareMessageEn = t.shareMessageEn || `Download the Odia Calendar App: ${appConfig.share.appUrl}`;
      const shareMessageOr = t.shareMessageOr || `ଓଡ଼ିଆ କ୍ୟାଲେଣ୍ଡର ଆପ୍ ଡାଉନଲୋଡ କରନ୍ତୁ: ${appConfig.share.appUrl}`;

      await Share.share({
        message: language === 'or' ? shareMessageOr : shareMessageEn,
      });
    } catch (error) {
      if (__DEV__) {
        console.error('Error sharing app:', error);
      }
      // Optionally log to Sentry
      // Sentry.captureException(error);
    }
  };

  // Request In-App Review (Settings screen - always opens store directly)
  const handleRequestReview = async () => {
    try {
      const requestSuccessful = await reviewService.openStoreForReview();
      if (!requestSuccessful) {
        Alert.alert(t.error, t.cannotOpenStore);
      }
    } catch (error) {
      Sentry.captureException(error);
      Alert.alert(t.error, t.reviewError);
    }
  };

  // More apps
  const handleMoreApps = () => {
    // Use appConfig for developer store URLs
    const developerUrl = Platform.select({
      ios: appConfig.store.iosDeveloperUrl,
      android: appConfig.store.androidDeveloperUrl,
      default: appConfig.store.androidDeveloperUrl,
    });

    Linking.canOpenURL(developerUrl).then((supported) => {
      if (supported) {
        Linking.openURL(developerUrl);
      } else {
        // Handle case where the URL can't be opened (e.g., invalid config)
        Alert.alert(t.error, t.cannotOpenStore); // Reuse existing translation
        if (__DEV__) {
          console.warn(`Could not open developer URL: ${developerUrl}`);
        }
      }
    });
  };

  // Test Sentry (only in development)
  const handleTestSentry = () => {
    // Send a test message to Sentry
    Sentry.captureMessage('Test message from Settings screen');

    // Send a test error to Sentry
    try {
      // Intentionally throw an error
      throw new Error('Test error from Settings screen');
    } catch (error) {
      Sentry.captureException(error);
    }

    // Show confirmation to the user
    alert('Test events sent to Sentry!');
  };

  // Open About Us page
  const handleOpenAboutUs = () => {
    const aboutUsUrl = 'https://sites.google.com/view/odia-calendar-1/odia-calendar-lite-about-us';

    Linking.canOpenURL(aboutUsUrl).then((supported) => {
      if (supported) {
        Linking.openURL(aboutUsUrl);
      } else {
        Alert.alert(t.error, t.cannotOpenStore);
        if (__DEV__) {
          console.warn(`Could not open About Us URL: ${aboutUsUrl}`);
        }
      }
    });
  };

  // Open Privacy Policy page
  const handleOpenPrivacyPolicy = () => {
    const privacyPolicyUrl = 'https://sites.google.com/view/odia-calendar-1/privacy-policy-odia-calendar-lite';

    Linking.canOpenURL(privacyPolicyUrl).then((supported) => {
      if (supported) {
        Linking.openURL(privacyPolicyUrl);
      } else {
        Alert.alert(t.error, t.cannotOpenStore);
        if (__DEV__) {
          console.warn(`Could not open Privacy Policy URL: ${privacyPolicyUrl}`);
        }
      }
    });
  };

  // Handle manual update check
  const handleManualUpdateCheck = async () => {
    setIsCheckingForUpdates(true);
    setUpdateStatusMessage(null); // Clear previous message
    setUpdateStatusType(null);

    try {
      // First check if network is available
      const isNetworkAvailable = await ensureNetworkConnection();

      if (!isNetworkAvailable) {
        // Show network error toast if offline
        showNetworkError({
          message: t.internetRequiredForUpdates,
          action: {
            label: t.retry,
            onPress: handleManualUpdateCheck
          }
        });

        // Also set status message in the UI
        setUpdateStatusMessage(t.noInternetConnection);
        setUpdateStatusType('error');
        return;
      }

      // If network is available, proceed with update check
      const affectedMonths = await syncService.triggerManualUpdate();
      const success = affectedMonths.length > 0;

      if (success) {
        setUpdateStatusMessage(t.updateSuccessful);
        setUpdateStatusType('success');
        // Invalidate cache if update was successful
        dataCoordinator.invalidateCacheForMonths(affectedMonths);
      } else {
        setUpdateStatusMessage(t.updateNoData);
        setUpdateStatusType('info');
      }
    } catch (error) {
      if (__DEV__) {
        console.error('Manual update check failed:', error);
      }
      setUpdateStatusMessage(t.updateFailed);
      setUpdateStatusType('error');
    } finally {
      setIsCheckingForUpdates(false);
      // Re-fetch the timestamp after check completes
      fetchLastCheckedTime();
      // Optionally clear the message after a delay using appConfig
      setTimeout(() => {
        setUpdateStatusMessage(null);
        setUpdateStatusType(null);
      }, appConfig.settingsScreen.updateStatusTimeoutMs);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: activeTheme.background }]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={activeTheme.background}
      />

      <SafeAreaView style={styles.safeArea} edges={['bottom']}>
        <ScrollView style={styles.scrollView}>
          {/* Data & Sync Section - Moved to top as most important */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: activeTheme.text }]}>
              {t.dataSync}
            </Text>

            <View style={[styles.card, { backgroundColor: activeTheme.card }]}>
              {/* Auto Update Switch */}
              <View style={styles.settingItem}>
                <View>
                  <View style={styles.settingLabelContainer}>
                    <DownloadCloud size={20} color={activeTheme.text} />
                    <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                      {t.autoUpdate}
                    </Text>
                  </View>
                  <Text style={[styles.settingDescription, { color: activeTheme.subtext }]}>
                    {t.autoUpdateDesc}
                  </Text>
                </View>
                <Switch
                  value={autoUpdateEnabled}
                  onValueChange={toggleAutoUpdate}
                  trackColor={{ false: '#767577', true: activeTheme.primary + '70' }}
                  thumbColor={autoUpdateEnabled ? activeTheme.primary : '#f4f3f4'}
                />
              </View>

              {/* Divider */}
              <View style={[styles.divider, { backgroundColor: activeTheme.border }]} />

              {/* Wi-Fi Only Switch */}
              <View style={[styles.settingItem, !autoUpdateEnabled && styles.disabledSetting]}>
                <View>
                  <View style={styles.settingLabelContainer}>
                    <Wifi size={20} color={!autoUpdateEnabled ? activeTheme.subtext : activeTheme.text} />
                    <Text style={[styles.settingLabel, { color: !autoUpdateEnabled ? activeTheme.subtext : activeTheme.text }]}>
                      {t.wifiOnlyUpdate}
                    </Text>
                  </View>
                  <Text style={[styles.settingDescription, { color: activeTheme.subtext }]}>
                    {t.wifiOnlyUpdateDesc}
                  </Text>
                </View>
                <Switch
                  value={autoUpdateWifiOnly}
                  onValueChange={toggleWifiOnlyUpdate}
                  trackColor={{ false: '#767577', true: activeTheme.primary + '70' }}
                  thumbColor={autoUpdateWifiOnly ? activeTheme.primary : '#f4f3f4'}
                  disabled={!autoUpdateEnabled} // Disable if auto-update is off
                />
              </View>

              {/* Divider */}
              <View style={[styles.divider, { backgroundColor: activeTheme.border }]} />

              {/* Manual Check Button */}
              <TouchableOpacity
                style={[styles.actionItem, styles.updateButtonContainer]}
                onPress={handleManualUpdateCheck}
                disabled={isCheckingForUpdates}
              >
                <View style={styles.settingLabelContainer}>
                  <RefreshCw size={20} color={activeTheme.primary} />
                  <Text style={[styles.settingLabel, { color: activeTheme.primary, marginLeft: 12 }]}>
                    {isCheckingForUpdates ? t.checkingForUpdates : t.checkForUpdates}
                  </Text>
                </View>
                {isCheckingForUpdates && (
                  <ActivityIndicator size="small" color={activeTheme.primary} style={styles.activityIndicator} />
                )}
              </TouchableOpacity>

              {/* Update Status Message */}
              {updateStatusMessage && (
                <Text style={[
                  styles.updateStatusText,
                  updateStatusType === 'success' && { color: activeTheme.success },
                  updateStatusType === 'error' && { color: activeTheme.error },
                  updateStatusType === 'info' && { color: activeTheme.subtext },
                ]}>
                  {updateStatusMessage}
                </Text>
              )}

              {/* Last Checked Timestamp */}
              <Text style={[styles.lastCheckedText, { color: activeTheme.subtext }]}>
                {t.lastCheckedLabel} {lastCheckedTimestamp ?? t.neverChecked}
              </Text>
            </View>
          </View>

          {/* Data Management Section - Placed right after Data & Sync for logical grouping */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: activeTheme.text }]}>
              {t.dataManagement}
            </Text>

            <View style={[styles.card, { backgroundColor: activeTheme.card }]}>
              {/* Delete Year Data */}
              <TouchableOpacity
                style={styles.actionItem}
                onPress={() => {
                  if (availableYears.length === 0) {
                    // If no years available, show a message
                    Alert.alert(t.deleteYearData, t.noYearsAvailable);
                  } else {
                    // Show the list of available years
                    Alert.alert(
                      t.deleteYearData,
                      `${t.availableYears}\n${availableYears.join(', ')}`,
                      [
                        ...availableYears.map(year => ({
                          text: t.deleteYear.replace('{year}', year),
                          onPress: () => {
                            // Show confirmation dialog
                            Alert.alert(
                              t.selectYearToDelete,
                              t.deleteYearConfirm.replace('{year}', year),
                              [
                                {
                                  text: t.cancel,
                                  style: 'cancel' as const
                                },
                                {
                                  text: t.deleteYear.replace('{year}', year),
                                  style: 'destructive' as const,
                                  onPress: () => handleDeleteYear(year)
                                }
                              ]
                            );
                          },
                          style: 'destructive' as const
                        })),
                        {
                          text: t.cancel,
                          style: 'cancel' as const,
                          onPress: () => {}
                        }
                      ]
                    );
                  }
                }}
              >
                <View style={styles.settingLabelContainer}>
                  <Trash2 size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {t.deleteYearData}
                  </Text>
                </View>
                <Text style={[styles.settingDescription, { color: activeTheme.subtext, marginTop: 4, marginLeft: 32 }]}>
                  {t.deleteYearDataDesc}
                </Text>
              </TouchableOpacity>

              {/* Delete Year Status Message */}
              {deleteYearStatusMessage && (
                <Text style={[
                  styles.updateStatusText,
                  deleteYearStatusType === 'success' && { color: activeTheme.success },
                  deleteYearStatusType === 'error' && { color: activeTheme.error },
                ]}>
                  {deleteYearStatusMessage}
                </Text>
              )}
            </View>
          </View>

          {/* Appearance Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: activeTheme.text }]}>
              {translations[currentLanguage].appearance}
            </Text>

            {/* Theme Selection */}
            <View style={[styles.card, { backgroundColor: activeTheme.card }]}>
              <View style={styles.settingItem}>
                <View style={styles.settingLabelContainer}>
                  <Sun size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {translations[currentLanguage].theme}
                  </Text>
                </View>
              </View>

              <View style={styles.themeOptions}>
                <TouchableOpacity
                  style={[
                    styles.themeOption,
                    themeMode === 'light' && { backgroundColor: activeTheme.primary + '30' },
                  ]}
                  onPress={() => handleSetTheme('light')}
                >
                  <Text
                    style={[
                      styles.themeOptionText,
                      { color: activeTheme.text },
                      themeMode === 'light' && { color: activeTheme.primary, fontWeight: 'bold' },
                    ]}
                  >
                    {translations[currentLanguage].lightTheme}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.themeOption,
                    themeMode === 'dark' && { backgroundColor: activeTheme.primary + '30' },
                  ]}
                  onPress={() => handleSetTheme('dark')}
                >
                  <Text
                    style={[
                      styles.themeOptionText,
                      { color: activeTheme.text },
                      themeMode === 'dark' && { color: activeTheme.primary, fontWeight: 'bold' },
                    ]}
                  >
                    {translations[currentLanguage].darkTheme}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.themeOption,
                    themeMode === 'system' && { backgroundColor: activeTheme.primary + '30' },
                  ]}
                  onPress={() => handleSetTheme('system')}
                >
                  <Text
                    style={[
                      styles.themeOptionText,
                      { color: activeTheme.text },
                      themeMode === 'system' && { color: activeTheme.primary, fontWeight: 'bold' },
                    ]}
                  >
                    {translations[currentLanguage].systemTheme}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Language Selection */}
            <View style={[styles.card, { backgroundColor: activeTheme.card }]}>
              <View style={styles.settingItem}>
                <View style={styles.settingLabelContainer}>
                  <Globe size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {translations[currentLanguage].language}
                  </Text>
                </View>
                <Switch
                  value={language === 'or'}
                  onValueChange={toggleLanguage}
                  trackColor={{ false: '#767577', true: activeTheme.primary + '70' }}
                  thumbColor={language === 'or' ? activeTheme.primary : '#f4f3f4'}
                />
              </View>
              <Text style={[styles.languageIndicator, { color: activeTheme.subtext }]}>
                {language === 'en' ? 'English' : 'ଓଡ଼ିଆ'}
              </Text>
            </View>
          </View>

          {/* Privacy Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: activeTheme.text }]}>
              {currentLanguage === 'or' ? 'ଗୋପନୀୟତା' : 'Privacy'}
            </Text>

            <View style={[styles.card, { backgroundColor: activeTheme.card }]}>
              <View style={styles.settingItem}>
                <View>
                  <View style={styles.settingLabelContainer}>
                    <Bug size={20} color={activeTheme.text} />
                    <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                      {translations[currentLanguage].errorReporting}
                    </Text>
                  </View>
                  <Text style={[styles.settingDescription, { color: activeTheme.subtext }]}>
                    {translations[currentLanguage].errorReportingDesc}
                  </Text>
                </View>
                <Switch
                  value={errorReportingEnabled}
                  onValueChange={toggleErrorReporting}
                  trackColor={{ false: '#767577', true: activeTheme.primary + '70' }}
                  thumbColor={errorReportingEnabled ? activeTheme.primary : '#f4f3f4'}
                />
              </View>
            </View>
          </View>

          {/* Settings screen banner ad */}
          <BannerAd placement="settingsScreen" />

          {/* App Actions Section - Moved lower as less frequently used */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: activeTheme.text }]}>
              {currentLanguage === 'or' ? 'ଆପ୍ କାର୍ଯ୍ୟ' : 'App Actions'}
            </Text>

            <View style={[styles.card, { backgroundColor: activeTheme.card }]}>
              {/* Share App */}
              <TouchableOpacity style={styles.actionItem} onPress={handleShareApp}>
                <View style={styles.settingLabelContainer}>
                  <Share2 size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {translations[currentLanguage].shareApp}
                  </Text>
                </View>
              </TouchableOpacity>

              {/* Rate & Review */}
              <View style={[styles.divider, { backgroundColor: activeTheme.border }]} />
              <TouchableOpacity style={styles.actionItem} onPress={handleRequestReview}>
                <View style={styles.settingLabelContainer}>
                  <Star size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {translations[currentLanguage].rateReview}
                  </Text>
                </View>
              </TouchableOpacity>

              {/* More Apps */}
              <View style={[styles.divider, { backgroundColor: activeTheme.border }]} />
              <TouchableOpacity style={styles.actionItem} onPress={handleMoreApps}>
                <View style={styles.settingLabelContainer}>
                  <Package size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {translations[currentLanguage].moreApps}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* About Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: activeTheme.text }]}>
              {t.about}
            </Text>

            <View style={[styles.card, { backgroundColor: activeTheme.card }]}>
              {/* Version */}
              <View style={styles.settingItem}>
                <View style={styles.settingLabelContainer}>
                  <Info size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {t.version}
                  </Text>
                </View>
                <Text style={[styles.versionText, { color: activeTheme.subtext }]}>1.0.0</Text>
              </View>

              {/* About Us */}
              <View style={[styles.divider, { backgroundColor: activeTheme.border }]} />
              <TouchableOpacity style={styles.actionItem} onPress={handleOpenAboutUs}>
                <View style={styles.settingLabelContainer}>
                  <Users size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {t.aboutUs}
                  </Text>
                </View>
              </TouchableOpacity>

              {/* Privacy Policy */}
              <View style={[styles.divider, { backgroundColor: activeTheme.border }]} />
              <TouchableOpacity style={styles.actionItem} onPress={handleOpenPrivacyPolicy}>
                <View style={styles.settingLabelContainer}>
                  <FileText size={20} color={activeTheme.text} />
                  <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                    {t.privacyPolicy}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Developer Section - Only visible in development */}
          {__DEV__ && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: activeTheme.text }]}>
                Developer Options
              </Text>

              <View style={[styles.card, { backgroundColor: activeTheme.card }]}>
                {/* Test Sentry */}
                <TouchableOpacity style={styles.actionItem} onPress={handleTestSentry}>
                  <View style={styles.settingLabelContainer}>
                    <Bug size={20} color={activeTheme.text} />
                    <Text style={[styles.settingLabel, { color: activeTheme.text }]}>
                      Test Sentry Integration
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* Add padding at the bottom to ensure content isn't hidden behind the sticky ad */}
          <View style={{ height: 70 }} />
        </ScrollView>

        {/* Sticky Banner Ad - always visible at the bottom */}
        <StickyBannerAd />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  card: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  actionItem: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  divider: {
    height: 1,
    marginHorizontal: 16,
  },
  settingLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingLabel: {
    fontSize: 16,
    marginLeft: 12,
  },
  languageIndicator: {
    fontSize: 14,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  themeOptions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  themeOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  themeOptionText: {
    fontSize: 14,
  },
  versionText: {
    fontSize: 14,
  },
  settingDescription: {
    fontSize: 12,
    marginLeft: 32, // Align with icon + margin
    marginTop: 4,
    marginRight: 40, // Ensure text doesn't overlap switch
  },
  disabledSetting: {
    opacity: 0.6, // Visually indicate disabled state
  },
  updateButtonContainer: {
    paddingVertical: 12, // Slightly less padding for button feel
  },
  activityIndicator: {
    marginLeft: 10,
  },
  updateStatusText: {
    fontSize: 12,
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingBottom: 4, // Reduced padding below message
    marginTop: -4, // Reduce space above message if button has padding
  },
  lastCheckedText: {
    fontSize: 12,
    textAlign: 'center',
    paddingBottom: 16, // Add padding below timestamp
    marginTop: 4, // Add some space above timestamp
    // Color is now applied inline using activeTheme
  },
});

// Add success/error colors to constants/colors.tsx if they don't exist
// Example:
// success: '#28a745', // Green
// error: '#dc3545', // Red
