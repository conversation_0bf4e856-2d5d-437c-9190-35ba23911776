import React, { useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, StatusBar, TouchableOpacity, Text, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { useRouter } from 'expo-router';
import { Moon, Sun, Globe } from 'lucide-react-native';
import { useDarkMode, useLanguage, useSettingsStore } from '@/store/settings-store';
import { useCalendarStore } from '@/store/calendar-store';
import { useReminderStore } from '@/store/reminder-store';
import { useCalendarDataContext } from '@/components/providers/CalendarDataProvider';
import { syncService } from '@/services/sync-service';
import { useOdiaMonths } from '@/hooks/useCalendarData';
import SwipeableCalendarView from '@/components/calendar/SwipeableCalendarView';
import DateDetailsModal from '@/components/calendar/DateDetailsModal';
import FestivalsList from '@/components/calendar/FestivalsList';
import MarriageDatesList from '@/components/calendar/MarriageDatesList';
import BrataGharaDatesList from '@/components/calendar/BrataGharaDatesList';
import AppMenu from '@/components/common/AppMenu';
import BannerAd from '@/components/common/BannerAd';
import StickyBannerAd from '@/components/common/StickyBannerAd';
import UpdatePromptModal from '@/components/common/UpdatePromptModal'; // Added UpdatePromptModal
import YearAvailabilityBanner from '@/components/calendar/YearAvailabilityBanner'; // Import the new banner
import FloatingAddReminderButton from '@/components/calendar/FloatingAddReminderButton';
import { useYearAvailabilityStatus } from '@/hooks/useYearAvailabilityStatus'; // Import the new hook
import colors from '@/constants/colors';

export default function CalendarScreen() {
  const [dateDetailsVisible, setDateDetailsVisible] = useState(false);
  const [selectedDateString, setSelectedDateString] = useState<string | null>(null);
  const router = useRouter();
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const { setThemeMode, setLanguage } = useSettingsStore();
  const { selectedYear, selectedMonth, setShowUpdatePrompt } = useCalendarStore();
  const { selectReminder } = useReminderStore();
  const theme = isDarkMode ? colors.dark : colors.light;
  const { isInitialized } = useCalendarDataContext();

  // Get Odia months from actual calendar data (utilizes cache)
  const { odiaMonths: odiaMonthDisplay, loading: odiaMonthsLoading } = useOdiaMonths(selectedYear, selectedMonth);

  // Use the new hook to check year availability
  const {
    yearStatus,
    isLoadingYearStatus,
    triggerYearDownload,
    // checkYearStatus // We might not need manual check trigger here, hook handles it on year change
  } = useYearAvailabilityStatus(selectedYear);

  // Check for general pending updates when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      const checkPendingUpdate = async () => {
        const pending = await syncService.isUpdatePending();
        if (pending) {
          setShowUpdatePrompt(true);
        }
      };
      checkPendingUpdate();
    }, [setShowUpdatePrompt])
  );

  // Handle date press
  const handleDatePress = (dateString: string) => {
    setSelectedDateString(dateString);
    setDateDetailsVisible(true);
  };

  // Close date details modal
  const closeDateDetails = () => {
    setDateDetailsVisible(false);
  };

  // Toggle theme
  const toggleTheme = () => {
    setThemeMode(isDarkMode ? 'light' : 'dark');
  };

  // Toggle language
  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'or' : 'en');
  };

  // Handle add reminder button press
  const handleAddReminder = useCallback(() => {
    selectReminder(null); // Clear any selected reminder
    router.push('/add-reminder');
  }, [selectReminder, router]);

  // Show loading indicator until initialized
  if (!isInitialized) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
      </View>
    );
  }

  // TODO: Optionally handle the 'error' state from useCalendarDataContext if needed

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.background}
      />

      <SafeAreaView style={styles.safeArea} edges={['top']}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.appTitleContainer}>
              <View style={[styles.appTitleDot, { backgroundColor: theme.primary }]} />
              <Text
                style={[
                  styles.monthDisplay,
                  {
                    color: theme.primary,
                    fontWeight: 'bold'
                  }
                ]}
              >
                {odiaMonthsLoading ? '...' : odiaMonthDisplay}
              </Text>
            </View>

            <View style={styles.headerControls}>
              {/* Theme Toggle */}
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: isDarkMode ? theme.themeDark : theme.themeLight }]} // Use new theme colors
                onPress={toggleTheme}
              >
                {isDarkMode ? (
                  <Moon size={18} color={theme.text} />
                ) : (
                  <Sun size={18} color={theme.text} />
                )}
              </TouchableOpacity>

              {/* Language Toggle */}
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: theme.language }]} // Use new language color
                onPress={toggleLanguage}
              >
                <Globe size={18} color={theme.text} />
                <Text style={[styles.languageText, { color: theme.text }]}>
                  {language === 'en' ? 'EN' : 'OR'}
                </Text>
              </TouchableOpacity>

              <AppMenu />
            </View>
          </View>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Render Year Availability Banner */}
          <YearAvailabilityBanner
            status={yearStatus}
            year={selectedYear}
            isLoading={isLoadingYearStatus} // Pass loading state from the hook
            onDownloadPress={triggerYearDownload} // Pass the download trigger function
          />

          {!dateDetailsVisible && (
            <View style={styles.calendarContainer}>
              {/* Use the new SwipeableCalendarView component */}
              <SwipeableCalendarView
                onDatePress={handleDatePress}
                dateDetailsVisible={dateDetailsVisible}
              />
            </View>
          )}

          {/* Calendar screen banner ad */}
          <BannerAd placement="calendarScreen" />

          {/* Only show lists if data is locally available */}
          {yearStatus === 'AVAILABLE_LOCALLY' && (
            <>
              {/* Festivals section with floating button */}
              <View style={styles.festivalsSection}>
                <FestivalsList />
                {/* Floating Add Reminder Button positioned beside festival header */}
                <View style={styles.floatingButtonContainer}>
                  <FloatingAddReminderButton onPress={handleAddReminder} />
                </View>
              </View>
              <MarriageDatesList />
              <BrataGharaDatesList />
            </>
          )}


          {/* Add padding at the bottom to ensure content isn't hidden behind the sticky ad */}
          {/* <MarriageDatesList /> REMOVED DUPLICATE */}

          {/* Add padding at the bottom to ensure content isn't hidden behind the sticky ad */}
          <View style={{ height: 70 }} />
        </ScrollView>
      </SafeAreaView>

      {/* Sticky Banner Ad - always visible at the bottom */}
      <StickyBannerAd />

      {/* Render the Update Prompt Modal */}
      <UpdatePromptModal />

      <DateDetailsModal
        visible={dateDetailsVisible}
        dateString={selectedDateString}
        onClose={closeDateDetails}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  appTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appTitleDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  appTitle: {
    height: 24,
    justifyContent: 'space-between',
  },
  appTitleLine: {
    height: 8,
    width: '80%',
    borderRadius: 4,
  },
  monthDisplay: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  languageText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  calendarContainer: {
    paddingHorizontal: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  festivalsSection: {
    position: 'relative',
  },
  floatingButtonContainer: {
    position: 'absolute',
    top: 16, // Align with festival header
    right: 16,
    zIndex: 1000,
  },
});
