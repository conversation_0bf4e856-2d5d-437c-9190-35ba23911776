import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import React, { useEffect, useState } from "react";
import { useColorScheme, TouchableOpacity, Text, StyleSheet, Platform, View } from "react-native";
import { setIsSystemDarkTheme } from "@/store/settings-store";
import { ErrorBoundary } from "./error-boundary";
import { AdMobProvider, useAdMob } from "@/components/common/AdMobProvider";
import { LogProvider, useComponentLogger, initializeLogging } from "@/utils/logging-sentry";
import SentryLogViewer from "@/components/common/SentryLogViewer";
import { CalendarDataProvider } from "@/components/providers/CalendarDataProvider";
import { mark, measure } from "@/utils/performanceMonitor";
import * as Sentry from '@sentry/react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NetworkStatusProvider } from '@/components/providers/NetworkStatusProvider';
import { reminderService } from '@/services/reminder-service';

Sentry.init({
  dsn: 'https://<EMAIL>/4509230712487936',

  // Session Replay disabled to reduce app size
  // replaysSessionSampleRate: 0.1,
  // replaysOnErrorSampleRate: 1,
  // integrations: [Sentry.mobileReplayIntegration()],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

export const unstable_settings = {
  initialRouteName: "(tabs)",
};

// We'll use refs to store measurement IDs to prevent re-renders
// This is outside the component to avoid being recreated on each render

// Prevent the splash screen from auto-hiding before asset loading is complete.
// We use a safer approach that doesn't rely on the SplashModule directly
const preventSplashAutoHide = async () => {
  try {
    // Check if the SplashScreen module is available
    if (SplashScreen && typeof SplashScreen.preventAutoHideAsync === 'function') {
      await SplashScreen.preventAutoHideAsync();
    } else if (__DEV__) {
      console.warn('SplashScreen.preventAutoHideAsync is not available');
    }
  } catch (error) {
    // This is non-critical, so just log it
    console.warn('Error with splash screen initialization:', error);

    // Fallback approach for production builds
    try {
      // Try alternative method if available
      if (SplashScreen && (SplashScreen as any).preventAutoHideAsync) {
        await (SplashScreen as any).preventAutoHideAsync();
      }
    } catch (fallbackError) {
      console.warn('Fallback splash screen initialization also failed:', fallbackError);
    }
  }
};

// Call the function
preventSplashAutoHide();

// Initialize performance measurement outside of React's render cycle
let hasInitializedPerformance = false;

// Only run this once, outside of React's render cycle
if (__DEV__ && !hasInitializedPerformance) {
  hasInitializedPerformance = true;
  // Use the new mark function
  mark('AppInitialize_Start');
  // appInitializeId is no longer needed for this pattern
}

// Import the supabase proxy for early initialization
// Just accessing it will trigger the initialization via the proxy pattern
import { supabase } from '@/services/supabase-client';

// Just accessing the supabase object will trigger initialization via the proxy
// This is intentional and happens before any React components are mounted
void supabase;

// Note: Reminder service initialization moved to CalendarDataProvider
// to centralize all database-related initialization

const RootLayout = Sentry.wrap(function RootLayout() {
  // Use refs to store measurement IDs to prevent re-renders
  const performanceInitialized = React.useRef(false);

  // Initialize fonts
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });

  const colorScheme = useColorScheme();
  // Keep the state for the log viewer, but it will only be opened programmatically
  const [logViewerVisible, setLogViewerVisible] = useState(false);
  // Removed toggleDashboard usage from usePerformanceStore

  // Initialize performance measurements once
  useEffect(() => {
    if (__DEV__ && !performanceInitialized.current) {
      performanceInitialized.current = true;
      // Use the new mark function
      mark('FontsLoad_Start');
      // fontsLoadIdRef is no longer needed for this pattern
    }
  }, []);

  // Initialize logging system and performance monitoring
  useEffect(() => {
    // Initialize the logging system
    try {
      initializeLogging();
    } catch (error) {
      if (__DEV__) {
        console.error('Failed to initialize logging system:', error);
      }
    }

    // Get component logger
    const componentLogger = useComponentLogger('RootLayout');

    // Log app startup
    componentLogger.info('App started', {
      environment: __DEV__ ? 'development' : 'production',
      colorScheme
    });

    // Handle performance measurements safely
    if (__DEV__) {
      // End app initialization measurement (only once)
      // Use the new measure function
      measure('AppStartup: Initialize', 'AppInitialize_Start');
      // appInitializeId is no longer needed

      // Measure store hydration (only once)
      if (__DEV__) { // Ensure this block only runs in DEV
        mark('StoreHydrate_Start');
        // storeHydrateId is no longer needed

        // End store hydration after a short delay
        setTimeout(() => {
          measure('AppStartup: Store Hydrate', 'StoreHydrate_Start');
          // Measure first render (only once)
          mark('FirstRender_Start');
          // firstRenderId is no longer needed

          // Schedule first interaction measurement
          setTimeout(() => {
            measure('AppStartup: First Render', 'FirstRender_Start');
            // Record first interaction
            mark('FirstInteraction_Start');
            // interactionId is no longer needed
            setTimeout(() => {
              measure('AppStartup: First Interaction', 'FirstInteraction_Start');
            }, 100);
          }, 100);
        }, 0);
      }
    }

    // Define handlers outside the conditional so they're in scope for cleanup
    let errorHandler: ((event: ErrorEvent) => void) | undefined;
    let rejectionHandler: ((event: PromiseRejectionEvent) => void) | undefined;
    let originalConsoleError: typeof console.error | undefined;

    // Add global error handlers in development
    if (__DEV__) {
      // Override console.error in development
      originalConsoleError = console.error;
      console.error = (...args) => {
        // Check if this is a message from our logger to prevent infinite recursion
        const firstArg = args[0]?.toString() || '';
        if (firstArg.includes('[DEBUG]') ||
            firstArg.includes('[INFO]') ||
            firstArg.includes('[WARN]') ||
            firstArg.includes('[ERROR]')) {
          // This is coming from our logger, just pass through to original
          originalConsoleError!.apply(console, args);
        } else {
          // This is a regular console.error call, log it to our system
          try {
            // Use a try-catch to ensure we don't crash if there's an issue with logging
            componentLogger.error('Console error', {
              args: args.map(arg => {
                try {
                  return typeof arg === 'object' ? JSON.stringify(arg) : String(arg);
                } catch (e) {
                  return 'Error stringifying argument';
                }
              })
            });
          } catch (e) {
            // If our logger throws, just use the original console.error
            originalConsoleError!.apply(console, ['Error in logger:', e]);
          }

          // Then call original
          originalConsoleError!.apply(console, args);
        }
      };

      // Add global error event listener
      errorHandler = (event: ErrorEvent) => {
        componentLogger.error('Unhandled error', {
          message: event.error?.message || event.message,
          stack: event.error?.stack,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        });
      };

      // Add global promise rejection handler
      rejectionHandler = (event: PromiseRejectionEvent) => {
        componentLogger.error('Unhandled promise rejection', {
          reason: event.reason?.message || String(event.reason)
        });
      };

      // Add event listeners for browser environments
      // Only add these in web platform, not in native
      if (typeof window !== 'undefined' && Platform.OS === 'web') {
        try {
          window.addEventListener('error', errorHandler);
          window.addEventListener('unhandledrejection', rejectionHandler);
        } catch (error) {
          if (__DEV__) {
            console.warn('Could not add window event listeners:', error);
          }
        }
      }
    }

    // Always return a cleanup function
    return () => {
      // Restore original console.error if we overrode it
      if (originalConsoleError && __DEV__) {
        console.error = originalConsoleError;
      }

      // Clean up event listeners if they were added
      if (typeof window !== 'undefined' && Platform.OS === 'web' && __DEV__) {
        try {
          if (errorHandler) {
            window.removeEventListener('error', errorHandler);
          }
          if (rejectionHandler) {
            window.removeEventListener('unhandledrejection', rejectionHandler);
          }
        } catch (error) {
          // Ignore cleanup errors
        }
      }
    };
  }, []);

  // Update system theme state when color scheme changes
  useEffect(() => {
    setIsSystemDarkTheme(colorScheme === 'dark');

    // Get component logger
    const componentLogger = useComponentLogger('RootLayout');
    componentLogger.debug('Color scheme changed', { colorScheme });
  }, [colorScheme]);

  useEffect(() => {
    if (error) {
      // Get component logger
      const componentLogger = useComponentLogger('RootLayout');
      componentLogger.error('Font loading error', { error });
      throw error;
    }
  }, [error]);

  useEffect(() => {
    if (loaded) {
      // Get component logger
      const componentLogger = useComponentLogger('RootLayout');
      componentLogger.debug('Fonts loaded, hiding splash screen');

      // Handle performance measurements safely
      if (__DEV__) {
        // End fonts loading measurement (only once)
        measure('AppStartup: Fonts Load', 'FontsLoad_Start');
        // fontsLoadIdRef is no longer needed

        // Start measuring assets loading (only once)
        mark('AssetsLoad_Start');
        // assetsLoadId is no longer needed
      }

      // Handle SplashScreen safely
      const hideSplashScreen = async () => {
        try {
          componentLogger.info('Attempting to hide splash screen...');

          // Check if the SplashScreen module is available
          if (SplashScreen && typeof SplashScreen.hideAsync === 'function') {
            componentLogger.info('Using SplashScreen.hideAsync method');
            await SplashScreen.hideAsync();
            componentLogger.info('SplashScreen.hideAsync completed successfully');
          } else {
            componentLogger.warn('SplashScreen.hideAsync is not available');

            // Try alternative approaches for production builds
            if (SplashScreen) {
              // Try with any casting to access potentially different method names
              const splashAny = SplashScreen as any;
              componentLogger.info('Trying alternative splash screen methods');

              // Try different method variations that might exist
              if (typeof splashAny.hideAsync === 'function') {
                componentLogger.info('Using splashAny.hideAsync method');
                await splashAny.hideAsync();
                componentLogger.info('splashAny.hideAsync completed successfully');
              } else if (typeof splashAny.hide === 'function') {
                componentLogger.info('Using splashAny.hide method');
                await splashAny.hide();
                componentLogger.info('splashAny.hide completed successfully');
              } else if (typeof splashAny.internalMaybeHideAsync === 'function') {
                componentLogger.info('Using splashAny.internalMaybeHideAsync method');
                await splashAny.internalMaybeHideAsync();
                componentLogger.info('splashAny.internalMaybeHideAsync completed successfully');
              } else {
                componentLogger.warn('No suitable splash screen hiding method found');
              }
            }
          }
        } catch (error) {
          // Log but don't throw - this is non-critical
          componentLogger.error('Failed to hide splash screen', {
            error: String(error),
            stack: error instanceof Error ? error.stack : 'No stack trace'
          });

          // Last resort fallback for production
          try {
            componentLogger.info('Attempting last resort native module approach');
            // Try to access native modules directly if available
            const { NativeModules } = require('react-native');
            if (NativeModules.SplashScreen && typeof NativeModules.SplashScreen.hide === 'function') {
              componentLogger.info('Using NativeModules.SplashScreen.hide method');
              NativeModules.SplashScreen.hide();
              componentLogger.info('NativeModules.SplashScreen.hide completed successfully');
            } else {
              componentLogger.warn('NativeModules.SplashScreen.hide not available');
            }
          } catch (fallbackError) {
            componentLogger.error('Fallback splash screen hiding also failed', {
              error: String(fallbackError),
              stack: fallbackError instanceof Error ? fallbackError.stack : 'No stack trace'
            });
          }
        } finally {
          componentLogger.info('Splash screen hiding process completed (success or failure)');
          // End assets loading measurement safely
          if (__DEV__) {
            measure('AppStartup: Assets Load', 'AssetsLoad_Start');
          }
        }
      };

      // Call the function
      hideSplashScreen();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ErrorBoundary>
        <LogProvider componentName="RootLayout">
          <AdMobProvider>
            {/* LogViewer modal (only in development) */}
            {__DEV__ && (
              <SentryLogViewer
                visible={logViewerVisible}
                onClose={() => setLogViewerVisible(false)}
              />
            )}

            {/* Removed Performance Dashboard Component */}

            {/* Wrap RootLayoutNav with providers */}
            <NetworkStatusProvider>
              <CalendarDataProvider>
                <RootLayoutNav />
              </CalendarDataProvider>
            </NetworkStatusProvider>
          </AdMobProvider>
        </LogProvider>
      </ErrorBoundary>
    </GestureHandlerRootView>
  );
});

// Remove CalendarDataProvider from here
function RootLayoutNav() {
  const { showInterstitial } = useAdMob();
  const sessionTimer = React.useRef<NodeJS.Timeout | null>(null);

  // Set up a timer to show an ad after a certain session duration
  useEffect(() => {
    // Clear any existing timer
    if (sessionTimer.current) {
      clearTimeout(sessionTimer.current);
    }

    // Set a new timer for session duration ad
    const sessionDuration = 180000; // 3 minutes (from appConfig.ads.interstitialPlacement.appSession.minSessionTime)
    sessionTimer.current = setTimeout(async () => {
      await showInterstitial('appSession');
    }, sessionDuration);

    // Clean up on unmount
    return () => {
      if (sessionTimer.current) {
        clearTimeout(sessionTimer.current);
      }
    };
  }, [showInterstitial]);

  return (
    // <CalendarDataProvider> // No longer needed here
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="modal" options={{ presentation: 'modal' }} />
        <Stack.Screen name="add-reminder" options={{ headerShown: true }} />
        <Stack.Screen name="reminder-details" options={{ headerShown: true }} />
      </Stack>
    // </CalendarDataProvider> // No longer needed here
  );
}

// Removed styles for development tools

export default RootLayout;
