import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import { useReminderStore } from '@/store/reminder-store';
import { UserReminder } from '@/types/reminders';
import colors from '@/constants/colors';
import { formatDate, formatTime } from '../src/utils/date-formatter';
import { Bell, Calendar, Clock, Edit, Trash2, AlertCircle, Download } from 'lucide-react-native';
import { checkDataAvailabilityForTithi } from '@/utils/tithi-calculator';
import { useScheduledNotifications } from '@/hooks/useReminderData';
import { useYearAvailabilityStatus } from '@/hooks/useYearAvailabilityStatus';
import { useNetworkStatusContext } from '@/components/providers/NetworkStatusProvider';

export default function ReminderDetailsScreen() {
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const t = translations[language];
  const theme = isDarkMode ? colors.dark : colors.light;
  const router = useRouter();
  const params = useLocalSearchParams();

  const { selectedReminder, deleteReminder, selectReminder, reminders } = useReminderStore();
  const [currentReminder, setCurrentReminder] = useState<UserReminder | null>(null);

  // Handle reminder loading from params or store
  useEffect(() => {
    const reminderId = params.reminderId ? Number(params.reminderId) : null;

    if (reminderId) {
      // Find the reminder by ID from the store
      const reminder = reminders.find(r => r.id === reminderId);
      if (reminder) {
        setCurrentReminder(reminder);
        selectReminder(reminder); // Also set in store for consistency
      } else if (reminders.length > 0) {
        // Reminders are loaded but this specific reminder not found
        router.replace('/');
      }
      // If reminders.length === 0, we're still loading, so wait
    } else if (selectedReminder) {
      // Use the selected reminder from store
      setCurrentReminder(selectedReminder);
    } else if (reminders.length > 0) {
      // Reminders are loaded but no reminder selected or found
      router.replace('/');
    }
    // If reminders.length === 0, we're still loading, so wait
  }, [params.reminderId, reminders, selectedReminder, selectReminder, router]);

  // Get scheduled notifications for this reminder
  const { notifications: scheduledNotifications, loading: notificationsLoading } = useScheduledNotifications(
    currentReminder?.id || null
  );
  const [isCheckingDataAvailability, setIsCheckingDataAvailability] = useState(false);
  const [dataAvailability, setDataAvailability] = useState<{
    combinationExists: boolean;
    latestAvailableYear: number | null;
    nextYearAvailable: boolean;
    nextYearDownloadable: boolean;
  } | null>(null);

  // Get the current year
  const currentYear = new Date().getFullYear();
  const nextYear = currentYear + 1;

  // Use the year availability hook for the next year
  const {
    yearStatus,
    isLoadingYearStatus,
    triggerYearDownload
  } = useYearAvailabilityStatus(nextYear);

  // Network status for download functionality
  const { ensureNetworkConnection, showNetworkError } = useNetworkStatusContext();

  // Check data availability if no scheduled notifications found
  useEffect(() => {
    const checkAvailability = async () => {
      if (!notificationsLoading && scheduledNotifications.length === 0 && currentReminder) {
        setIsCheckingDataAvailability(true);
        try {
          const availability = await checkDataAvailabilityForTithi(
            currentReminder.odiaMonth,
            currentReminder.paksha,
            currentReminder.tithi
          );

          setDataAvailability(availability);

          // If the combination exists but we don't have occurrences,
          // check if next year data is available for download
          if (availability.combinationExists && !availability.nextYearAvailable) {
            // Update the nextYearDownloadable flag based on the yearStatus
            if (yearStatus === 'AVAILABLE_REMOTELY') {
              setDataAvailability({
                ...availability,
                nextYearDownloadable: true
              });
            }
          }
        } catch (error) {
          console.error('Error checking data availability:', error);
        } finally {
          setIsCheckingDataAvailability(false);
        }
      } else {
        // Reset data availability if we found an occurrence
        setDataAvailability(null);
      }
    };

    checkAvailability();
  }, [notificationsLoading, scheduledNotifications, currentReminder, yearStatus]);

  // Handle edit button press
  const handleEdit = useCallback(() => {
    router.push('/add-reminder');
  }, [router]);

  // Handle delete button press
  const handleDelete = useCallback(() => {
    if (!currentReminder) return;

    Alert.alert(
      t.deleteTitle,
      t.deleteConfirm,
      [
        {
          text: language === 'or' ? t.commonCancel : 'Cancel',
          style: 'cancel'
        },
        {
          text: language === 'or' ? t.commonDelete : 'Delete',
          style: 'destructive',
          onPress: async () => {
            if (currentReminder.id) {
              const success = await deleteReminder(currentReminder.id);
              if (success) {
                router.replace('/');
              } else {
                Alert.alert(
                  t.error,
                  t.deleteFailed
                );
              }
            }
          }
        }
      ]
    );
  }, [currentReminder, deleteReminder, router, t]);

  // Handle download button press
  const handleDownload = useCallback(async () => {
    try {
      // First check if network is available
      const isNetworkAvailable = await ensureNetworkConnection();

      if (!isNetworkAvailable) {
        // Show network error toast if offline
        showNetworkError({
          message: t.internetRequiredForYearData.replace('{year}', nextYear.toString()),
          action: {
            label: t.retry,
            onPress: handleDownload
          }
        });
        return;
      }

      // Trigger the download
      const success = await triggerYearDownload(nextYear);

      if (success) {
        // If download was successful, refresh the scheduled notifications
        // The useScheduledNotifications hook will automatically refresh
        console.log('Year data downloaded successfully');
      }
    } catch (error) {
      console.error('Error downloading year data:', error);
    }
  }, [nextYear, ensureNetworkConnection, showNetworkError, triggerYearDownload, t]);

  // If no reminder is loaded, show loading
  if (!currentReminder) {
    return (
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
      </View>
    );
  }

  // Get the reminder type text
  const getReminderTypeText = () => {
    switch (currentReminder.reminderType) {
      case 'monthly_tithi':
        return t.monthlyTithi;
      case 'yearly_tithi':
        return t.yearlyTithi;
      case 'specific_date':
        return t.specificDate;
      default:
        return '';
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: t.reminderDetails,
          headerRight: () => (
            <View style={styles.headerButtons}>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleEdit}
              >
                <Edit size={20} color={theme.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleDelete}
              >
                <Trash2 size={20} color={theme.error} />
              </TouchableOpacity>
            </View>
          ),
        }}
      />

      <ScrollView
        style={[styles.container, { backgroundColor: theme.background }]}
        contentContainerStyle={styles.content}
      >
        {/* Title and Description */}
        <View style={[styles.card, { backgroundColor: theme.cardBackground }]}>
          <Text style={[styles.title, { color: theme.text }]}>
            {currentReminder.title}
          </Text>

          {currentReminder.description && (
            <Text style={[styles.description, { color: theme.textSecondary }]}>
              {currentReminder.description}
            </Text>
          )}
        </View>

        {/* Reminder Details */}
        <View style={[styles.card, { backgroundColor: theme.cardBackground }]}>
          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Bell size={20} color={theme.primary} />
            </View>
            <View style={styles.detailContent}>
              <Text style={[styles.detailLabel, { color: theme.textSecondary }]}>
                {t.type}
              </Text>
              <Text style={[styles.detailValue, { color: theme.text }]}>
                {getReminderTypeText()}
              </Text>
            </View>
          </View>

          {currentReminder.reminderType === 'yearly_tithi' && currentReminder.odiaMonth && (
            <View style={styles.detailRow}>
              <View style={styles.detailIcon}>
                <Calendar size={20} color={theme.primary} />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, { color: theme.textSecondary }]}>
                  {t.reminderMonth}
                </Text>
                <Text style={[styles.detailValue, { color: theme.text }]}>
                  {currentReminder.odiaMonth}
                </Text>
              </View>
            </View>
          )}

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Calendar size={20} color={theme.primary} />
            </View>
            <View style={styles.detailContent}>
              <Text style={[styles.detailLabel, { color: theme.textSecondary }]}>
                {t.reminderPaksha}
              </Text>
              <Text style={[styles.detailValue, { color: theme.text }]}>
                {currentReminder.paksha}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Calendar size={20} color={theme.primary} />
            </View>
            <View style={styles.detailContent}>
              <Text style={[styles.detailLabel, { color: theme.textSecondary }]}>
                {t.reminderTithi}
              </Text>
              <Text style={[styles.detailValue, { color: theme.text }]}>
                {currentReminder.tithi}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Clock size={20} color={theme.primary} />
            </View>
            <View style={styles.detailContent}>
              <Text style={[styles.detailLabel, { color: theme.textSecondary }]}>
                {t.notificationTime}
              </Text>
              <Text style={[styles.detailValue, { color: theme.text }]}>
                {formatTime(currentReminder.notificationTime)}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Bell size={20} color={theme.primary} />
            </View>
            <View style={styles.detailContent}>
              <Text style={[styles.detailLabel, { color: theme.textSecondary }]}>
                {t.recurring}
              </Text>
              <Text style={[styles.detailValue, { color: theme.text }]}>
                {currentReminder.isRecurring
                  ? t.yes
                  : t.no}
              </Text>
            </View>
          </View>
        </View>

        {/* Scheduled Notifications */}
        <View style={[styles.card, { backgroundColor: theme.cardBackground }]}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>
            {language === 'or' ? 'ନିର୍ଧାରିତ ସୂଚନା' : 'Scheduled Notifications'}
          </Text>

          {notificationsLoading ? (
            <ActivityIndicator size="small" color={theme.primary} style={styles.loader} />
          ) : scheduledNotifications.length > 0 ? (
            <View style={styles.notificationsContainer}>
              {scheduledNotifications.slice(0, 5).map((notification) => (
                <View key={notification.id} style={styles.notificationItem}>
                  <View style={styles.notificationDate}>
                    <Text style={[styles.occurrenceDate, { color: theme.text }]}>
                      {formatDate(new Date(notification.localDate))}
                    </Text>
                    <Text style={[styles.occurrenceDetails, { color: theme.textSecondary }]}>
                      {notification.odiaMonth}, {notification.paksha} {notification.tithi}
                    </Text>
                  </View>
                  <View style={styles.notificationStatus}>
                    <Bell size={16} color={theme.primary} />
                  </View>
                </View>
              ))}
              {scheduledNotifications.length > 5 && (
                <Text style={[styles.moreNotifications, { color: theme.textSecondary }]}>
                  {language === 'or' ? `ଆଉ ${scheduledNotifications.length - 5}ଟି...` : `+${scheduledNotifications.length - 5} more...`}
                </Text>
              )}
            </View>
          ) : isCheckingDataAvailability ? (
            <View style={styles.noOccurrenceContainer}>
              <ActivityIndicator size="small" color={theme.primary} style={styles.loaderSmall} />
              <View style={styles.noOccurrenceTextContainer}>
                <Text style={[styles.noOccurrenceText, { color: theme.textSecondary }]}>
                  {t.checkingDataAvailability}
                </Text>
              </View>
            </View>
          ) : dataAvailability && dataAvailability.combinationExists ? (
            // The combination exists but no occurrences found - likely due to missing data
            <View style={styles.dataAvailabilityContainer}>
              <View style={styles.noOccurrenceContainer}>
                <AlertCircle size={24} color={theme.warning} />
                <View style={styles.noOccurrenceTextContainer}>
                  <Text style={[styles.noOccurrenceText, { color: theme.warning }]}>
                    {t.noOccurrenceFound}
                  </Text>
                  <Text style={[styles.noOccurrenceExplanation, { color: theme.textSecondary }]}>
                    {t.dataNotAvailableExplanation}
                  </Text>
                </View>
              </View>

              {/* Show download button if next year data is available for download */}
              {dataAvailability.nextYearDownloadable && (
                <TouchableOpacity
                  style={[styles.downloadButton, { backgroundColor: theme.primary }]}
                  onPress={handleDownload}
                  disabled={isLoadingYearStatus}
                >
                  {isLoadingYearStatus ? (
                    <ActivityIndicator size="small" color={theme.buttonText} />
                  ) : (
                    <>
                      <Download size={16} color={theme.buttonText} style={styles.downloadButtonIcon} />
                      <Text style={[styles.downloadButtonText, { color: theme.buttonText }]}>
                        {t.downloadNextYearData.replace('{year}', nextYear.toString())}
                      </Text>
                    </>
                  )}
                </TouchableOpacity>
              )}
            </View>
          ) : (
            // The combination doesn't exist or we couldn't determine data availability
            <View style={styles.noOccurrenceContainer}>
              <AlertCircle size={24} color={theme.warning} />
              <View style={styles.noOccurrenceTextContainer}>
                <Text style={[styles.noOccurrenceText, { color: theme.warning }]}>
                  {t.noOccurrenceFound}
                </Text>
                <Text style={[styles.noOccurrenceExplanation, { color: theme.textSecondary }]}>
                  {t.noOccurrenceFoundExplanation}
                </Text>
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  detailIcon: {
    width: 24,
    marginRight: 16,
    alignItems: 'center',
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  loader: {
    marginVertical: 16,
  },
  loaderSmall: {
    marginRight: 8,
  },
  occurrenceContainer: {
    marginTop: 8,
  },
  occurrenceDate: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  occurrenceDetails: {
    fontSize: 16,
  },
  noOccurrenceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 8,
  },
  noOccurrenceTextContainer: {
    flex: 1,
    marginLeft: 8,
  },
  noOccurrenceText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  noOccurrenceExplanation: {
    fontSize: 14,
    lineHeight: 20,
  },
  dataAvailabilityContainer: {
    marginTop: 8,
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginTop: 16,
  },
  downloadButtonIcon: {
    marginRight: 8,
  },
  downloadButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // New notification styles
  notificationsContainer: {
    marginTop: 8,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginVertical: 4,
    backgroundColor: 'rgba(156, 39, 176, 0.05)',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#9C27B0',
  },
  notificationDate: {
    flex: 1,
  },
  notificationStatus: {
    marginLeft: 12,
  },
  moreNotifications: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
});
