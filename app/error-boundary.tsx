import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { createLogger } from '@/utils/logging-sentry';

interface Props {
  children: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

const IFRAME_ID = 'rork-web-preview';

const webTargetOrigins = [
  "http://localhost:3000",
  "https://rorkai.com",
  "https://rork.app",
];

// Create a logger for the error boundary
const logger = createLogger('ErrorBoundary');

function sendErrorToIframeParent(error: any, errorInfo?: any) {
  // Only run in web environment
  if (Platform.OS !== 'web') {
    return;
  }

  // Use a try-catch for the entire function to prevent any issues
  try {
    // Make sure window exists before trying to use it
    if (typeof window === 'undefined' || !window.parent) {
      return;
    }

    // Safe access to document.referrer
    const referrer = typeof document !== 'undefined' ? document.referrer : '';

    // Log using our logger
    logger.debug('Sending error to parent:', {
      error: error?.message || error?.toString(),
      componentStack: errorInfo?.componentStack,
      referrer: referrer
    });

    const errorMessage = {
      type: 'ERROR',
      error: {
        message: error?.message || error?.toString() || 'Unknown error',
        stack: error?.stack,
        componentStack: errorInfo?.componentStack,
        timestamp: new Date().toISOString(),
      },
      iframeId: IFRAME_ID,
    };

    try {
      // Safe access to window.parent
      if (window.parent && window.parent.postMessage) {
        window.parent.postMessage(
          errorMessage,
          webTargetOrigins.includes(referrer) ? referrer : '*'
        );
      }
    } catch (postMessageError) {
      // Log using our logger
      logger.error('Failed to send error to parent:', {
        error: typeof postMessageError === 'object' && postMessageError !== null && 'message' in postMessageError
          ? (postMessageError as Error).message
          : String(postMessageError)
      });
    }
  } catch (error) {
    // Silently fail - we don't want error handling to cause more errors
  }
}

// Only add web-specific error handlers in web environment
// This code will only run once during module initialization
// We need to be careful with conditional code here
if (Platform.OS === 'web') {
  // Use a try-catch to prevent any issues with window access
  try {
    // Make sure window exists before trying to use it
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('error', (event) => {
        try {
          event.preventDefault();
          const errorDetails = event.error ?? {
            message: event.message ?? 'Unknown error',
            filename: event.filename ?? 'Unknown file',
            lineno: event.lineno ?? 'Unknown line',
            colno: event.colno ?? 'Unknown column'
          };

          // Log using our logger
          logger.error('Global error caught', {
            message: errorDetails.message,
            filename: errorDetails.filename,
            lineno: errorDetails.lineno,
            colno: errorDetails.colno
          });

          sendErrorToIframeParent(errorDetails);
        } catch (e) {
          // Silently fail - we don't want error handling to cause more errors
        }
      }, true);

      window.addEventListener('unhandledrejection', (event) => {
        try {
          event.preventDefault();

          // Log using our logger
          logger.error('Unhandled promise rejection', {
            reason: event.reason?.message || String(event.reason)
          });

          sendErrorToIframeParent(event.reason);
        } catch (e) {
          // Silently fail - we don't want error handling to cause more errors
        }
      }, true);
    }
  } catch (error) {
    // Silently fail - this is just for web error handling
    if (__DEV__) {
      console.warn('Could not set up web error handlers:', error);
    }
  }
}

export class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log using our logger
    logger.error('Error caught by boundary', {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack
    });

    sendErrorToIframeParent(error, errorInfo);

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.container}>
          <View style={styles.content}>
            <Text style={styles.title}>Something went wrong</Text>
            <Text style={styles.subtitle}>{this.state.error?.message}</Text>
            {Platform.OS !== 'web' && (
              <Text style={styles.description}>
                Please check your device logs for more details.
              </Text>
            )}
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 36,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
});

export default ErrorBoundary;