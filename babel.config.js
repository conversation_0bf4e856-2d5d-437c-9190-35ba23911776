module.exports = function(api) {
  api.cache(true);

  // Determine if we're in production mode
  const isProd = process.env.NODE_ENV === 'production';

  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./src'],
          extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
          alias: {
            '@': './src',
          },
        },
      ],
      'react-native-reanimated/plugin',
      // Only remove console statements in production
      isProd && ['transform-remove-console', { exclude: ['error', 'warn'] }],
    ].filter(Boolean), // Filter out false values
  };
};
