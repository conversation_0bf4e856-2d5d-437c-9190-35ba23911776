const { getSentryExpoConfig } = require("@sentry/react-native/metro");
const { getDefaultConfig } = require("expo/metro-config");

// Get the Sentry configuration
const sentryConfig = getSentryExpoConfig(__dirname);

// Add tree shaking and console removal optimizations
const config = {
  ...sentryConfig,
  transformer: {
    ...sentryConfig.transformer,
    minifierConfig: {
      ...sentryConfig.transformer?.minifierConfig,
      compress: {
        ...sentryConfig.transformer?.minifierConfig?.compress,
        drop_console: true,  // Remove console statements
        unused: true,        // Remove unused variables
        dead_code: true,     // Remove unreachable code
      },
    },
  },
};

module.exports = config;
